#include "catch2/catch.hpp"
#include "progress_fitting/progress_fitting.h"
#include "logger/logger.h"
#include "ev_loop/eventloop_manager.h"
#include "dds_service_manager/duc_service_manager.h"

#include <thread>
#include <chrono>
#include <vector>
#include <iomanip>

using namespace seres::fotamaster;
using namespace seres::ota_duc_service;

class ProgressCalculationTestFixture
{
public:
    ProgressCalculationTestFixture()
    {
        // 初始化日志系统
        LogConfig config;
        config.console_output = true;
        config.log_level = LogLevel::kLogLevelInfo;
        base::Singleton<Logger>::Instance().Init(config);
        
        // 初始化进度拟合模块
        progressFitting.Initialize([this](uint8_t progress, bool allFinished) {
            lastProgress = progress;
            isAllFinished = allFinished;
            progressHistory.push_back({progress, allFinished});
            LOG_INFO("Progress Update: %d%%, All Finished: %s", 
                    progress, allFinished ? "Yes" : "No");
        }, 0.1); // 100ms间隔用于测试
    }
    
    ~ProgressCalculationTestFixture()
    {
        progressFitting.Stop();
    }

    // 创建测试设备
    std::vector<InventoryInfo> createTestDevices(const std::string& prefix, int count)
    {
        std::vector<InventoryInfo> devices;
        for (int i = 1; i <= count; ++i)
        {
            InventoryInfo device;
            device.ecuName(prefix + "_ECU_" + std::to_string(i));
            device.serialNumber(prefix + "_" + std::to_string(i));
            device.partNumber(prefix + "_PART_" + std::to_string(i));
            devices.push_back(device);
        }
        return devices;
    }

    // 模拟设备进度更新
    void simulateDeviceProgress(DUCType ducType, const std::vector<std::string>& deviceIds, 
                               const std::vector<uint8_t>& progresses)
    {
        UpdateProgress updateProgress;
        updateProgress.allFinished(false);
        
        std::vector<DeviceUpdateProgress> progressList;
        for (size_t i = 0; i < deviceIds.size() && i < progresses.size(); ++i)
        {
            DeviceUpdateProgress deviceProgress;
            deviceProgress.deviceId(deviceIds[i]);
            deviceProgress.deviceName("Device_" + deviceIds[i]);
            deviceProgress.progressPercent(progresses[i]);
            deviceProgress.status(progresses[i] >= 100 ? 
                                 DeviceUpdateStatus::SUCCESS : DeviceUpdateStatus::UPDATING);
            progressList.push_back(deviceProgress);
        }
        
        updateProgress.progressLists(progressList);
        
        // 直接调用进度更新函数（模拟DDS回调）
        progressFitting.onUpdateProgress(ducType, updateProgress);
    }

    void printProgressSummary()
    {
        LOG_INFO("=== Progress Calculation Test Summary ===");
        LOG_INFO("Total progress updates: %zu", progressHistory.size());
        for (size_t i = 0; i < progressHistory.size(); ++i)
        {
            LOG_INFO("Update %zu: %d%%, Finished: %s", 
                    i + 1, progressHistory[i].first, 
                    progressHistory[i].second ? "Yes" : "No");
        }
        LOG_INFO("Final Progress: %d%%", lastProgress);
        LOG_INFO("All Finished: %s", isAllFinished ? "Yes" : "No");
    }

public:
    ProgressFitting progressFitting;
    uint8_t lastProgress = 0;
    bool isAllFinished = false;
    std::vector<std::pair<uint8_t, bool>> progressHistory;
};

TEST_CASE_METHOD(ProgressCalculationTestFixture, "Progress Calculation - Equal Weights", "[progress_calculation]")
{
    SECTION("Single Domain with Multiple Devices")
    {
        // 测试单个域控多个设备的进度计算
        auto cdcDevices = createTestDevices("CDC", 3);
        REQUIRE(progressFitting.AddUpdateDevice(DUCType::CDC, cdcDevices) == true);
        
        progressFitting.Start();
        
        // 模拟设备进度：0%, 50%, 100%，期望域控进度为50%
        simulateDeviceProgress(DUCType::CDC, {"CDC_1", "CDC_2", "CDC_3"}, {0, 50, 100});
        
        // 等待计算
        std::this_thread::sleep_for(std::chrono::milliseconds(200));
        
        uint8_t currentProgress = progressFitting.GetCurrentProgress();
        LOG_INFO("Single domain progress: %d%% (expected around 50%%)", currentProgress);
        
        // 由于只有一个域控，总进度应该等于域控进度
        // 设备进度平均值：(0 + 50 + 100) / 3 = 50%
        REQUIRE(currentProgress >= 45);  // 允许一些误差
        REQUIRE(currentProgress <= 55);
    }

    SECTION("Multiple Domains with Equal Weights")
    {
        // 测试多个域控的权重分配
        auto cdcDevices = createTestDevices("CDC", 2);  // 2个CDC设备
        auto mdcDevices = createTestDevices("MDC", 3);  // 3个MDC设备
        auto zcuDevices = createTestDevices("ZCU", 1);  // 1个ZCU设备
        
        REQUIRE(progressFitting.AddUpdateDevice(DUCType::CDC, cdcDevices) == true);
        REQUIRE(progressFitting.AddUpdateDevice(DUCType::MDC, mdcDevices) == true);
        REQUIRE(progressFitting.AddUpdateDevice(DUCType::ZCU, zcuDevices) == true);
        
        progressFitting.Start();
        
        // 模拟各域控的设备进度
        // CDC: 0%, 100% -> 域控进度 50%
        simulateDeviceProgress(DUCType::CDC, {"CDC_1", "CDC_2"}, {0, 100});
        
        // MDC: 30%, 60%, 90% -> 域控进度 60%
        simulateDeviceProgress(DUCType::MDC, {"MDC_1", "MDC_2", "MDC_3"}, {30, 60, 90});
        
        // ZCU: 80% -> 域控进度 80%
        simulateDeviceProgress(DUCType::ZCU, {"ZCU_1"}, {80});
        
        // 等待计算
        std::this_thread::sleep_for(std::chrono::milliseconds(200));
        
        uint8_t currentProgress = progressFitting.GetCurrentProgress();
        
        // 总进度计算：(50% + 60% + 80%) / 3 = 63.33%
        LOG_INFO("Multiple domains progress: %d%% (expected around 63%%)", currentProgress);
        
        REQUIRE(currentProgress >= 60);
        REQUIRE(currentProgress <= 67);
    }
}

TEST_CASE_METHOD(ProgressCalculationTestFixture, "Progress Calculation - Edge Cases", "[progress_calculation]")
{
    SECTION("All Devices at 0%")
    {
        auto devices = createTestDevices("TEST", 3);
        REQUIRE(progressFitting.AddUpdateDevice(DUCType::CDC, devices) == true);
        
        progressFitting.Start();
        
        // 所有设备进度为0%
        simulateDeviceProgress(DUCType::CDC, {"TEST_1", "TEST_2", "TEST_3"}, {0, 0, 0});
        
        std::this_thread::sleep_for(std::chrono::milliseconds(200));
        
        uint8_t currentProgress = progressFitting.GetCurrentProgress();
        LOG_INFO("All devices 0%% progress: %d%%", currentProgress);
        
        REQUIRE(currentProgress == 0);
    }

    SECTION("All Devices at 100%")
    {
        auto devices = createTestDevices("TEST", 3);
        REQUIRE(progressFitting.AddUpdateDevice(DUCType::CDC, devices) == true);
        
        progressFitting.Start();
        
        // 所有设备进度为100%
        simulateDeviceProgress(DUCType::CDC, {"TEST_1", "TEST_2", "TEST_3"}, {100, 100, 100});
        
        std::this_thread::sleep_for(std::chrono::milliseconds(200));
        
        uint8_t currentProgress = progressFitting.GetCurrentProgress();
        LOG_INFO("All devices 100%% progress: %d%%", currentProgress);
        
        REQUIRE(currentProgress == 100);
    }

    SECTION("Progressive Update Simulation")
    {
        // 模拟渐进式更新过程
        auto devices = createTestDevices("PROG", 2);
        REQUIRE(progressFitting.AddUpdateDevice(DUCType::CDC, devices) == true);
        
        progressFitting.Start();
        
        // 模拟渐进式更新
        std::vector<std::vector<uint8_t>> progressSteps = {
            {0, 0},      // 初始状态
            {25, 10},    // 第一步
            {50, 30},    // 第二步
            {75, 60},    // 第三步
            {100, 90},   // 第四步
            {100, 100}   // 完成
        };
        
        for (size_t step = 0; step < progressSteps.size(); ++step)
        {
            simulateDeviceProgress(DUCType::CDC, {"PROG_1", "PROG_2"}, progressSteps[step]);
            std::this_thread::sleep_for(std::chrono::milliseconds(150));
            
            uint8_t currentProgress = progressFitting.GetCurrentProgress();
            uint8_t expectedProgress = (progressSteps[step][0] + progressSteps[step][1]) / 2;
            
            LOG_INFO("Step %zu: Device1=%d%%, Device2=%d%%, Overall=%d%%, Expected=%d%%",
                    step + 1, progressSteps[step][0], progressSteps[step][1], 
                    currentProgress, expectedProgress);
        }
        
        printProgressSummary();
    }
}
