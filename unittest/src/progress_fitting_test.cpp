#include "catch2/catch.hpp"
#include "progress_fitting/progress_fitting.h"
#include "logger/logger.h"
#include "ev_loop/eventloop_manager.h"
#include "dds_service_manager/duc_service_manager.h"

#include <thread>
#include <chrono>

using namespace seres::fotamaster;
using namespace seres::ota_duc_service;

class ProgressFittingTestFixture
{
public:
    ProgressFittingTestFixture()
    {
        // 初始化日志系统
        LogConfig config;
        config.console_output = true;
        config.log_level = LogLevel::kLogLevelDebug;
        base::Singleton<Logger>::Instance().Init(config);
        
        // 创建测试设备列表
        createTestDevices();
    }
    
    ~ProgressFittingTestFixture()
    {
        progressFitting.Stop();
    }

private:
    void createTestDevices()
    {
        // 创建CDC域的设备
        InventoryInfo cdcDevice1;
        cdcDevice1.ecuName("CDC_ECU_1");
        cdcDevice1.serialNumber("CDC001");
        cdcDevice1.partNumber("CDC-PART-001");
        testDevices.push_back(cdcDevice1);

        InventoryInfo cdcDevice2;
        cdcDevice2.ecuName("CDC_ECU_2");
        cdcDevice2.serialNumber("CDC002");
        cdcDevice2.partNumber("CDC-PART-002");
        testDevices.push_back(cdcDevice2);

        // 创建MDC域的设备
        InventoryInfo mdcDevice1;
        mdcDevice1.ecuName("MDC_ADAS_1");
        mdcDevice1.serialNumber("MDC001");
        mdcDevice1.partNumber("MDC-PART-001");
        testDevices.push_back(mdcDevice1);

        // 创建ZCU域的设备
        InventoryInfo zcuDevice1;
        zcuDevice1.ecuName("ZCU_Gateway");
        zcuDevice1.serialNumber("ZCU001");
        zcuDevice1.partNumber("ZCU-PART-001");
        testDevices.push_back(zcuDevice1);
    }

public:
    ProgressFitting progressFitting;
    std::vector<InventoryInfo> testDevices;
    std::vector<std::pair<uint8_t, bool>> progressHistory;
    std::mutex historyMutex;
};

TEST_CASE_METHOD(ProgressFittingTestFixture, "ProgressFitting Basic Functionality", "[progress_fitting]")
{
    SECTION("Initialization")
    {
        bool result = progressFitting.Initialize(
            testDevices,
            [this](uint8_t progress, bool allFinished) {
                std::lock_guard<std::mutex> lock(historyMutex);
                progressHistory.emplace_back(progress, allFinished);
                LOG_INFO("Progress callback: %d%%, finished: %s", 
                        progress, allFinished ? "true" : "false");
            },
            0.5 // 0.5秒报告间隔
        );
        
        REQUIRE(result == true);
        REQUIRE(progressFitting.GetCurrentProgress() == 0);
        REQUIRE(progressFitting.IsAllFinished() == false);
    }

    SECTION("Start and Stop")
    {
        progressFitting.Initialize(
            testDevices,
            [this](uint8_t progress, bool allFinished) {
                std::lock_guard<std::mutex> lock(historyMutex);
                progressHistory.emplace_back(progress, allFinished);
            }
        );
        
        REQUIRE(progressFitting.Start() == true);
        
        // 等待一段时间让定时器运行
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        
        progressFitting.Stop();
        
        // 检查是否有进度回调
        std::lock_guard<std::mutex> lock(historyMutex);
        REQUIRE(progressHistory.size() >= 0); // 可能还没有进度更新
    }

    SECTION("Smoothing Factor")
    {
        progressFitting.Initialize(testDevices, [](uint8_t, bool){});
        
        progressFitting.SetSmoothingFactor(0.5);
        // 应该不会抛出异常
        
        progressFitting.SetSmoothingFactor(-0.1); // 无效值，应该使用默认值
        progressFitting.SetSmoothingFactor(1.5);  // 无效值，应该使用默认值
    }

    SECTION("Reset Functionality")
    {
        progressFitting.Initialize(testDevices, [](uint8_t, bool){});
        progressFitting.Start();
        
        // 重置进度
        progressFitting.Reset();
        
        REQUIRE(progressFitting.GetCurrentProgress() == 0);
        REQUIRE(progressFitting.IsAllFinished() == false);
    }
}

TEST_CASE_METHOD(ProgressFittingTestFixture, "ProgressFitting Error Handling", "[progress_fitting]")
{
    SECTION("Empty device list")
    {
        std::vector<InventoryInfo> emptyDevices;
        bool result = progressFitting.Initialize(
            emptyDevices,
            [](uint8_t, bool){}
        );
        
        REQUIRE(result == false);
    }

    SECTION("Null callback")
    {
        bool result = progressFitting.Initialize(
            testDevices,
            nullptr
        );
        
        REQUIRE(result == false);
    }

    SECTION("Invalid report interval")
    {
        bool result = progressFitting.Initialize(
            testDevices,
            [](uint8_t, bool){},
            -1.0 // 无效的报告间隔
        );
        
        REQUIRE(result == false);
    }

    SECTION("Start without initialization")
    {
        bool result = progressFitting.Start();
        REQUIRE(result == false);
    }
}

// 模拟进度更新的测试
TEST_CASE_METHOD(ProgressFittingTestFixture, "ProgressFitting Simulation", "[progress_fitting][simulation]")
{
    SECTION("Simulated Progress Updates")
    {
        progressFitting.Initialize(
            testDevices,
            [this](uint8_t progress, bool allFinished) {
                std::lock_guard<std::mutex> lock(historyMutex);
                progressHistory.emplace_back(progress, allFinished);
                LOG_INFO("Progress: %d%%, All finished: %s", 
                        progress, allFinished ? "true" : "false");
            },
            0.2 // 200ms间隔
        );
        
        REQUIRE(progressFitting.Start() == true);
        
        // 运行一段时间观察进度变化
        std::this_thread::sleep_for(std::chrono::seconds(2));
        
        progressFitting.Stop();
        
        // 检查进度历史
        std::lock_guard<std::mutex> lock(historyMutex);
        LOG_INFO("Total progress updates: %zu", progressHistory.size());
        
        for (size_t i = 0; i < progressHistory.size(); ++i)
        {
            LOG_INFO("Update %zu: %d%%, finished: %s", 
                    i, progressHistory[i].first, 
                    progressHistory[i].second ? "true" : "false");
        }
    }
}
