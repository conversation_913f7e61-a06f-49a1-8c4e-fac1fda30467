#
# Copyright(c) 2020 to 2022 ZettaScale Technology and others
#
# This program and the accompanying materials are made available under the
# terms of the Eclipse Public License v. 2.0 which is available at
# http://www.eclipse.org/legal/epl-2.0, or the Eclipse Distribution License
# v. 1.0 which is available at
# http://www.eclipse.org/org/documents/edl-v10.php.
#
# SPDX-License-Identifier: EPL-2.0 OR BSD-3-Clause
#
cmake_minimum_required(VERSION 3.16)

if (CMAKE_BINARY_DIR STREQUAL CMAKE_CURRENT_SOURCE_DIR)
  message(FATAL_ERROR "Building in-source is not supported. "
                      "Create a build dir and remove CMakeFiles and CMakeCache.txt")
endif()

message("cmake version: ${CMAKE_MAJOR_VERSION}.${CMAKE_MINOR_VERSION}")
if(${CMAKE_VERSION} VERSION_LESS 3.16)
    cmake_policy(VERSION ${CMAKE_MAJOR_VERSION}.${CMAKE_MINOR_VERSION})
else()
    cmake_policy(VERSION 3.16)
endif()

project(ota_unittest)

set(CMAKE_CXX_STANDARD 17)

list(APPEND CMAKE_MODULE_PATH "${CMAKE_CURRENT_LIST_DIR}/../cmake")
list(APPEND CMAKE_MODULE_PATH "${CMAKE_CURRENT_LIST_DIR}/../cdds_install/x64/lib/cmake")

find_package(Threads REQUIRED)
find_package(spdlog REQUIRED)
find_package(libev REQUIRED)

include(GNUInstallDirs)
include(CheckAndAddFiles)


#################################logger test#################################
set(LOGGER_TEST logger_test)
check_and_add_files(${LOGGER_TEST}_SRC
  "src/"
    logger_test.cpp
)

check_and_add_files(${LOGGER_TEST}_other_SRC
  "../src/logger"
    logger_config.cpp
    logger.cpp
)

add_executable(${LOGGER_TEST} ${${LOGGER_TEST}_SRC} ${${LOGGER_TEST}_other_SRC})
# 传递变量, 使用target_compile_definitions
target_compile_definitions(${LOGGER_TEST} PRIVATE LOGGER_CFG_FILE_PATH_PERFIX="${CMAKE_INSTALL_PREFIX}/cfg/fotamaster")
target_include_directories(${LOGGER_TEST}
  PRIVATE
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/../include>
    $<INSTALL_INTERFACE:${CMAKE_INSTALL_INCLUDEDIR}>
)
target_link_libraries(${LOGGER_TEST} PRIVATE Threads::Threads spdlog::spdlog)
install(TARGETS ${LOGGER_TEST}
    RUNTIME DESTINATION ${CMAKE_INSTALL_PREFIX}/bin)

#################################ev_loop test#################################
set(EV_LOOP_TEST ev_loop_test)
check_and_add_files(${EV_LOOP_TEST}_SRC
  "src/"
    ev_loop_test.cpp
)

check_and_add_files(${EV_LOOP_TEST}_other_SRC
  "../src/ev_loop"
    eventloop.cpp
    eventloop_manager.cpp
    timeout_handler.cpp
    timer.cpp
    signal_handler.cpp
    signal_manager.cpp
)

add_executable(${EV_LOOP_TEST} ${${EV_LOOP_TEST}_SRC} ${${EV_LOOP_TEST}_other_SRC})
target_include_directories(${EV_LOOP_TEST}
  PRIVATE
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/../include>
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/../../cdds_install/x64/include>
    $<INSTALL_INTERFACE:${CMAKE_INSTALL_INCLUDEDIR}>
)
target_link_libraries(${EV_LOOP_TEST} PRIVATE Threads::Threads libev::libev)
install(TARGETS ${EV_LOOP_TEST}
    RUNTIME DESTINATION ${CMAKE_INSTALL_PREFIX}/bin)

#################################duc_service_test test#################################

if(NOT TARGET CycloneDDS-CXX::ddscxx)
  find_package(CycloneDDS-CXX REQUIRED)
endif()

idlcxx_generate(TARGET ota_service
  FILES
    ../gen/rpcCommon.idl
    ../gen/OTA_DucInterface.idl
    ../gen/OTA_DucData.idl
    ../gen/OTA_VucServiceData.idl
  WARNINGS
    no-implicit-extensibility)

add_executable(ddsserver
  src/duc_service_test.cpp
  ../gen/rpc/OTA_DucInterface_gen_server.cpp)

# target_include_directories(ddsclient PRIVATE ./gen ../include ../src)
target_include_directories(ddsserver PRIVATE
  $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/../include>
  $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/../gen>
  $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/../../cdds_install/x64/include/rpcddscxx>
  $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/../../cdds_install/x64/include>
  $<INSTALL_INTERFACE:${CMAKE_INSTALL_INCLUDEDIR}>)

target_link_directories(ddsserver PRIVATE ${CMAKE_CURRENT_SOURCE_DIR}/../../cdds_install/x64/lib)

target_link_libraries(ddsserver
  PRIVATE
    Threads::Threads
    CycloneDDS-CXX::ddscxx
    rpcddscxx
    libev::libev
    spdlog::spdlog
    ota_service
)
set_property(TARGET ddsserver PROPERTY CXX_STANDARD ${cyclonedds_cpp_std_to_use})

#################################duc_service_client test#################################

add_executable(duc_service_manager_test
  src/duc_service_manager_test.cpp
  ../src/dds_service_manager/duc_service_client.cpp
  ../src/dds_service_manager/duc_service_manager.cpp
  ../gen/rpc/OTA_DucInterface_gen_client.cpp
  ../src/logger/logger_config.cpp
  ../src/logger/logger.cpp)

target_include_directories(duc_service_manager_test PRIVATE
  $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/../include>
  $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/../gen>
  $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/../../cdds_install/x64/include/rpcddscxx>
  $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/../../cdds_install/x64/include>
  $<INSTALL_INTERFACE:${CMAKE_INSTALL_INCLUDEDIR}>)

target_link_directories(duc_service_manager_test PRIVATE ${CMAKE_CURRENT_SOURCE_DIR}/../../cdds_install/x64/lib)

target_link_libraries(duc_service_manager_test
  PRIVATE
    Threads::Threads
    CycloneDDS-CXX::ddscxx
    rpcddscxx
    libev::libev
    spdlog::spdlog
    ota_service
)
set_property(TARGET duc_service_manager_test PROPERTY CXX_STANDARD ${cyclonedds_cpp_std_to_use})


#################################pub_test test#################################
set(PUB_TEST pub_test)
check_and_add_files(${PUB_TEST}_SRC
  "src/"
    pub_test.cpp
)

check_and_add_files(${PUB_TEST}_logger_SRC
  "../src/logger"
    logger_config.cpp
    logger.cpp
)

# check_and_add_files(${PUB_TEST}_gen_SRC
#   "../gen"
#     OTA_VucService.cpp
# )

add_executable(${PUB_TEST} ${${PUB_TEST}_SRC} ${${PUB_TEST}_logger_SRC})
target_include_directories(${PUB_TEST}
  PRIVATE
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/../include>
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/../gen>
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/../../cdds_install/x64/include>
    $<INSTALL_INTERFACE:${CMAKE_INSTALL_INCLUDEDIR}>
)
target_link_libraries(${PUB_TEST} PRIVATE Threads::Threads CycloneDDS-CXX::ddscxx spdlog::spdlog ota_service)
install(TARGETS ${PUB_TEST}
    RUNTIME DESTINATION ${CMAKE_INSTALL_PREFIX}/bin)


#################################progress test#################################
check_and_add_files(event_SRC
  "../src/ev_loop"
    eventloop.cpp
    eventloop_manager.cpp
    timeout_handler.cpp
    timer.cpp
    signal_handler.cpp
    signal_manager.cpp
)

add_executable(progress_fitting_test
  src/progress_fitting_manual_test.cpp
  ../src/progress_fitting/progress_fitting.cpp
  ../src/logger/logger_config.cpp
  ../src/logger/logger.cpp
  event_SRC)

target_include_directories(duc_service_manager_test PRIVATE
  $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/../include>
  $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/../gen>
  $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/../../cdds_install/x64/include/rpcddscxx>
  $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/../../cdds_install/x64/include>
  $<INSTALL_INTERFACE:${CMAKE_INSTALL_INCLUDEDIR}>)

target_link_directories(duc_service_manager_test PRIVATE ${CMAKE_CURRENT_SOURCE_DIR}/../../cdds_install/x64/lib)

target_link_libraries(progress_fitting_test
  PRIVATE
    Threads::Threads
    CycloneDDS-CXX::ddscxx
    rpcddscxx
    libev::libev
    spdlog::spdlog
    ota_service
)
set_property(TARGET duc_service_manager_test PROPERTY CXX_STANDARD ${cyclonedds_cpp_std_to_use})