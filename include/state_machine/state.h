#pragma once

#include "ev_loop/eventloop.h"
#include <any>
#include <memory>
#include <string>
#include <unordered_map>
#include <cassert>
#include "logger/logger.h"
#include "base/classfactory.h"
#include "base/singleton.h"


namespace seres
{
namespace fotamaster
{

struct EventData
{
    int type;
    std::any data;
};

class StateMachine;
class OtaStateMachine;

class State
{
public:
    State() = default;
    virtual ~State()
    {
    }

    virtual void Init(EventLoop *event_loop)
    {
        event_loop_ = event_loop;
    }
    virtual bool Enter()
    {
        return true;
    }

    virtual void Exit()
    {
    }

    virtual void Process(const EventData &data)
    {
        (void)data;
    }

    void SetStateMachine(OtaStateMachine *sm)
    {
        state_machine_ = sm;
    }

protected:
    OtaStateMachine *GetStateMachine() const
    {
        return state_machine_;
    }

    EventLoop *GetEventLoop() const
    {
        return event_loop_;
    }

private:
    OtaStateMachine *state_machine_{nullptr};
    EventLoop *event_loop_{nullptr};
};

#define REGISTER_STATE(Name, Type) REGISTER_CLASS(Name, Type, State)

class StateMachine
{
public:
    StateMachine(EventLoop *event_loop) : event_loop_(event_loop)
    {
    }
    virtual ~StateMachine()
    {
        states_.clear();
    }

protected:
    void AddState(const std::string &state_name, OtaStateMachine *sm)
    {
        if (states_.count(state_name) != 0)
        {
            LOG_DEBUG("State: %s is registered", state_name.c_str());
            return;
        }

        State *state = base::Singleton<base::ClassFactoryManager>::Instance()
                            .CreateObject<State>(state_name);
        assert(state != nullptr);
        state->SetStateMachine(sm);
        state->Init(event_loop_);
        states_.emplace(state_name, std::shared_ptr<State>(state));

    }

    virtual void ChangeState(const std::string &new_state)
    {
        if (!current_state_.empty())
        {
            GetState(current_state_)->Exit();
        }

        LOG_INFO("##############ChangeState = %s", new_state.c_str());
        current_state_ = new_state;

        if (!GetState(current_state_)->Enter())
        {
            LOG_ERROR("Enter state %s  failed", current_state_.c_str());
        }
    }

    std::shared_ptr<State> GetState(const std::string &state_name)
    {
        assert(states_.count(state_name) != 0);

        return states_[state_name];
    }

    const std::string GetCurrentState() const
    {
        return current_state_;
    }

private:
    std::unordered_map<std::string, std::shared_ptr<State>> states_;
    EventLoop *event_loop_{nullptr};
    std::string current_state_{};
};

} // namespace fotamaster
} // namespace seres