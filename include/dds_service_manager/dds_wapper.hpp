#pragma once
#include "base/noncopyable.h"
#include "dds/dds.hpp"
#include <functional>
#include <memory>
#include <mutex>
#include <optional>
#include <string>
#include <type_traits>
#include <unordered_map>

namespace seres
{
namespace fotamaster
{
namespace dds_wrapper
{

template <typename T>
struct DdsResult
{
    static_assert(!std::is_same_v<T, void>,
                  "DdsResult<T> cannot use T = void. Use DdsResult<void> "
                  "specialization.");

    std::optional<T> value;
    std::string error_msg;

    static DdsResult Success(T val)
    {
        DdsResult res;
        res.value = std::move(val);
        return res;
    }

    static DdsResult Failure(std::string &&msg)
    {
        DdsResult res;
        res.error_msg = std::move(msg);
        return res;
    }

    explicit operator bool() const
    {
        return value.has_value();
    }
    T &GetValue()
    {
        return *value;
    }
    const T &GetValue() const
    {
        return *value;
    }
};

template <>
struct DdsResult<void>
{
    bool has_value{false};
    std::string error_msg;

    static DdsResult Success()
    {
        DdsResult res;
        res.has_value = true;
        return res;
    }

    static DdsResult Failure(std::string &&msg)
    {
        DdsResult res;
        res.has_value = false;
        res.error_msg = std::move(msg);
        return res;
    }

    explicit operator bool() const
    {
        return has_value;
    }
};

struct DdsQosConfig
{
    bool use_qos{false};
    dds::core::Duration deadline{dds::core::Duration::infinite()};
    dds::core::Duration liveliness_lease_duration{
        dds::core::Duration::infinite()};
    dds::core::policy::ReliabilityKind reliability{
        dds::core::policy::ReliabilityKind::RELIABLE};
    dds::core::policy::DurabilityKind durability{
        dds::core::policy::DurabilityKind::VOLATILE};
    dds::core::policy::HistoryKind history{
        dds::core::policy::HistoryKind::KEEP_LAST};
    int32_t history_depth{1};
};

struct ITopicEntry
{
    virtual ~ITopicEntry() = default;
    virtual void Close() = 0;
};

class Subscriber : base::Noncopyable
{
public:
    template <typename MsgT>
    using DataAvailableCallback = std::function<void(const MsgT &)>;

    template <typename MsgT>
    using IncompatibleQosCallback = std::function<void(
        const dds::core::status::RequestedIncompatibleQosStatus &)>;

    static DdsResult<std::shared_ptr<Subscriber>> Create(uint32_t domain_id)
    {
        try
        {
            // 先创建participant和subscriber对象
            dds::domain::DomainParticipant participant(domain_id);
            dds::sub::Subscriber subscriber(participant);

            // 使用私有构造函数创建Subscriber实例
            // 使用new构造实例，shared_ptr接管所有权
            auto *raw_sub =
                new Subscriber(std::move(participant), std::move(subscriber));
            auto sub = std::shared_ptr<Subscriber>(raw_sub);
            // auto sub = std::make_shared<Subscriber>(std::move(participant), std::move(subscriber));
            return DdsResult<std::shared_ptr<Subscriber>>::Success(sub);
        }
        catch (const dds::core::Exception &e)
        {
            return DdsResult<std::shared_ptr<Subscriber>>::Failure(
                "DDS exception: " + std::string(e.what()));
        }
        catch (const std::exception &e)
        {
            return DdsResult<std::shared_ptr<Subscriber>>::Failure(
                "Standard exception: " + std::string(e.what()));
        }
    }

    template <typename MsgT>
    DdsResult<bool> Subscribe(
        const std::string &topic_name,
        DataAvailableCallback<MsgT> &&data_callback,
        IncompatibleQosCallback<MsgT> &&qos_callback = nullptr,
        const DdsQosConfig &qos_config = DdsQosConfig{})
    {
        {
            std::lock_guard<std::mutex> lock{topics_mutex_};
            if (topics_.count(topic_name))
            {
                return DdsResult<bool>::Failure("Topic already subscribed");
            }
        }

        try
        {
            // 配置QoS
            auto qos = subscriber_.default_datareader_qos();
            ApplyQosConfig(qos, qos_config);

            // 创建监听器
            auto listener = std::make_unique<DataReaderListener<MsgT>>(
                std::move(data_callback),
                std::move(qos_callback));

            auto entry =
                std::make_unique<TopicEntry<MsgT>>(participant_,
                                                   topic_name,
                                                   subscriber_,
                                                   qos,
                                                   std::move(listener));

            {
                std::lock_guard<std::mutex> lock{topics_mutex_};
                topics_.emplace(topic_name, std::move(entry));
            }

            return DdsResult<bool>::Success(true);
        }
        catch (const dds::core::Exception &e)
        {
            return DdsResult<bool>::Failure("DDS exception: " +
                                            std::string(e.what()));
        }
        catch (const std::exception &e)
        {
            return DdsResult<bool>::Failure("Standard exception: " +
                                            std::string(e.what()));
        }
    }

    ~Subscriber()
    {
        std::lock_guard<std::mutex> lock{topics_mutex_};
        for (auto &[name, entry] : topics_)
        {
            entry->Close();
        }
        topics_.clear();
    }

    // // 禁用拷贝和赋值
    // Subscriber(const Subscriber &) = delete;
    // Subscriber &operator=(const Subscriber &) = delete;

private:
    // 私有构造函数，接收已构造的DDS对象
    Subscriber(dds::domain::DomainParticipant &&participant,
               dds::sub::Subscriber &&subscriber)
        : participant_(std::move(participant)),
          subscriber_(std::move(subscriber))
    {
    }

private:
    template <typename MsgT>
    class DataReaderListener : public dds::sub::NoOpDataReaderListener<MsgT>
    {
    public:
        DataReaderListener(DataAvailableCallback<MsgT> data_cb,
                           IncompatibleQosCallback<MsgT> qos_cb)
            : data_cb_(std::move(data_cb)), qos_cb_(std::move(qos_cb))
        {
        }

        bool HasQosCallback() const
        {
            return static_cast<bool>(qos_cb_);
        }

        void on_data_available(dds::sub::DataReader<MsgT> &reader) override
        {
            dds::sub::LoanedSamples<MsgT> samples = reader.take();
            for (const auto &sample : samples)
            {
                if (sample.info().valid() && data_cb_)
                {
                    data_cb_(sample.data());
                }
            }
        }

        void on_requested_incompatible_qos(
            dds::sub::DataReader<MsgT> &reader,
            const dds::core::status::RequestedIncompatibleQosStatus &status)
            override
        {
            (void)reader;
            if (qos_cb_)
            {
                qos_cb_(status);
            }
        }

    private:
        DataAvailableCallback<MsgT> data_cb_;
        IncompatibleQosCallback<MsgT> qos_cb_;
    };

    template <typename MsgT>
    struct TopicEntry : ITopicEntry
    {
        dds::topic::Topic<MsgT> topic;
        dds::sub::DataReader<MsgT> reader;
        std::unique_ptr<DataReaderListener<MsgT>> listener;

        TopicEntry(const dds::domain::DomainParticipant &participant,
                   const std::string &topic_name,
                   const dds::sub::Subscriber &subscriber,
                   const dds::sub::qos::DataReaderQos &qos,
                   std::unique_ptr<DataReaderListener<MsgT>> listener_ptr)
            : topic(participant, topic_name), reader(subscriber, topic, qos),
              listener(std::move(listener_ptr))
        {
            (void)qos;

            // 设置监听器
            dds::core::status::StatusMask mask =
                dds::core::status::StatusMask::data_available();
            if (listener->HasQosCallback())
            {
                mask |=
                    dds::core::status::StatusMask::requested_incompatible_qos();
            }
            reader.listener(listener.get(), mask);
        }

        void Close() override
        {
            reader.listener(nullptr, dds::core::status::StatusMask::none());
            reader.close();
        }
    };

    void ApplyQosConfig(dds::sub::qos::DataReaderQos &qos,
                        const DdsQosConfig &config)
    {
        (void)qos;
        (void)config;
        // TODO
        // qos.policy(dds::core::policy::Reliability::Reliable(
        //     config.reliability ==
        //     dds::core::policy::ReliabilityKind::RELIABLE));
        // qos.policy(dds::core::policy::Durability::Volatile(config.durability));
        // qos.policy(dds::core::policy::History::KeepLast(config.history_depth));
    }

    dds::domain::DomainParticipant participant_;
    dds::sub::Subscriber subscriber_;
    std::mutex topics_mutex_;
    std::unordered_map<std::string, std::unique_ptr<ITopicEntry>> topics_;
};

class Publisher : base::Noncopyable
{
public:
    static DdsResult<std::shared_ptr<Publisher>> Create(uint32_t domain_id)
    {
        try
        {
            dds::domain::DomainParticipant participant(domain_id);
            dds::pub::Publisher publisher(participant);
            // 同理修改Publisher的构造方式
            auto *raw_pub =
                new Publisher(std::move(participant), std::move(publisher));
            auto pub = std::shared_ptr<Publisher>(raw_pub);
            return DdsResult<std::shared_ptr<Publisher>>::Success(pub);
        }
        catch (const dds::core::Exception &e)
        {
            return DdsResult<std::shared_ptr<Publisher>>::Failure(
                "DDS exception: " + std::string(e.what()));
        }
        catch (const std::exception &e)
        {
            return DdsResult<std::shared_ptr<Publisher>>::Failure(
                "Standard exception: " + std::string(e.what()));
        }
    }

    template <typename MsgT>
    DdsResult<bool> AddTopic(const std::string &topic_name,
                             const DdsQosConfig &qos_config = DdsQosConfig{})
    {
        {
            std::lock_guard<std::mutex> lock{topics_mutex_};
            if (topics_.count(topic_name))
            {
                return DdsResult<bool>::Failure("Topic already exists");
            }
        }

        try
        {
            // auto entry = std::make_shared<TopicEntry<MsgT>>();

            // // 创建Topic
            // entry->topic = dds::topic::Topic<MsgT>(participant_, topic_name);

            // 配置QoS
            auto qos = publisher_.default_datawriter_qos();
            ApplyQosConfig(qos, qos_config);
            auto entry = std::make_unique<TopicEntry<MsgT>>(participant_,
                                                            topic_name,
                                                            publisher_,
                                                            qos);
            // 创建DataWriter
            entry->writer =
                dds::pub::DataWriter<MsgT>(publisher_, entry->topic, qos);

            {
                std::lock_guard<std::mutex> lock{topics_mutex_};
                topics_.emplace(topic_name, std::move(entry));
            }

            return DdsResult<bool>::Success(true);
        }
        catch (const dds::core::Exception &e)
        {
            return DdsResult<bool>::Failure("DDS exception: " +
                                            std::string(e.what()));
        }
        catch (const std::exception &e)
        {
            return DdsResult<bool>::Failure("Standard exception: " +
                                            std::string(e.what()));
        }
    }

    template <typename MsgT>
    DdsResult<bool> Publish(const std::string &topic_name, const MsgT &msg)
    {
        std::lock_guard<std::mutex> lock{topics_mutex_};
        auto iter = topics_.find(topic_name);
        if (iter == topics_.end())
        {
            return DdsResult<bool>::Failure("Topic not registered");
        }

        try
        {
            auto entry = static_cast<TopicEntry<MsgT> *>(iter->second.get());
            entry->writer.write(msg);
            return DdsResult<bool>::Success(true);
        }
        catch (const dds::core::Exception &e)
        {
            return DdsResult<bool>::Failure("DDS exception: " +
                                            std::string(e.what()));
        }
        catch (const std::exception &e)
        {
            return DdsResult<bool>::Failure("Standard exception: " +
                                            std::string(e.what()));
        }
    }

    /**
     * @brief 判断指定Topic是否已连接订阅者
     * @tparam MsgT Topic的消息类型
     * @param topic_name 要检查的Topic名称
     * @return DdsResult<bool> 连接状态结果
     */
    template <typename MsgT>
    DdsResult<bool> IsConnected(const std::string &topic_name)
    {
        std::lock_guard<std::mutex> lock(topics_mutex_);
        if (auto it = topics_.find(topic_name); it != topics_.end())
        {
            try
            {
                // 动态类型检查保证安全
                auto entry =
                    dynamic_cast<const TopicEntry<MsgT> *>(it->second.get());
                if (!entry)
                {
                    return DdsResult<bool>::Failure("Type mismatch for topic");
                }

                /* 正确做法：使用const_cast访问基础DDS对象 */
                auto &writer =
                    const_cast<dds::pub::DataWriter<MsgT> &>(entry->writer);
                auto status = writer.publication_matched_status();
                return DdsResult<bool>::Success((status.current_count() > 0));
            }
            catch (const dds::core::Exception &e)
            {
                return DdsResult<bool>::Failure("DDS exception: " +
                                                std::string(e.what()));
            }
        }
        return DdsResult<bool>::Failure("Topic not registered");
    }

    /**
     * @brief 获取匹配的订阅者数量
     * @tparam MsgT Topic的消息类型
     * @param topic_name 要检查的Topic名称
     * @return DdsResult<int> 匹配数量结果
     */
    template <typename MsgT>
    DdsResult<int> GetMatchedSubscribersCount(const std::string &topic_name)
    {
        std::lock_guard<std::mutex> lock(topics_mutex_);

        if (auto it = topics_.find(topic_name); it != topics_.end())
        {
            try
            {
                auto entry =
                    dynamic_cast<const TopicEntry<MsgT> *>(it->second.get());
                if (!entry)
                {
                    return DdsResult<int>::Failure("Type mismatch for topic");
                }
                /* 正确做法：使用const_cast访问基础DDS对象 */
                auto &writer =
                    const_cast<dds::pub::DataWriter<MsgT> &>(entry->writer);
                auto status = writer.publication_matched_status();
                return DdsResult<int>::Success(status.current_count());
            }
            catch (const dds::core::Exception &e)
            {
                return DdsResult<int>::Failure("DDS exception: " +
                                               std::string(e.what()));
            }
        }
        return DdsResult<int>::Failure("Topic not registered");
    }

    ~Publisher()
    {
        std::lock_guard<std::mutex> lock{topics_mutex_};
        for (auto &[name, entry] : topics_)
        {
            entry->Close();
        }
        topics_.clear();
    }

    // // 禁用拷贝和赋值
    // Publisher(const Publisher &) = delete;
    // Publisher &operator=(const Publisher &) = delete;

private:
    Publisher(dds::domain::DomainParticipant &&participant,
              dds::pub::Publisher &&publisher)
        : participant_(std::move(participant)), publisher_(std::move(publisher))
    {
    }

private:
    template <typename MsgT>
    struct TopicEntry : ITopicEntry
    {
        dds::topic::Topic<MsgT> topic;
        dds::pub::DataWriter<MsgT> writer;

        TopicEntry(const dds::domain::DomainParticipant &participant,
                   const std::string &topic_name,
                   const dds::pub::Publisher &publisher,
                   const dds::pub::qos::DataWriterQos &qos)
            : topic(participant, topic_name), writer(publisher, topic, qos)
        {
        }

        void Close() override
        {
            writer.close();
        }
    };

    void ApplyQosConfig(dds::pub::qos::DataWriterQos &qos,
                        const DdsQosConfig &config)
    {
        (void)qos;
        (void)config;
        // TODO
        // qos.policy(dds::core::policy::Reliability::Reliable(
        //     config.reliability ==
        //     dds::core::policy::ReliabilityKind::RELIABLE));
        // qos.policy(dds::core::policy::Durability::Volatile(config.durability));
        // qos.policy(dds::core::policy::History::KeepLast(config.history_depth));
    }

    dds::domain::DomainParticipant participant_;
    dds::pub::Publisher publisher_;
    std::mutex topics_mutex_;
    std::unordered_map<std::string, std::unique_ptr<ITopicEntry>> topics_;
};

} // namespace dds_wrapper
} // namespace fotamaster
} // namespace seres