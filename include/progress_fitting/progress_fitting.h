#pragma once

#include "OTA_DucData.hpp"
#include "base/noncopyable.h"
#include "base/singleton.h"
#include "dds_service_manager/duc_service_manager.h"
#include "ev_loop/timer.h"
#include "logger/logger.h"

#include <atomic>
#include <chrono>
#include <functional>
#include <map>
#include <memory>
#include <mutex>
#include <string>
#include <unordered_map>
#include <vector>

namespace seres
{
namespace fotamaster
{

using namespace seres::ota_duc_service;

// 进度回调函数类型
using ProgressCallback =
    std::function<void(uint8_t progress, bool allFinished)>;

// 设备进度信息
struct DeviceProgressInfo
{
    std::string deviceId;
    std::string deviceName;
    uint8_t currentProgress = 0;
    DeviceUpdateStatus status = DeviceUpdateStatus::IDLE;
    std::chrono::steady_clock::time_point lastUpdateTime;
    bool isActive = false; // 是否在当前更新任务中
};

// 域控进度信息
struct DomainProgressInfo
{
    DUCType ducType;
    std::map<std::string, DeviceProgressInfo>
        devices; // deviceId -> DeviceProgressInfo
    uint8_t domainProgress = 0;
    bool allFinished = false;
    std::chrono::steady_clock::time_point lastUpdateTime;
};

/**
 * @brief 进度拟合模块
 *
 * 负责收集各个域控和ECU的更新进度，使用平滑算法计算总体进度，
 * 并定期向外部反馈统一的进度信息。
 */
class ProgressFitting : public base::Noncopyable
{
public:
    ProgressFitting() = default;
    ~ProgressFitting();

    /**
     * @brief 初始化进度拟合模块
     *
     * @param updateDevices 要升级的设备列表
     * @param progressCallback 进度回调函数
     * @param reportInterval 进度报告间隔(秒)，默认1秒
     * @return true 初始化成功
     * @return false 初始化失败
     */
    bool Initialize(const std::vector<InventoryInfo> &updateDevices,
                    ProgressCallback progressCallback,
                    double reportInterval = 1.0);

    /**
     * @brief 启动进度监控
     *
     * @return true 启动成功
     * @return false 启动失败
     */
    bool Start();

    /**
     * @brief 停止进度监控
     */
    void Stop();

    /**
     * @brief 重置进度状态
     */
    void Reset();

    /**
     * @brief 获取当前总体进度
     *
     * @return uint8_t 进度百分比 (0-100)
     */
    uint8_t GetCurrentProgress() const;

    /**
     * @brief 检查是否所有设备都已完成更新
     *
     * @return true 全部完成
     * @return false 未全部完成
     */
    bool IsAllFinished() const;

    /**
     * @brief 设置进度平滑参数
     *
     * @param smoothingFactor 平滑因子 (0.0-1.0)，越小越平滑
     */
    void SetSmoothingFactor(double smoothingFactor);

private:
    /**
     * @brief 处理来自DDS的更新进度数据
     *
     * @param ducType 域控类型
     * @param progress 进度数据
     */
    void onUpdateProgress(DUCType ducType, const UpdateProgress &progress);

    /**
     * @brief 定时器回调，用于定期计算和报告进度
     */
    void onTimerCallback();

    /**
     * @brief 计算总体进度
     *
     * @return uint8_t 计算得出的总体进度
     */
    uint8_t calculateOverallProgress();

    /**
     * @brief 应用平滑算法
     *
     * @param newProgress 新的进度值
     * @return uint8_t 平滑后的进度值
     */
    uint8_t applySmoothingAlgorithm(uint8_t newProgress);

    /**
     * @brief 检查设备是否超时
     *
     * @param deviceInfo 设备信息
     * @return true 设备超时
     * @return false 设备正常
     */
    bool isDeviceTimeout(const DeviceProgressInfo &deviceInfo) const;

    /**
     * @brief 根据设备名称获取域控类型
     *
     * @param deviceName 设备名称
     * @return DUCType 域控类型
     */
    DUCType getDUCTypeByDeviceName(const std::string &deviceName) const;

private:
    // 初始化状态
    std::atomic<bool> m_initialized{false};
    std::atomic<bool> m_started{false};

    // 设备和域控信息
    std::vector<InventoryInfo> m_updateDevices;
    std::unordered_map<DUCType, DomainProgressInfo> m_domainProgress;

    // 进度计算相关
    std::atomic<uint8_t> m_currentProgress{0};
    std::atomic<uint8_t> m_smoothedProgress{0};
    std::atomic<bool> m_allFinished{false};

    // 平滑算法参数
    double m_smoothingFactor = 0.3; // 平滑因子，越小越平滑
    static constexpr double DEFAULT_DEVICE_TIMEOUT = 30.0; // 设备超时时间(秒)

    // 回调和定时器
    ProgressCallback m_progressCallback;
    std::shared_ptr<Timer> m_timer;
    double m_reportInterval = 1.0;

    // 线程安全
    mutable std::mutex m_mutex;

    // 设备名称到域控类型的映射
    std::unordered_map<std::string, DUCType> m_deviceToDUCMap;
};

} // namespace fotamaster
} // namespace seres