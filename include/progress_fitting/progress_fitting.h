#pragma once

#include "OTA_DucData.hpp"
#include "base/noncopyable.h"
#include "base/singleton.h"
#include "dds_service_manager/duc_service_manager.h"
#include "ev_loop/timer.h"
#include "logger/logger.h"

#include <atomic>
#include <chrono>
#include <functional>
#include <map>
#include <memory>
#include <mutex>
#include <string>
#include <unordered_map>
#include <vector>

namespace seres
{
namespace fotamaster
{

using namespace seres::ota_duc_service;

// 进度回调函数类型
using ProgressCallback =
    std::function<void(uint8_t progress, bool allFinished)>;

// 设备进度信息
struct DeviceProgressInfo
{
    std::string deviceId;        //设备id
    uint8_t currentProgress = 0; //进度
    float weight;                //设备权重
};

struct DomainProgressInfo
{
    std::vector<DeviceProgressInfo> devices; // 设备映射
    uint8_t domainProgress;                  // 域控总体进度
    float weight;                            //域控权重
    bool allFinished;                        // 是否全部完成
};

class ProgressFitting : public base::Noncopyable
{
public:
    ProgressFitting() = default;
    ~ProgressFitting();

    bool Initialize(ProgressCallback progressCallback,
                    double reportInterval = 1.0);

    bool AddUpdateDevice(DUCType ducType,
                         const std::vector<InventoryInfo> &inventorys);

    bool Start();

    void Stop();

    void Reset();

    uint8_t GetCurrentProgress() const;

    bool IsAllFinished() const;

    void SetSmoothingFactor(double smoothingFactor);

private:
    void onUpdateProgress(DUCType ducType, const UpdateProgress &progress);

    void onTimerCallback();

    uint8_t calculateOverallProgress();

    uint8_t applySmoothingAlgorithm(uint8_t newProgress);

private:
    // 初始化状态
    std::atomic<bool> m_initialized{false};
    std::atomic<bool> m_started{false};

    // 设备和域控进度信息
    std::unordered_map<DUCType, DomainProgressInfo> m_domainProgress;

    // 进度计算相关
    std::atomic<uint8_t> m_currentProgress{0};
    std::atomic<uint8_t> m_smoothedProgress{0};
    std::atomic<bool> m_allFinished{false};

    // 平滑算法参数
    double m_smoothingFactor = 0.3; // 平滑因子，越小越平滑
    static constexpr double DEFAULT_DEVICE_TIMEOUT = 30.0; // 设备超时时间(秒)

    // 回调和定时器
    ProgressCallback m_progressCallback;
    std::shared_ptr<Timer> m_timer;
    double m_reportInterval = 1.0;

    // 线程安全
    mutable std::mutex m_mutex;
};

} // namespace fotamaster
} // namespace seres