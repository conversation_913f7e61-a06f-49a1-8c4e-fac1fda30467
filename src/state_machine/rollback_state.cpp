#include "state_machine/rollback_state.h"
#include "logger/logger.h"

namespace seres
{
namespace fotamaster
{

bool RollbackState::Enter()
{
    LOG_INFO("===Enter rollback state");
    return true;
}

void RollbackState::Process(const EventData &data)
{
    LOG_INFO("=====Process rollback state, data.type = %d", data.type);
}

void RollbackState::Exit()
{
    LOG_INFO("===Exit rollback state");
}

REGISTER_STATE("RollbackState", RollbackState)

} // namespace fotamaster
} // namespace seres