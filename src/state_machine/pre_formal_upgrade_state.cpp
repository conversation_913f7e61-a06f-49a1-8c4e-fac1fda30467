#include "state_machine/pre_formal_upgrade_state.h"
#include "logger/logger.h"

namespace seres
{
namespace fotamaster
{

bool PreFormalUpgradeState::Enter()
{
    LOG_INFO("=====Enter pre formal upgrade state");
    return true;
}

void PreFormalUpgradeState::Process(const EventData &data)
{
    LOG_INFO("=====Process pre formal upgrade state, data.type = %d", data.type);
}

void PreFormalUpgradeState::Exit()
{
    LOG_INFO("=====Exit pre formal upgrade state");
}

REGISTER_STATE("PreFormalUpgradeState", PreFormalUpgradeState)

} // namespace fotamaster
} // namespace seres