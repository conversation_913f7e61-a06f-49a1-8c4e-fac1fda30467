#include "state_machine/ota_state_machine.h"
#include "base/classfactory.h"
#include "base/singleton.h"
#include "logger/logger.h"
#include <vector>


namespace seres
{
namespace fotamaster
{

OtaStateMachine::OtaStateMachine(EventLoop *event_loop)
    : StateMachine(event_loop)
{
    auto state_name_vec = base::Singleton<base::ClassFactoryManager>::Instance()
                              .GetRegisteredClasses<State>();
    LOG_INFO("Registered states size: %lu", state_name_vec.size());

    for (const auto &state_name : state_name_vec)
    {
        LOG_INFO("Machine add state: %s", state_name.c_str());
        AddState(state_name, this);
    }

    // ChangeState("IdleState");
}

OtaStateMachine::~OtaStateMachine()
{
}

void OtaStateMachine::ChangeState(const std::string &new_state)
{
    StateMachine::ChangeState(new_state);
}

} // namespace fotamaster
} // namespace seres