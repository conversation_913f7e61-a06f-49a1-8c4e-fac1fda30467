#include "state_machine/fault_state.h"
#include "logger/logger.h"

namespace seres
{
namespace fotamaster
{

bool FaultState::Enter()
{
    LOG_INFO("=====Enter fault state");
    return true;
}

void FaultState::Process(const EventData &data)
{
    LOG_INFO("=====Process fault state, data.type = %d", data.type);
}

void FaultState::Exit()
{
    LOG_INFO("=====Exit fault state");
}

REGISTER_STATE("FaultState", FaultState)

} // namespace fotamaster
} // namespace seres