#include "state_machine/exit_state.h"
#include "logger/logger.h"

namespace seres
{
namespace fotamaster
{

bool ExitState::Enter()
{
    LOG_INFO("=====Enter exit state");
    return true;
}

void ExitState::Process(const EventData &data)
{
    LOG_INFO("=====Process exit state, data.type = %d", data.type);
}

void ExitState::Exit()
{
    LOG_INFO("=====Exit exit state");
}

REGISTER_STATE("ExitState", ExitState)

} // namespace fotamaster
} // namespace seres