#include "state_machine/idle_state.h"
#include "logger/logger.h"

namespace seres
{
namespace fotamaster
{

bool IdleState::Enter()
{
    LOG_INFO("=====Enter idle state");
    return true;
}

void IdleState::Process(const EventData &data)
{
    LOG_INFO("=====Process idle state, data.type = %d", data.type);
}

void IdleState::Exit()
{
    LOG_INFO("=====Exit idle state");
}

REGISTER_STATE("IdleState", IdleState)

} // namespace fotamaster
} // namespace seres