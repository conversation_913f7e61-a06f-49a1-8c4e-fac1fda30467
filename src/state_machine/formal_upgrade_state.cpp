#include "state_machine/formal_upgrade_state.h"
#include "logger/logger.h"

namespace seres
{
namespace fotamaster
{

bool FormalUpgradeState::Enter()
{
    LOG_INFO("=====Enter formal upgrade state");
    return true;
}

void FormalUpgradeState::Process(const EventData &data)
{
    LOG_INFO("=====Process formal upgrade state, data.type = %d", data.type);
}

void FormalUpgradeState::Exit()
{
    LOG_INFO("=====Exit formal upgrade state");
}

REGISTER_STATE("FormalUpgradeState", FormalUpgradeState)

} // namespace fotamaster
} // namespace seres