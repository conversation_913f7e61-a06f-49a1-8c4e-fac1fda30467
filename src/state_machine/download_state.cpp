#include "state_machine/download_state.h"
#include "logger/logger.h"

namespace seres
{
namespace fotamaster
{

bool DownloadState::Enter()
{
    LOG_INFO("=====Enter download state");
    return true;
}

void DownloadState::Process(const EventData &data)
{
    LOG_INFO("=====Process download state, data.type = %d", data.type);
}

void DownloadState::Exit()
{
    LOG_INFO("=====Exit download state");
}

REGISTER_STATE("DownloadState", DownloadState)

} // namespace fotamaster
} // namespace seres