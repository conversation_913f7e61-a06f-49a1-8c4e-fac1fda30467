#include "state_machine/seamless_upgrade_state.h"
#include "logger/logger.h"

namespace seres
{
namespace fotamaster
{

bool SeamlessUpgradeState::Enter()
{
    LOG_INFO("=====Enter seamless upgrade state state");
    return true;
}

void SeamlessUpgradeState::Process(const EventData &data)
{
    LOG_INFO("=====Process seamless upgrade state, data.type = %d", data.type);
}

void SeamlessUpgradeState::Exit()
{
    LOG_INFO("=====Exit seamless upgrade state");
}

REGISTER_STATE("SeamlessUpgradeState", SeamlessUpgradeState)

} // namespace fotamaster
} // namespace seres