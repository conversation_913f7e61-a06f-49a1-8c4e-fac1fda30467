/**
 * @file progress_fitting_example.cpp
 * @brief 进度拟合模块使用示例
 * 
 * 本文件展示了如何在fotamaster项目中使用ProgressFitting模块
 * 来聚合和平滑多个域控和ECU的更新进度。
 */

#include "dds_service_manager/duc_service_manager.h"
#include "ev_loop/eventloop_manager.h"
#include "logger/logger.h"
#include "progress_fitting/progress_fitting.h"

#include <chrono>
#include <iostream>
#include <thread>

using namespace seres::fotamaster;
using namespace seres::ota_duc_service;

class OTAProgressManager
{
public:
    OTAProgressManager() = default;
    ~OTAProgressManager() = default;

    /**
     * @brief 初始化OTA进度管理器
     * 
     * @param updateDevices 要升级的设备列表
     * @return true 初始化成功
     * @return false 初始化失败
     */
    bool Initialize(const std::vector<InventoryInfo> &updateDevices)
    {
        LOG_INFO("Initializing OTA Progress Manager with %zu devices",
                 updateDevices.size());

        // 初始化进度拟合模块
        bool result = m_progressFitting.Initialize(
            [this](uint8_t progress, bool allFinished) {
                this->onProgressUpdate(progress, allFinished);
            },
            1.0 // 每秒报告一次进度
        );

        if (!result)
        {
            LOG_ERROR("Failed to initialize progress fitting module");
            return false;
        }

        // 设置平滑因子，值越小进度越平滑
        m_progressFitting.SetSmoothingFactor(0.2);

        m_updateDevices = updateDevices;
        LOG_INFO("OTA Progress Manager initialized successfully");
        return true;
    }

    /**
     * @brief 开始OTA升级过程
     * 
     * @return true 启动成功
     * @return false 启动失败
     */
    bool StartOTAUpdate()
    {
        LOG_INFO("Starting OTA update process");

        // 启动进度监控
        if (!m_progressFitting.Start())
        {
            LOG_ERROR("Failed to start progress fitting");
            return false;
        }

        // 这里可以添加实际的OTA升级启动逻辑
        // 例如：向各个域控发送升级命令

        LOG_INFO("OTA update process started");
        return true;
    }

    /**
     * @brief 停止OTA升级过程
     */
    void StopOTAUpdate()
    {
        LOG_INFO("Stopping OTA update process");
        m_progressFitting.Stop();
        LOG_INFO("OTA update process stopped");
    }

    /**
     * @brief 重置OTA升级进度
     */
    void ResetProgress()
    {
        LOG_INFO("Resetting OTA progress");
        m_progressFitting.Reset();
    }

    /**
     * @brief 获取当前总体进度
     * 
     * @return uint8_t 进度百分比 (0-100)
     */
    uint8_t GetCurrentProgress() const
    {
        return m_progressFitting.GetCurrentProgress();
    }

    /**
     * @brief 检查是否所有设备都已完成更新
     * 
     * @return true 全部完成
     * @return false 未全部完成
     */
    bool IsUpdateComplete() const
    {
        return m_progressFitting.IsAllFinished();
    }

private:
    /**
     * @brief 进度更新回调函数
     * 
     * @param progress 当前进度百分比
     * @param allFinished 是否全部完成
     */
    void onProgressUpdate(uint8_t progress, bool allFinished)
    {
        LOG_INFO("OTA Progress Update: %d%%, All finished: %s",
                 progress,
                 allFinished ? "Yes" : "No");

        // 这里可以添加进度通知逻辑
        // 例如：向HMI发送进度更新消息
        notifyProgressToHMI(progress, allFinished);

        // 如果全部完成，执行后续操作
        if (allFinished)
        {
            onOTAUpdateComplete();
        }
    }

    /**
     * @brief 向HMI通知进度更新
     * 
     * @param progress 进度百分比
     * @param allFinished 是否全部完成
     */
    void notifyProgressToHMI(uint8_t progress, bool allFinished)
    {
        // 模拟向HMI发送进度消息
        LOG_INFO("Notifying HMI: Progress = %d%%, Complete = %s",
                 progress,
                 allFinished ? "true" : "false");

        // 实际实现中，这里会调用HMI通信接口
        // 例如：hmiInterface.sendProgressUpdate(progress, allFinished);
    }

    /**
     * @brief OTA更新完成后的处理
     */
    void onOTAUpdateComplete()
    {
        LOG_INFO("OTA update completed successfully!");

        // 执行完成后的清理工作
        // 例如：清理临时文件、发送完成通知等

        // 停止进度监控
        StopOTAUpdate();
    }

private:
    ProgressFitting m_progressFitting;
    std::vector<InventoryInfo> m_updateDevices;
};

/**
 * @brief 创建测试用的设备列表
 * 
 * @return std::vector<InventoryInfo> 设备列表
 */
std::vector<InventoryInfo> createTestDeviceList()
{
    std::vector<InventoryInfo> devices;

    // 座舱域设备
    InventoryInfo cdcEcu1;
    cdcEcu1.ecuName("CDC_MainECU");
    cdcEcu1.serialNumber("CDC001");
    cdcEcu1.partNumber("CDC-MAIN-001");
    cdcEcu1.softwareVersion("1.0.0");
    devices.push_back(cdcEcu1);

    InventoryInfo cdcEcu2;
    cdcEcu2.ecuName("CDC_DisplayECU");
    cdcEcu2.serialNumber("CDC002");
    cdcEcu2.partNumber("CDC-DISP-001");
    cdcEcu2.softwareVersion("2.1.0");
    devices.push_back(cdcEcu2);

    // 智驾域设备
    InventoryInfo mdcEcu1;
    mdcEcu1.ecuName("MDC_ADAS_Controller");
    mdcEcu1.serialNumber("MDC001");
    mdcEcu1.partNumber("MDC-ADAS-001");
    mdcEcu1.softwareVersion("3.2.1");
    devices.push_back(mdcEcu1);

    // 其他域设备
    InventoryInfo zcuEcu1;
    zcuEcu1.ecuName("ZCU_Gateway");
    zcuEcu1.serialNumber("ZCU001");
    zcuEcu1.partNumber("ZCU-GW-001");
    zcuEcu1.softwareVersion("1.5.2");
    devices.push_back(zcuEcu1);

    return devices;
}

/**
 * @brief 主函数 - 进度拟合模块使用示例
 */
int main()
{
    // 初始化日志系统
    LogConfig logConfig;
    logConfig.console_output = true;
    logConfig.log_level = LogLevel::kLogLevelInfo;
    base::Singleton<Logger>::Instance().Init(logConfig);

    LOG_INFO("=== ProgressFitting Module Usage Example ===");

    try
    {
        // 创建OTA进度管理器
        OTAProgressManager otaManager;

        // 创建测试设备列表
        auto testDevices = createTestDeviceList();

        // 初始化进度管理器
        if (!otaManager.Initialize(testDevices))
        {
            LOG_ERROR("Failed to initialize OTA progress manager");
            return -1;
        }

        // 启动OTA更新过程
        if (!otaManager.StartOTAUpdate())
        {
            LOG_ERROR("Failed to start OTA update");
            return -1;
        }

        // 模拟运行一段时间
        LOG_INFO("Running OTA update simulation for 10 seconds...");
        std::this_thread::sleep_for(std::chrono::seconds(10));

        // 获取当前进度
        uint8_t currentProgress = otaManager.GetCurrentProgress();
        bool isComplete = otaManager.IsUpdateComplete();

        LOG_INFO("Current progress: %d%%, Complete: %s",
                 currentProgress,
                 isComplete ? "Yes" : "No");

        // 停止OTA更新
        otaManager.StopOTAUpdate();

        LOG_INFO("=== Example completed successfully ===");
    }
    catch (const std::exception &e)
    {
        LOG_ERROR("Exception occurred: %s", e.what());
        return -1;
    }

    return 0;
}
