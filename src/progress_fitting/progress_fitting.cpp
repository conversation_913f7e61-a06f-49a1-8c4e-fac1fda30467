#include "ev_loop/eventloop_manager.h"
#include "progress_fitting/progress_fitting.h"

#include <algorithm>
#include <cmath>

namespace seres
{
namespace fotamaster
{

ProgressFitting::~ProgressFitting()
{
    Stop();
}

bool ProgressFitting::Initialize(ProgressCallback progressCallback,
                                 double reportInterval)
{
    std::lock_guard<std::mutex> lock(m_mutex);

    if (m_initialized.load())
    {
        LOG_WARN("ProgressFitting already initialized");
        return false;
    }

    if (!progressCallback)
    {
        LOG_ERROR("Progress callback is null");
        return false;
    }

    if (reportInterval <= 0.0)
    {
        LOG_ERROR("Invalid report interval: %f", reportInterval);
        return false;
    }

    // 保存配置
    m_progressCallback = progressCallback;
    m_reportInterval = reportInterval;

    // 初始化域控进度信息
    m_domainProgress.clear();
    // 创建定时器
    auto eventLoop =
        base::Singleton<EventLoopManager>::Instance().GetDefaultLoop();
    if (!eventLoop)
    {
        LOG_ERROR("Failed to get event loop");
        return false;
    }

    m_timer = std::make_shared<Timer>(eventLoop);
    if (!m_timer)
    {
        LOG_ERROR("Failed to create timer");
        return false;
    }

    // 订阅各个域控的更新进度
    auto &ducManager = base::Singleton<DUCServiceManager>::Instance();
    ducManager.subscribeUpdateProgress(DUCType::CDC,
                                       [this](const UpdateProgress &progress) {
                                           this->onUpdateProgress(DUCType::CDC,
                                                                  progress);
                                       });

    ducManager.subscribeUpdateProgress(DUCType::MDC,
                                       [this](const UpdateProgress &progress) {
                                           this->onUpdateProgress(DUCType::MDC,
                                                                  progress);
                                       });

    ducManager.subscribeUpdateProgress(DUCType::ZCU,
                                       [this](const UpdateProgress &progress) {
                                           this->onUpdateProgress(DUCType::ZCU,
                                                                  progress);
                                       });

    // 重置进度状态
    m_currentProgress.store(0);
    m_smoothedProgress.store(0);
    m_allFinished.store(false);

    m_initialized.store(true);

    return true;
}

bool ProgressFitting::addUpdateDevice(DUCType type,
                                      const std::vector<InventoryInfo> &devices)
{
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!m_initialized.load())
    {
        LOG_ERROR("ProgressFitting not initialized");
        return false;
    }

    if (devices.empty())
    {
        LOG_ERROR("Update devices list is empty");
        return false;
    }

    // 更新设备和域控信息
    for (const auto &device : devices)
    {
        DeviceProgressInfo deviceInfo;
        deviceInfo.deviceId = device.serialNumber();
        deviceInfo.deviceName = device.ecuName();
        deviceInfo.currentProgress = 0;
        deviceInfo.status = DeviceUpdateStatus::IDLE;
        deviceInfo.lastUpdateTime = std::chrono::steady_clock::now();
        deviceInfo.isActive = true;
        m_domainProgress[type].push_back(deviceInfo);
    }

    LOG_INFO("Added %zu devices to ProgressFitting", devices.size());
    return true;
}

bool ProgressFitting::Start()
{
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!m_initialized.load())
    {
        LOG_ERROR("ProgressFitting not initialized");
        return false;
    }

    if (m_started.load())
    {
        LOG_WARN("ProgressFitting already started");
        return true;
    }

    // 设置定时器回调
    auto result = m_timer->SetCallback([this]() { this->onTimerCallback(); });

    if (result.has_value())
    {
        LOG_ERROR("Failed to set timer callback: %d",
                  static_cast<int>(result.value()));
        return false;
    }

    // 启动定时器
    result = m_timer->After(m_reportInterval);
    if (result.has_value())
    {
        LOG_ERROR("Failed to start timer: %d",
                  static_cast<int>(result.value()));
        return false;
    }

    m_started.store(true);
    LOG_INFO("ProgressFitting started with report interval: %f seconds",
             m_reportInterval);

    return true;
}

void ProgressFitting::Stop()
{
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!m_started.load())
    {
        return;
    }

    // 停止定时器
    if (m_timer)
    {
        m_timer->Cancel();
    }

    m_started.store(false);
    LOG_INFO("ProgressFitting stopped");
}

void ProgressFitting::Reset()
{
    std::lock_guard<std::mutex> lock(m_mutex);

    LOG_INFO("ProgressFitting reset");
}

uint8_t ProgressFitting::GetCurrentProgress() const
{
    return m_smoothedProgress.load();
}

bool ProgressFitting::IsAllFinished() const
{
    return m_allFinished.load();
}

void ProgressFitting::SetSmoothingFactor(double smoothingFactor)
{
    std::lock_guard<std::mutex> lock(m_mutex);

    if (smoothingFactor < 0.0 || smoothingFactor > 1.0)
    {
        LOG_WARN("Invalid smoothing factor: %f, using default 0.3",
                 smoothingFactor);
        m_smoothingFactor = 0.3;
    }
    else
    {
        m_smoothingFactor = smoothingFactor;
        LOG_INFO("Smoothing factor set to: %f", m_smoothingFactor);
    }
}

void ProgressFitting::onUpdateProgress(DUCType ducType,
                                       const UpdateProgress &progress)
{
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!m_initialized.load() || !m_started.load())
    {
        return;
    }

    auto domainIt = m_domainProgress.find(ducType);
    if (domainIt == m_domainProgress.end())
    {
        LOG_WARN("Received progress for unknown domain: %d",
                 static_cast<int>(ducType));
        return;
    }

    auto &domainInfo = domainIt->second;
    domainInfo.lastUpdateTime = std::chrono::steady_clock::now();
    domainInfo.allFinished = progress.allFinished();

    // 更新设备进度
    for (const auto &deviceProgress : progress.progressLists())
    {
        auto deviceIt = domainInfo.devices.find(deviceProgress.deviceId());
        if (deviceIt != domainInfo.devices.end())
        {
            auto &deviceInfo = deviceIt->second;
            deviceInfo.currentProgress = deviceProgress.progressPercent();
            deviceInfo.status = deviceProgress.status();
            deviceInfo.lastUpdateTime = std::chrono::steady_clock::now();

            LOG_DEBUG("Updated device progress: %s = %d%%, status = %d",
                      deviceProgress.deviceName().c_str(),
                      deviceProgress.progressPercent(),
                      static_cast<int>(deviceProgress.status()));
        }
        else
        {
            LOG_DEBUG("Received progress for unknown device: %s",
                      deviceProgress.deviceName().c_str());
        }
    }

    // 计算域控总体进度
    uint32_t totalProgress = 0;
    uint32_t deviceCount = 0;
    bool allDevicesFinished = true;

    for (const auto &[deviceId, deviceInfo] : domainInfo.devices)
    {
        if (deviceInfo.isActive)
        {
            totalProgress += deviceInfo.currentProgress;
            deviceCount++;

            if (deviceInfo.status != DeviceUpdateStatus::SUCCESS)
            {
                allDevicesFinished = false;
            }
        }
    }

    if (deviceCount > 0)
    {
        domainInfo.domainProgress =
            static_cast<uint8_t>(totalProgress / deviceCount);
    }
    else
    {
        domainInfo.domainProgress = 0;
    }

    domainInfo.allFinished = allDevicesFinished && progress.allFinished();

    LOG_DEBUG("Domain %d progress: %d%%, all finished: %s",
              static_cast<int>(ducType),
              domainInfo.domainProgress,
              domainInfo.allFinished ? "true" : "false");
}

void ProgressFitting::onTimerCallback()
{
    if (!m_initialized.load() || !m_started.load())
    {
        return;
    }

    // 计算总体进度
    uint8_t newProgress = calculateOverallProgress();

    // 应用平滑算法
    uint8_t smoothedProgress = applySmoothingAlgorithm(newProgress);

    // 检查是否所有设备都已完成
    bool allFinished = true;
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        for (const auto &[ducType, domainInfo] : m_domainProgress)
        {
            if (!domainInfo.allFinished)
            {
                allFinished = false;
                break;
            }
        }
    }

    // 更新状态
    m_currentProgress.store(newProgress);
    m_smoothedProgress.store(smoothedProgress);
    m_allFinished.store(allFinished);

    // 调用外部回调
    if (m_progressCallback)
    {
        m_progressCallback(smoothedProgress, allFinished);
    }

    // 如果未完成，重新启动定时器
    if (!allFinished && m_started.load())
    {
        auto result = m_timer->After(m_reportInterval);
        if (result.has_value())
        {
            LOG_ERROR("Failed to restart timer: %d",
                      static_cast<int>(result.value()));
        }
    }
    else if (allFinished)
    {
        LOG_INFO("All devices finished updating, stopping progress monitoring");
        // 不需要手动停止，让外部决定何时停止
    }
}

uint8_t ProgressFitting::calculateOverallProgress()
{
    std::lock_guard<std::mutex> lock(m_mutex);

    if (m_domainProgress.empty())
    {
        return 0;
    }

    uint32_t totalProgress = 0;
    uint32_t totalWeight = 0;

    // 计算各域控的加权平均进度
    for (const auto &[ducType, domainInfo] : m_domainProgress)
    {
        uint32_t domainWeight =
            static_cast<uint32_t>(domainInfo.devices.size());
        if (domainWeight == 0)
            continue;

        uint32_t domainProgress = 0;
        uint32_t activeDevices = 0;

        // 计算域控内设备的平均进度
        for (const auto &[deviceId, deviceInfo] : domainInfo.devices)
        {
            if (!deviceInfo.isActive)
                continue;

            uint8_t deviceProgress = deviceInfo.currentProgress;

            // 如果设备已成功完成，进度设为100%
            if (deviceInfo.status == DeviceUpdateStatus::SUCCESS)
            {
                deviceProgress = 100;
            }
            // 如果设备失败，进度保持当前值不变
            else if (deviceInfo.status == DeviceUpdateStatus::FAILURE)
            {
                LOG_WARN("Device %s failed, progress: %d%%",
                         deviceInfo.deviceName.c_str(),
                         deviceProgress);
            }

            domainProgress += deviceProgress;
            activeDevices++;
        }

        if (activeDevices > 0)
        {
            uint8_t avgDomainProgress =
                static_cast<uint8_t>(domainProgress / activeDevices);
            totalProgress += avgDomainProgress * domainWeight;
            totalWeight += domainWeight;

            LOG_DEBUG("Domain %d: %d%% (weight: %d, active devices: %d)",
                      static_cast<int>(ducType),
                      avgDomainProgress,
                      domainWeight,
                      activeDevices);
        }
    }

    uint8_t overallProgress = 0;
    if (totalWeight > 0)
    {
        overallProgress = static_cast<uint8_t>(totalProgress / totalWeight);
    }

    // 确保进度不会倒退（除非重置）
    uint8_t currentProgress = m_currentProgress.load();
    if (overallProgress < currentProgress && overallProgress > 0)
    {
        LOG_DEBUG("Progress would decrease from %d%% to %d%%, keeping current",
                  currentProgress,
                  overallProgress);
        overallProgress = currentProgress;
    }

    LOG_DEBUG("Calculated overall progress: %d%% (total weight: %d)",
              overallProgress,
              totalWeight);

    return overallProgress;
}

uint8_t ProgressFitting::applySmoothingAlgorithm(uint8_t newProgress)
{
    uint8_t currentSmoothed = m_smoothedProgress.load();

    // 使用指数移动平均进行平滑
    // smoothed = α * new + (1 - α) * old
    double smoothed = m_smoothingFactor * newProgress +
                      (1.0 - m_smoothingFactor) * currentSmoothed;

    uint8_t result = static_cast<uint8_t>(std::round(smoothed));

    // 确保平滑后的进度不会超过原始进度太多
    if (result > newProgress + 5)
    {
        result = newProgress;
    }

    // 确保进度在有效范围内
    result = std::min(result, static_cast<uint8_t>(100));

    LOG_DEBUG("Applied smoothing: %d%% -> %d%% (factor: %f)",
              newProgress,
              result,
              m_smoothingFactor);

    return result;
}

} // namespace fotamaster
} // namespace seres