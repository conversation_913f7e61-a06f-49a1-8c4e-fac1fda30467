#include "ev_loop/eventloop_manager.h"
#include "progress_fitting/progress_fitting.h"

#include <algorithm>
#include <cmath>

namespace seres
{
namespace fotamaster
{

ProgressFitting::~ProgressFitting()
{
    Stop();
}

bool ProgressFitting::Initialize(ProgressCallback progressCallback,
                                 double reportInterval)
{
    std::lock_guard<std::mutex> lock(m_mutex);

    if (m_initialized.load())
    {
        LOG_WARN("ProgressFitting already initialized");
        return false;
    }

    if (!progressCallback)
    {
        LOG_ERROR("Progress callback is null");
        return false;
    }

    if (reportInterval <= 0.0)
    {
        LOG_ERROR("Invalid report interval: %f", reportInterval);
        return false;
    }

    // 保存配置
    m_progressCallback = progressCallback;
    m_reportInterval = reportInterval;

    // 初始化域控进度信息
    m_domainProgress.clear();
    // 创建定时器
    auto eventLoop =
        base::Singleton<EventLoopManager>::Instance().GetDefaultLoop();
    if (!eventLoop)
    {
        LOG_ERROR("Failed to get event loop");
        return false;
    }

    m_timer = std::make_shared<Timer>(eventLoop);
    if (!m_timer)
    {
        LOG_ERROR("Failed to create timer");
        return false;
    }

    // 订阅各个域控的更新进度
    auto &ducManager = base::Singleton<DUCServiceManager>::Instance();
    ducManager.subscribeUpdateProgress(DUCType::CDC,
                                       [this](const UpdateProgress &progress) {
                                           this->onUpdateProgress(DUCType::CDC,
                                                                  progress);
                                       });

    ducManager.subscribeUpdateProgress(DUCType::MDC,
                                       [this](const UpdateProgress &progress) {
                                           this->onUpdateProgress(DUCType::MDC,
                                                                  progress);
                                       });

    ducManager.subscribeUpdateProgress(DUCType::ZCU,
                                       [this](const UpdateProgress &progress) {
                                           this->onUpdateProgress(DUCType::ZCU,
                                                                  progress);
                                       });

    // 重置进度状态
    m_currentProgress.store(0);
    m_smoothedProgress.store(0);
    m_allFinished.store(false);

    m_initialized.store(true);

    return true;
}

bool ProgressFitting::AddUpdateDevice(
    DUCType ducType,
    const std::vector<InventoryInfo> &inventorys)
{
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!m_initialized.load())
    {
        LOG_ERROR("ProgressFitting not initialized");
        return false;
    }

    if (inventorys.empty())
    {
        LOG_ERROR("Update devices list is empty");
        return false;
    }

    // 创建域控进度信息
    DomainProgressInfo domainInfo;
    domainInfo.domainProgress = 0;
    domainInfo.allFinished = false;

    // 计算每个设备的权重 - 域内设备分配相同权重
    float deviceWeight = 1.0f / static_cast<float>(inventorys.size());

    // 添加设备信息
    for (const auto &inventory : inventorys)
    {
        DeviceProgressInfo deviceInfo;
        deviceInfo.deviceId = inventory.serialNumber();
        deviceInfo.currentProgress = 0;
        deviceInfo.weight = deviceWeight; // 设备在域内的权重
        domainInfo.devices.push_back(deviceInfo);
    }

    // 保存域控信息
    m_domainProgress[ducType] = domainInfo;

    // 设置域控权重 - 三个域分配相同权重
    float dweight = 1.0f / float(m_domainProgress.size());

    for (auto &pair : m_domainProgress)
    {
        pair.second.weight = dweight;
        LOG_INFO("Adding domain %s with weight %.3f",
                 ducTypeToString(pair.first).c_str(),
                 pair.second.weight);
    }

    LOG_INFO("Added %zu devices to domain %s with device weight %.3f and "
             "domain weight %.3f",
             inventorys.size(),
             ducTypeToString(ducType).c_str(),
             deviceWeight,
             m_domainProgress[ducType].weight);

    return true;
}

bool ProgressFitting::Start()
{
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!m_initialized.load())
    {
        LOG_ERROR("ProgressFitting not initialized");
        return false;
    }

    if (m_started.load())
    {
        LOG_WARN("ProgressFitting already started");
        return true;
    }

    // 设置定时器回调
    auto result = m_timer->SetCallback([this]() { this->onTimerCallback(); });

    if (result.has_value())
    {
        LOG_ERROR("Failed to set timer callback: %d",
                  static_cast<int>(result.value()));
        return false;
    }

    // 启动定时器
    result = m_timer->After(m_reportInterval);
    if (result.has_value())
    {
        LOG_ERROR("Failed to start timer: %d",
                  static_cast<int>(result.value()));
        return false;
    }

    m_started.store(true);
    LOG_INFO("ProgressFitting started with report interval: %f seconds",
             m_reportInterval);

    return true;
}

void ProgressFitting::Stop()
{
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!m_started.load())
    {
        return;
    }

    // 停止定时器
    if (m_timer)
    {
        m_timer->Cancel();
    }

    m_started.store(false);
    LOG_INFO("ProgressFitting stopped");
}

void ProgressFitting::Reset()
{
    std::lock_guard<std::mutex> lock(m_mutex);

    LOG_INFO("ProgressFitting reset");
}

uint8_t ProgressFitting::GetCurrentProgress() const
{
    return m_smoothedProgress.load();
}

bool ProgressFitting::IsAllFinished() const
{
    return m_allFinished.load();
}

void ProgressFitting::SetSmoothingFactor(double smoothingFactor)
{
    std::lock_guard<std::mutex> lock(m_mutex);

    if (smoothingFactor < 0.0 || smoothingFactor > 1.0)
    {
        LOG_WARN("Invalid smoothing factor: %f, using default 0.3",
                 smoothingFactor);
        m_smoothingFactor = 0.3;
    }
    else
    {
        m_smoothingFactor = smoothingFactor;
        LOG_INFO("Smoothing factor set to: %f", m_smoothingFactor);
    }
}

void ProgressFitting::onUpdateProgress(DUCType ducType,
                                       const UpdateProgress &progress)
{
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!m_initialized.load() || !m_started.load())
    {
        return;
    }

    // 检查域控是否存在
    auto domainIt = m_domainProgress.find(ducType);
    if (domainIt == m_domainProgress.end())
    {
        LOG_WARN("Received progress for unknown domain: %d",
                 static_cast<int>(ducType));
        return;
    }

    auto &domainInfo = domainIt->second;

    // 更新域控完成状态
    if (progress.allFinished())
    {
        domainInfo.allFinished = true;
        LOG_INFO("Domain %d finished updating", static_cast<int>(ducType));
    }
    // 更新设备进度
    float totalWeightedProgress = 0.0f;
    for (const auto &singleDevice : progress.progressLists())
    {
        for (auto &deviceInfo : domainInfo.devices)
        {
            if (deviceInfo.deviceId == singleDevice.deviceId())
            {
                deviceInfo.currentProgress = singleDevice.progressPercent();
                totalWeightedProgress +=
                    deviceInfo.currentProgress * deviceInfo.weight;
                LOG_DEBUG("Updated device %s progress: %d%%",
                          deviceInfo.deviceId.c_str(),
                          deviceInfo.currentProgress);
                break;
            }
        }
    }
    domainInfo.domainProgress = totalWeightedProgress;

    LOG_DEBUG("Domain %d progress calculated: %d%%",
              static_cast<int>(ducType),
              domainInfo.domainProgress);
}

void ProgressFitting::onTimerCallback()
{
    if (!m_initialized.load() || !m_started.load())
    {
        return;
    }

    // 计算总体进度
    uint8_t newProgress = calculateOverallProgress();

    // 应用平滑算法
    uint8_t smoothedProgress = applySmoothingAlgorithm(newProgress);

    // 检查是否所有设备都已完成
    bool allFinished = true;
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        for (const auto &[ducType, domainInfo] : m_domainProgress)
        {
            if (!domainInfo.allFinished)
            {
                allFinished = false;
                break;
            }
        }
    }

    // 更新状态
    m_currentProgress.store(newProgress);
    m_smoothedProgress.store(smoothedProgress);
    m_allFinished.store(allFinished);

    // 调用外部回调
    if (m_progressCallback)
    {
        m_progressCallback(smoothedProgress, allFinished);
    }

    // 如果未完成，重新启动定时器
    if (!allFinished && m_started.load())
    {
        auto result = m_timer->After(m_reportInterval);
        if (result.has_value())
        {
            LOG_ERROR("Failed to restart timer: %d",
                      static_cast<int>(result.value()));
        }
    }
    else if (allFinished)
    {
        LOG_INFO("All devices finished updating, stopping progress monitoring");
        // 不需要手动停止，让外部决定何时停止
    }
}

uint8_t ProgressFitting::calculateOverallProgress()
{
    std::lock_guard<std::mutex> lock(m_mutex);

    if (m_domainProgress.empty())
    {
        return 0;
    }

    // 计算基于域控权重的总体进度
    float totalWeightedProgress = 0.0f;

    // 遍历所有域控，计算加权总进度
    for (const auto &[ducType, domainInfo] : m_domainProgress)
    {
        // 使用域控的权重和已计算的域控进度
        totalWeightedProgress += domainInfo.domainProgress * domainInfo.weight;

        LOG_INFO(
            "Domain %d: progress=%d%%, weight=%.3f, weighted_progress=%.2f",
            static_cast<int>(ducType),
            domainInfo.domainProgress,
            domainInfo.weight,
            domainInfo.domainProgress * domainInfo.weight);
    }

    uint8_t overallProgress = totalWeightedProgress;

    // 确保进度不会倒退（除非重置）
    uint8_t currentProgress = m_currentProgress.load();
    if (overallProgress < currentProgress && overallProgress > 0)
    {
        LOG_INFO("Progress would decrease from %d%% to %d%%, keeping current",
                 currentProgress,
                 overallProgress);
        overallProgress = currentProgress;
    }

    LOG_INFO("Calculated overall progress: %d%% (total weighted progress: "
             "%.2f)",
             overallProgress,
             totalWeightedProgress);

    return overallProgress;
}

uint8_t ProgressFitting::applySmoothingAlgorithm(uint8_t newProgress)
{
    if (m_allFinished)
        return 100;
    uint8_t currentSmoothed = m_smoothedProgress.load();

    // 使用指数移动平均进行平滑
    // smoothed = α * new + (1 - α) * old
    double smoothed = m_smoothingFactor * newProgress +
                      (1.0 - m_smoothingFactor) * currentSmoothed;

    uint8_t result = static_cast<uint8_t>(std::round(smoothed));

    // 确保平滑后的进度不会超过原始进度太多
    if (result > newProgress + 5)
    {
        result = newProgress;
    }

    // 确保进度在有效范围内
    result = std::min(result, static_cast<uint8_t>(100));

    LOG_INFO("Applied smoothing: %d%% -> %d%% (factor: %f)",
             newProgress,
             result,
             m_smoothingFactor);

    return result;
}

} // namespace fotamaster
} // namespace seres