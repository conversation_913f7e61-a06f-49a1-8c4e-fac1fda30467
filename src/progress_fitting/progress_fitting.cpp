#include "ev_loop/eventloop_manager.h"
#include "logger/logger.h"
#include "progress_fitting/progress_fitting.h"

namespace seres
{
namespace fotamaster
{

ProgressFitting::ProgressFitting()
    : m_totalProgress(0), m_allFinished(false), m_reportInterval(500),
      m_initialized(false), m_running(false)
{
}

ProgressFitting::~ProgressFitting()
{
    stop();
}

bool ProgressFitting::initialize(
    const std::vector<seres::ota_duc_service::InventoryInfo> &deviceList,
    const TotalProgressCallback &callback,
    uint32_t reportInterval)
{
    std::lock_guard<std::mutex> lock(m_mutex);

    if (m_initialized)
    {
        LOG_WARN("ProgressFitting already initialized");
        return false;
    }

    if (deviceList.empty())
    {
        LOG_ERROR("Device list is empty");
        return false;
    }

    if (!callback)
    {
        LOG_ERROR("Progress callback is null");
        return false;
    }

    m_progressCallback = callback;
    m_reportInterval = reportInterval;
    m_deviceProgressList.clear();
    m_deviceIndexMap.clear();

    // 初始化设备进度列表
    size_t index = 0;
    for (const auto &device : deviceList)
    {
        DeviceProgressInfo info;
        info.deviceId = device.ecuName();
        info.deviceName = device.ecuName();
        info.progressPercent = 0;
        info.isFinished = false;
        info.isActive = false;

        // 根据设备类型设置不同的权重，可以根据实际情况调整
        // 这里假设所有设备权重相等
        info.weight = 1.0 / deviceList.size();

        // 根据设备名称判断所属域控类型，这里需要根据实际情况调整
        // 这里简单示例，实际应用中可能需要更复杂的逻辑
        if (device.ecuName().find("CDC") != std::string::npos)
        {
            info.ducType = DUCType::CDC;
        }
        else if (device.ecuName().find("MDC") != std::string::npos)
        {
            info.ducType = DUCType::MDC;
        }
        else
        {
            info.ducType = DUCType::ZCU;
        }

        m_deviceProgressList.push_back(info);
        m_deviceIndexMap[info.deviceId] = index;
        index++;
    }

    // 创建定时器
    auto eventLoop =
        base::Singleton<EventLoopManager>::Instance().GetDefaultLoop();
    if (!eventLoop)
    {
        LOG_ERROR("Failed to get event loop");
        return false;
    }

    m_timer = std::make_shared<Timer>(eventLoop);
    if (!m_timer)
    {
        LOG_ERROR("Failed to create timer");
        return false;
    }

    // 订阅各个域控的更新进度
    auto &ducManager = base::Singleton<DUCServiceManager>::Instance();
    ducManager.subscribeUpdateProgress(
        DUCType::CDC,
        [this](const seres::ota_duc_service::UpdateProgress &progress) {
            this->onUpdateProgress(DUCType::CDC, progress);
        });

    ducManager.subscribeUpdateProgress(
        DUCType::MDC,
        [this](const seres::ota_duc_service::UpdateProgress &progress) {
            this->onUpdateProgress(DUCType::MDC, progress);
        });

    ducManager.subscribeUpdateProgress(
        DUCType::ZCU,
        [this](const seres::ota_duc_service::UpdateProgress &progress) {
            this->onUpdateProgress(DUCType::ZCU, progress);
        });

    m_initialized = true;
    LOG_INFO("ProgressFitting initialized with %zu devices",
             m_deviceProgressList.size());
    return true;
}

bool ProgressFitting::start()
{
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!m_initialized)
    {
        LOG_ERROR("ProgressFitting not initialized");
        return false;
    }

    if (m_running)
    {
        LOG_WARN("ProgressFitting already running");
        return true;
    }

    // 启动定时器，定期报告进度
    m_timer->Start(m_reportInterval, true, [this]() {
        this->onTimerCallback();
    });

    m_running = true;
    LOG_INFO("ProgressFitting started");
    return true;
}

void ProgressFitting::stop()
{
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!m_running)
    {
        return;
    }

    if (m_timer)
    {
        m_timer->Stop();
    }

    m_running = false;
    LOG_INFO("ProgressFitting stopped");
}

void ProgressFitting::reset()
{
    std::lock_guard<std::mutex> lock(m_mutex);

    for (auto &device : m_deviceProgressList)
    {
        device.progressPercent = 0;
        device.isFinished = false;
        device.isActive = false;
    }

    m_totalProgress = 0;
    m_allFinished = false;

    LOG_INFO("ProgressFitting reset");
}

uint8_t ProgressFitting::getCurrentProgress() const
{
    std::lock_guard<std::mutex> lock(m_mutex);
    return m_totalProgress;
}

bool ProgressFitting::isAllFinished() const
{
    return m_allFinished;
}

void ProgressFitting::onUpdateProgress(
    DUCType type,
    const seres::ota_duc_service::UpdateProgress &progress)
{
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!m_running)
    {
        return;
    }

    // 更新设备进度
    for (const auto &deviceProgress : progress.progressLists())
    {
        auto it = m_deviceIndexMap.find(deviceProgress.deviceId());
        if (it != m_deviceIndexMap.end())
        {
            auto &device = m_deviceProgressList[it->second];

            // 只有当设备类型匹配时才更新进度
            if (device.ducType == type)
            {
                device.progressPercent = deviceProgress.progressPercent();
                device.isActive = true;

                // 根据状态判断是否完成
                if (deviceProgress.status() ==
                    seres::ota_duc_service::DeviceUpdateStatus::SUCCESS)
                {
                    device.isFinished = true;
                    device.progressPercent = 100;
                }
                else if (deviceProgress.status() ==
                         seres::ota_duc_service::DeviceUpdateStatus::FAILURE)
                {
                    // 失败时也标记为完成，但进度保持当前值
                    device.isFinished = true;
                }

                LOG_DEBUG("Device %s progress updated: %u%%",
                          device.deviceName.c_str(),
                          device.progressPercent);
            }
        }
    }

    // 计算总进度
    calculateTotalProgress();
}

void ProgressFitting::onTimerCallback()
{
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!m_running)
    {
        return;
    }

    // 调用回调函数报告进度
    if (m_progressCallback)
    {
        m_progressCallback(m_totalProgress, m_allFinished);
    }
}

void ProgressFitting::calculateTotalProgress()
{
    // 计算总进度的算法
    // 1. 基于权重的加权平均
    double weightedProgress = 0.0;
    double totalWeight = 0.0;
    bool allFinished = true;

    // 计算活动设备的加权进度
    for (const auto &device : m_deviceProgressList)
    {
        if (device.isActive)
        {
            weightedProgress += device.progressPercent * device.weight;
            totalWeight += device.weight;
        }

        // 检查是否所有设备都已完成
        if (!device.isFinished)
        {
            allFinished = false;
        }
    }

    // 如果没有活动设备，总进度为0
    if (totalWeight > 0)
    {
        m_totalProgress = static_cast<uint8_t>(weightedProgress / totalWeight);
    }
    else
    {
        m_totalProgress = 0;
    }

    // 更新是否所有设备都已完成
    m_allFinished = allFinished;

    LOG_DEBUG("Total progress: %u%%, All finished: %s",
              m_totalProgress,
              m_allFinished ? "true" : "false");
}

} // namespace fotamaster
} // namespace seres