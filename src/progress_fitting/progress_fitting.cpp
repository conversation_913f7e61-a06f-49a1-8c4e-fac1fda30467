#include "ev_loop/eventloop_manager.h"
#include "progress_fitting/progress_fitting.h"

#include <algorithm>
#include <cmath>

namespace seres
{
namespace fotamaster
{

ProgressFitting::~ProgressFitting()
{
    Stop();
}

bool ProgressFitting::Initialize(
    const std::vector<InventoryInfo> &updateDevices,
    ProgressCallback progressCallback,
    double reportInterval)
{
    std::lock_guard<std::mutex> lock(m_mutex);

    if (m_initialized.load())
    {
        LOG_WARN("ProgressFitting already initialized");
        return false;
    }

    if (updateDevices.empty())
    {
        LOG_ERROR("Update devices list is empty");
        return false;
    }

    if (!progressCallback)
    {
        LOG_ERROR("Progress callback is null");
        return false;
    }

    if (reportInterval <= 0.0)
    {
        LOG_ERROR("Invalid report interval: %f", reportInterval);
        return false;
    }

    // 保存配置
    m_updateDevices = updateDevices;
    m_progressCallback = progressCallback;
    m_reportInterval = reportInterval;

    // 初始化域控进度信息
    m_domainProgress.clear();
    m_deviceToDUCMap.clear();

    // 根据设备名称推断域控类型并初始化
    for (const auto &device : m_updateDevices)
    {
        DUCType ducType = getDUCTypeByDeviceName(device.ecuName());
        m_deviceToDUCMap[device.ecuName()] = ducType;

        // 初始化域控信息
        if (m_domainProgress.find(ducType) == m_domainProgress.end())
        {
            DomainProgressInfo domainInfo;
            domainInfo.ducType = ducType;
            domainInfo.domainProgress = 0;
            domainInfo.allFinished = false;
            domainInfo.lastUpdateTime = std::chrono::steady_clock::now();
            m_domainProgress[ducType] = domainInfo;
        }

        // 初始化设备信息
        DeviceProgressInfo deviceInfo;
        deviceInfo.deviceId = device.serialNumber();
        deviceInfo.deviceName = device.ecuName();
        deviceInfo.currentProgress = 0;
        deviceInfo.status = DeviceUpdateStatus::IDLE;
        deviceInfo.lastUpdateTime = std::chrono::steady_clock::now();
        deviceInfo.isActive = true;

        m_domainProgress[ducType].devices[device.serialNumber()] = deviceInfo;
    }

    // 创建定时器
    auto eventLoop =
        base::Singleton<EventLoopManager>::Instance().GetDefaultLoop();
    if (!eventLoop)
    {
        LOG_ERROR("Failed to get event loop");
        return false;
    }

    m_timer = std::make_shared<Timer>(eventLoop);
    if (!m_timer)
    {
        LOG_ERROR("Failed to create timer");
        return false;
    }

    // 订阅各个域控的更新进度
    auto &ducManager = base::Singleton<DUCServiceManager>::Instance();
    ducManager.subscribeUpdateProgress(DUCType::CDC,
                                       [this](const UpdateProgress &progress) {
                                           this->onUpdateProgress(DUCType::CDC,
                                                                  progress);
                                       });

    ducManager.subscribeUpdateProgress(DUCType::MDC,
                                       [this](const UpdateProgress &progress) {
                                           this->onUpdateProgress(DUCType::MDC,
                                                                  progress);
                                       });

    ducManager.subscribeUpdateProgress(DUCType::ZCU,
                                       [this](const UpdateProgress &progress) {
                                           this->onUpdateProgress(DUCType::ZCU,
                                                                  progress);
                                       });

    // 重置进度状态
    m_currentProgress.store(0);
    m_smoothedProgress.store(0);
    m_allFinished.store(false);

    m_initialized.store(true);
    LOG_INFO("ProgressFitting initialized successfully with %zu devices",
             updateDevices.size());

    return true;
}

bool ProgressFitting::Start()
{
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!m_initialized.load())
    {
        LOG_ERROR("ProgressFitting not initialized");
        return false;
    }

    if (m_started.load())
    {
        LOG_WARN("ProgressFitting already started");
        return true;
    }

    // 设置定时器回调
    auto result = m_timer->SetCallback([this]() { this->onTimerCallback(); });

    if (result.has_value())
    {
        LOG_ERROR("Failed to set timer callback: %d",
                  static_cast<int>(result.value()));
        return false;
    }

    // 启动定时器
    result = m_timer->After(m_reportInterval);
    if (result.has_value())
    {
        LOG_ERROR("Failed to start timer: %d",
                  static_cast<int>(result.value()));
        return false;
    }

    m_started.store(true);
    LOG_INFO("ProgressFitting started with report interval: %f seconds",
             m_reportInterval);

    return true;
}

void ProgressFitting::Stop()
{
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!m_started.load())
    {
        return;
    }

    // 停止定时器
    if (m_timer)
    {
        m_timer->Cancel();
    }

    m_started.store(false);
    LOG_INFO("ProgressFitting stopped");
}

void ProgressFitting::Reset()
{
    std::lock_guard<std::mutex> lock(m_mutex);

    // 重置所有进度状态
    for (auto &[ducType, domainInfo] : m_domainProgress)
    {
        domainInfo.domainProgress = 0;
        domainInfo.allFinished = false;
        domainInfo.lastUpdateTime = std::chrono::steady_clock::now();

        for (auto &[deviceId, deviceInfo] : domainInfo.devices)
        {
            deviceInfo.currentProgress = 0;
            deviceInfo.status = DeviceUpdateStatus::IDLE;
            deviceInfo.lastUpdateTime = std::chrono::steady_clock::now();
        }
    }

    m_currentProgress.store(0);
    m_smoothedProgress.store(0);
    m_allFinished.store(false);

    LOG_INFO("ProgressFitting reset");
}

uint8_t ProgressFitting::GetCurrentProgress() const
{
    return m_smoothedProgress.load();
}

bool ProgressFitting::IsAllFinished() const
{
    return m_allFinished.load();
}

void ProgressFitting::SetSmoothingFactor(double smoothingFactor)
{
    std::lock_guard<std::mutex> lock(m_mutex);

    if (smoothingFactor < 0.0 || smoothingFactor > 1.0)
    {
        LOG_WARN("Invalid smoothing factor: %f, using default 0.3",
                 smoothingFactor);
        m_smoothingFactor = 0.3;
    }
    else
    {
        m_smoothingFactor = smoothingFactor;
        LOG_INFO("Smoothing factor set to: %f", m_smoothingFactor);
    }
}