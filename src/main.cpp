#include "base/singleton.h"
#include "ev_loop/eventloop_manager.h"
#include "ev_loop/signal_manager.h"
#include "logger/logger.h"
#include "state_machine/ota_state_machine.h"
#include <chrono>
#include <iostream>
#include <signal.h>
#include <thread>
#include <string>

using namespace seres::fotamaster;

static void logger_init()
{
#if 0
    // 配置日志系统
    LogConfig config;
    config.log_file_path = "logs/ota_master.log"; // 日志文件路径
    config.max_file_size = 5 * 1024 * 1024;       // 5MB
    config.max_files = 3;                         // 最多保留3个文件
    config.console_output = true;                 // 输出到控制台
    config.log_level = LogLevel::kLogLevelDebug;  // 日志级别

    // 初始化日志系统
    base::Singleton<Logger>::Instance().Init(config);
#else
    std::string configRealpath = LOGGER_CFG_FILE_PATH_PERFIX + std::string("/logger_config.json");
    std::cout << "configRealpath: " << configRealpath << std::endl;
    // 初始化日志系统
    base::Singleton<Logger>::Instance().Init(std::move(configRealpath));
#endif
}

static void signal_handler(EventLoop *loop)
{
    base::Singleton<SignalManager>::Instance(loop).RegisterHandler(
        SIGTERM,
        [loop](int) {
            LOG_INFO("Process SIGTERM signal ,terminate ota master node...");
            loop->TerminateLoop();
        });

    base::Singleton<SignalManager>::Instance(loop).RegisterHandler(
        SIGINT,
        [loop](int) {
            LOG_INFO("Process SIGINT signal ,terminate ota master node...");
            loop->TerminateLoop();
        });
}

int main(int argc, char *argv[])
{
    (void)argc;
    (void)argv;

    // 初始化日志
    logger_init();

    auto loop = base::Singleton<EventLoopManager>::Instance().GetDefaultLoop();
    assert(loop);

    signal_handler(loop);

    auto ota_sm = std::make_shared<OtaStateMachine>(loop);
    assert(ota_sm);
    ota_sm->ChangeState("IdleState");

    loop->LoopForever();
    return 0;
}