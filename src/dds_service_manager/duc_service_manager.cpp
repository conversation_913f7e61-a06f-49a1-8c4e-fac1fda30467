#include "dds_service_manager/duc_service_manager.h"
#include <chrono>
#include <thread>

namespace seres
{
namespace fotamaster
{

std::string ducTypeToString(DUCType type)
{
    switch (type)
    {
    case DUCType::CDC:
        return "CDC";
    case DUCType::MDC:
        return "MDC";
    case DUCType::ZCU:
        return "ZCU";
    default:
        return "UNKNOWN";
    }
}
std::string returnCodeToString(ReturnCode code)
{
    switch (code)
    {
    case ReturnCode::OK:
        return "OK";
    case ReturnCode::ERROR:
        return "ERROR";
    case ReturnCode::REFUSED:
        return "REFUSED";
    default:
        return "UNKNOWN"; // 防御性处理
    }
}

DUCServiceManager::DUCServiceManager() : m_initialized(false)
{
}

DUCServiceManager::~DUCServiceManager()
{
    shutdown();
}

bool DUCServiceManager::initialize(int domainId)
{
    std::lock_guard<std::mutex> lock(m_mutex);

    if (m_initialized)
    {
        LOG_WARN("DUCServiceManager already initialized");
        return true;
    }

    try
    {
        LOG_INFO("Initializing DUCServiceManager, domain ID: %d", domainId);
        m_participant =
            std::make_shared<dds::domain::DomainParticipant>(domainId);
        m_subscriber = std::make_shared<dds::sub::Subscriber>(*m_participant);

        m_initialized = true;
        return true;
    }
    catch (const dds::core::Exception &e)
    {
        LOG_ERROR("Failed to initialize DUCServiceManager: %s", e.what());
        return false;
    }
}

bool DUCServiceManager::createClient(DUCType type,
                                     std::string serviceName,
                                     ServiceStatusCallback /* statusCallback */)
{
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!m_initialized)
    {
        LOG_ERROR("DUCServiceManager not initialized");
        return false;
    }

    try
    {
        // 检查是否已经创建
        if (m_clients.find(type) != m_clients.end() ||
            m_TopicSubscribers.find(type) != m_TopicSubscribers.end())
        {
            LOG_WARN("Client already exists: %s",
                     ducTypeToString(type).c_str());
            return true;
        }

        //创建rpc客户端
        if (serviceName.empty())
            serviceName = getServiceName(type);
        auto client = std::make_unique<DUCServiceClient>(type,
                                                         serviceName,
                                                         *m_participant);
        m_clients[type] = std::move(client);

        //订阅话题
        m_TopicSubscribers[type] =
            std::make_unique<TopicSubscriber<OTA_DucDataUnion>>(
                *m_participant,
                getTopicName(type),
                [this, type](const OTA_DucDataUnion &data) {
                    onTopicDataReceived(type, data);
                },
                m_subscriber);
        return true;
    }
    catch (const dds::core::Exception &e)
    {
        LOG_ERROR("Failed to create client: %s", e.what());
        return false;
    }
}

void DUCServiceManager::onTopicDataReceived(DUCType type,
                                            const OTA_DucDataUnion &data)
{
    switch (data._d())
    {
    case OTA_TopicData::INVENTORY_RESULT:
        LOG_INFO("Received inventory result from DUC=%s",
                 ducTypeToString(type).c_str());
        m_InventoryResultCallbacks[type](data.inventoryResult());
        break;
    case OTA_TopicData::DOWNLOAD_PROGRESS:
        LOG_INFO("Received download progress from DUC=%s",
                 ducTypeToString(type).c_str());
        m_DownloadProgressCallbacks[type](data.downloadProgress());
        break;
    case OTA_TopicData::UZIP_PACKAGES_RESULT:
        LOG_INFO("Received unzip packages result from DUC=%s",
                 ducTypeToString(type).c_str());
        m_UzipPackagesResultCallbacks[type](data.uzipPackagesResult());
        break;
    case OTA_TopicData::PACKAGES_VERIFY_RESULT:
        LOG_INFO("Received packages verify result from DUC=%s",
                 ducTypeToString(type).c_str());
        m_PackagesVerifyResultCallbacks[type](data.packagesVerifyResult());
        break;
    case OTA_TopicData::CHECK_UPDATE_CONDITION_RESULT:
        LOG_INFO("Received check update condition result from DUC=%s",
                 ducTypeToString(type).c_str());
        m_CheckUpdateConditionResultCallbacks[type](
            data.checkUpdateConditionResult());
        break;
    case OTA_TopicData::UPDATE_PROGRESS:
        LOG_INFO("Received update progress from DUC=%s",
                 ducTypeToString(type).c_str());
        m_UpdateProgressCallbacks[type](data.updateProgress());
        break;
    default:
        LOG_ERROR("Received unknown topic data from DUC=%s",
                  ducTypeToString(type).c_str());
        break;
    }
}

bool DUCServiceManager::subscribeInventoryResult(
    DUCType type,
    const InventoryResultCallback &callback)
{
    std::lock_guard<std::mutex> lock(m_mutex);

    m_InventoryResultCallbacks[type] = callback;
    return true;
}

bool DUCServiceManager::subscribeDownloadProgress(
    DUCType type,
    const DownloadProgressCallback &callback)
{
    std::lock_guard<std::mutex> lock(m_mutex);

    m_DownloadProgressCallbacks[type] = callback;
    return true;
}
bool DUCServiceManager::subscribeUpdateProgress(
    DUCType type,
    const UpdateProgressCallback &callback)
{
    std::lock_guard<std::mutex> lock(m_mutex);

    m_UpdateProgressCallbacks[type] = callback;
    return true;
}

bool DUCServiceManager::subscribeUzipPackagesResult(
    DUCType type,
    const UzipPackagesResultCallback &callback)
{
    std::lock_guard<std::mutex> lock(m_mutex);

    m_UzipPackagesResultCallbacks[type] = callback;
    return true;
}

bool DUCServiceManager::subscribePackagesVerifyResult(
    DUCType type,
    const PackagesVerifyResultCallback &callback)
{
    std::lock_guard<std::mutex> lock(m_mutex);

    m_PackagesVerifyResultCallbacks[type] = callback;
    return true;
}

bool DUCServiceManager::subscribeCheckUpdateConditionResult(
    DUCType type,
    const CheckUpdateConditionResultCallback &callback)
{
    std::lock_guard<std::mutex> lock(m_mutex);

    m_CheckUpdateConditionResultCallbacks[type] = callback;
    return true;
}
DucServiceInterfaceClient *DUCServiceManager::getDUCClient(DUCType type)
{
    auto it = m_clients.find(type);
    if (it == m_clients.end())
    {
        LOG_ERROR("Client does not exist: %s", ducTypeToString(type).c_str());
        return nullptr;
    }
    return it->second->getClient();
}

bool DUCServiceManager::checkClientExist(DUCType type)
{
    return m_clients.find(type) != m_clients.end();
}

bool DUCServiceManager::checkClientConnected(DUCType type)
{
    auto it = m_clients.find(type);
    if (it == m_clients.end())
    {
        LOG_ERROR("Client does not exist: %s", ducTypeToString(type).c_str());
        return false;
    }

    if (!it->second->isConnected())
    {
        LOG_ERROR("Client not connected: %s", ducTypeToString(type).c_str());
        return false;
    }

    return true;
}

std::string DUCServiceManager::getServiceName(DUCType type)
{
    switch (type)
    {
    case DUCType::CDC:
        return CDC_SERVICE_NAME;
    case DUCType::MDC:
        return MDC_SERVICE_NAME;
    case DUCType::ZCU:
        return ZCU_SERVICE_NAME;
    default:
        return "";
    }
}

std::string DUCServiceManager::getTopicName(DUCType type)
{
    switch (type)
    {
    case DUCType::CDC:
        return CDC_TOPIC_NAME;
    case DUCType::MDC:
        return MDC_TOPIC_NAME;
    case DUCType::ZCU:
        return ZCU_TOPIC_NAME;
    default:
        return "";
    }
}

// Asset information retrieval related methods
ReturnCode DUCServiceManager::inventoryCollection(
    DUCType type,
    const SelectedInventoryList &inventory_list)
{
    if (m_clients[type] == nullptr || !m_clients[type]->isConnected())
    {
        LOG_ERROR("client not create or server is not online");
        return ReturnCode::ERROR;
    }
    auto client = getDUCClient(type);
    try
    {
        ReturnCode ret = client->inventoryCollection(inventory_list);
        LOG_INFO("Requested asset information check from DUC=%s: result=%d",
                 ducTypeToString(type).c_str(),
                 static_cast<int>(ret));
        return ret;
    }
    catch (const dds::core::Exception &e)
    {
        LOG_ERROR("Exception occurred while requesting asset information check "
                  "from DUC=%s: %s",
                  ducTypeToString(type).c_str(),
                  e.what());
        return ReturnCode::ERROR;
    }
}

ReturnCode DUCServiceManager::stopInventoryCollection(DUCType type)
{
    if (m_clients[type] == nullptr || !m_clients[type]->isConnected())
    {
        LOG_ERROR("client not create or server is not online");
        return ReturnCode::ERROR;
    }
    auto client = getDUCClient(type);
    try
    {
        ReturnCode ret = client->stopInventoryCollection();
        LOG_INFO("Sent stop asset information collection request to DUC=%s: "
                 "result=%d",
                 ducTypeToString(type).c_str(),
                 static_cast<int>(ret));
        return ret;
    }
    catch (const dds::core::Exception &e)
    {
        LOG_ERROR("Exception occurred while sending stop asset information "
                  "collection request to DUC=%s: %s",
                  ducTypeToString(type).c_str(),
                  e.what());
        return ReturnCode::ERROR;
    }
}

ReturnCode DUCServiceManager::getInventoryResult(DUCType type,
                                                 InventoryResult &result)
{
    if (m_clients[type] == nullptr || !m_clients[type]->isConnected())
    {
        LOG_ERROR("client not create or server is not online");
        return ReturnCode::ERROR;
    }
    auto client = getDUCClient(type);
    try
    {
        ReturnCode ret = client->getInventoryResult(result);
        LOG_INFO("Retrieved asset information result from DUC=%s: result=%d, "
                 "count=%zu",
                 ducTypeToString(type).c_str(),
                 static_cast<int>(ret),
                 result.InventoryLists().size());
        return ret;
    }
    catch (const dds::core::Exception &e)
    {
        LOG_ERROR("Exception occurred while retrieving asset information "
                  "result from DUC=%s: %s",
                  ducTypeToString(type).c_str(),
                  e.what());
        return ReturnCode::ERROR;
    }
}

// Download pre-check related methods
ReturnCode DUCServiceManager::checkDownloadCondition(
    DUCType type,
    const DownloadConditionLists &conditions,
    DownloadConditionResult &result)
{
    if (m_clients[type] == nullptr || !m_clients[type]->isConnected())
    {
        LOG_ERROR("client not create or server is not online");
        return ReturnCode::ERROR;
    }
    auto client = getDUCClient(type);
    try
    {
        ReturnCode ret = client->checkDownloadCondition(conditions, result);
        LOG_INFO("Sent download condition check request to DUC=%s: result=%d, "
                 "check result=%d",
                 ducTypeToString(type).c_str(),
                 static_cast<int>(ret),
                 static_cast<int>(result));
        return ret;
    }
    catch (const dds::core::Exception &e)
    {
        LOG_ERROR("Exception occurred while sending download condition check "
                  "request to DUC=%s: %s",
                  ducTypeToString(type).c_str(),
                  e.what());
        return ReturnCode::ERROR;
    }
}

// Download related methods
ReturnCode DUCServiceManager::startDownload(DUCType type,
                                            const DownloadTaskLists &tasks)
{
    if (m_clients[type] == nullptr || !m_clients[type]->isConnected())
    {
        LOG_ERROR("client not create or server is not online");
        return ReturnCode::ERROR;
    }
    auto client = getDUCClient(type);
    try
    {
        ReturnCode ret = client->startDownload(tasks);
        LOG_INFO("Sent start download request to DUC=%s: result=%d, task "
                 "count=%zu",
                 ducTypeToString(type).c_str(),
                 static_cast<int>(ret),
                 tasks.taskLists().size());
        return ret;
    }
    catch (const dds::core::Exception &e)
    {
        LOG_ERROR("Exception occurred while sending start download request to "
                  "DUC=%s: %s",
                  ducTypeToString(type).c_str(),
                  e.what());
        return ReturnCode::ERROR;
    }
}

ReturnCode DUCServiceManager::downloadCtrl(DUCType type, DownloadCtrl command)
{
    if (m_clients[type] == nullptr || !m_clients[type]->isConnected())
    {
        LOG_ERROR("client not create or server is not online");
        return ReturnCode::ERROR;
    }
    auto client = getDUCClient(type);
    try
    {
        ReturnCode ret = client->downloadCtrl(command);
        LOG_INFO("Sent download control request to DUC=%s: command=%d, "
                 "result=%d",
                 ducTypeToString(type).c_str(),
                 static_cast<int>(command),
                 static_cast<int>(ret));
        return ret;
    }
    catch (const dds::core::Exception &e)
    {
        LOG_ERROR("Exception occurred while sending download control request "
                  "to DUC=%s: %s",
                  ducTypeToString(type).c_str(),
                  e.what());
        return ReturnCode::ERROR;
    }
}

ReturnCode DUCServiceManager::getDownloadProgress(
    DUCType type,
    DownloadProgress &download_progress)
{
    if (m_clients[type] == nullptr || !m_clients[type]->isConnected())
    {
        LOG_ERROR("client not create or server is not online");
        return ReturnCode::ERROR;
    }
    auto client = getDUCClient(type);
    try
    {
        ReturnCode ret = client->getDownloadProgress(download_progress);
        LOG_INFO("Retrieved download progress from DUC=%s: result=%d, all "
                 "finished=%d",
                 ducTypeToString(type).c_str(),
                 static_cast<int>(ret),
                 download_progress.allFinished());
        return ret;
    }
    catch (const dds::core::Exception &e)
    {
        LOG_ERROR("Exception occurred while retrieving download progress from "
                  "DUC=%s: %s",
                  ducTypeToString(type).c_str(),
                  e.what());
        return ReturnCode::ERROR;
    }
}

// Unzip related methods
ReturnCode DUCServiceManager::uzipPackages(DUCType type)
{
    if (m_clients[type] == nullptr || !m_clients[type]->isConnected())
    {
        LOG_ERROR("client not create or server is not online");
        return ReturnCode::ERROR;
    }
    auto client = getDUCClient(type);
    try
    {
        ReturnCode ret = client->uzipPackages();
        LOG_INFO("Sent unzip request to DUC=%s: result=%d",
                 ducTypeToString(type).c_str(),
                 static_cast<int>(ret));
        return ret;
    }
    catch (const dds::core::Exception &e)
    {
        LOG_ERROR(
            "Exception occurred while sending unzip request to DUC=%s: %s",
            ducTypeToString(type).c_str(),
            e.what());
        return ReturnCode::ERROR;
    }
}

ReturnCode DUCServiceManager::getuzipPackagesResult(
    DUCType type,
    UzipPackagesResult &uzip_Result)
{
    if (m_clients[type] == nullptr || !m_clients[type]->isConnected())
    {
        LOG_ERROR("client not create or server is not online");
        return ReturnCode::ERROR;
    }
    auto client = getDUCClient(type);
    try
    {
        ReturnCode ret = client->getuzipPackagesResult(uzip_Result);
        LOG_INFO("Retrieved unzip result from DUC=%s: result=%d, unzip "
                 "success=%d",
                 ducTypeToString(type).c_str(),
                 static_cast<int>(ret),
                 uzip_Result.successed());
        if (!uzip_Result.successed())
        {
            LOG_ERROR("Unzip failed reason: %s",
                      uzip_Result.errorMsg().c_str());
        }
        return ret;
    }
    catch (const dds::core::Exception &e)
    {
        LOG_ERROR("Exception occurred while retrieving unzip result from "
                  "DUC=%s: %s",
                  ducTypeToString(type).c_str(),
                  e.what());
        return ReturnCode::ERROR;
    }
}

// Package verification related methods
ReturnCode DUCServiceManager::startPackagesVerify(DUCType type)
{
    if (m_clients[type] == nullptr || !m_clients[type]->isConnected())
    {
        LOG_ERROR("client not create or server is not online");
        return ReturnCode::ERROR;
    }
    auto client = getDUCClient(type);
    try
    {
        ReturnCode ret = client->startPackagesVerify();
        LOG_INFO("Sent package verification request to DUC=%s: result=%d",
                 ducTypeToString(type).c_str(),
                 static_cast<int>(ret));
        return ret;
    }
    catch (const dds::core::Exception &e)
    {
        LOG_ERROR("Exception occurred while sending package verification "
                  "request to DUC=%s: %s",
                  ducTypeToString(type).c_str(),
                  e.what());
        return ReturnCode::ERROR;
    }
}

ReturnCode DUCServiceManager::getPackagesVerifyResult(
    DUCType type,
    PackagesVerifyResult &verify_Result)
{
    if (m_clients[type] == nullptr || !m_clients[type]->isConnected())
    {
        LOG_ERROR("client not create or server is not online");
        return ReturnCode::ERROR;
    }
    auto client = getDUCClient(type);
    try
    {
        ReturnCode ret = client->getPackagesVerifyResult(verify_Result);
        LOG_INFO("Retrieved package verification result from DUC=%s: "
                 "result=%d, verification success=%d",
                 ducTypeToString(type).c_str(),
                 static_cast<int>(ret),
                 verify_Result.successed());
        if (!verify_Result.successed())
        {
            LOG_ERROR("Verification failed reason: %s",
                      verify_Result.errorMsg().c_str());
        }
        return ret;
    }
    catch (const dds::core::Exception &e)
    {
        LOG_ERROR("Exception occurred while retrieving package verification "
                  "result from DUC=%s: %s",
                  ducTypeToString(type).c_str(),
                  e.what());
        return ReturnCode::ERROR;
    }
}

// Update related methods
ReturnCode DUCServiceManager::checkUpdateCondition(DUCType type)
{
    if (m_clients[type] == nullptr || !m_clients[type]->isConnected())
    {
        LOG_ERROR("client not create or server is not online");
        return ReturnCode::ERROR;
    }
    auto client = getDUCClient(type);
    try
    {
        ReturnCode ret = client->checkUpdateCondition();
        LOG_INFO("Sent update condition check request to DUC=%s: result=%d",
                 ducTypeToString(type).c_str(),
                 static_cast<int>(ret));
        return ret;
    }
    catch (const dds::core::Exception &e)
    {
        LOG_ERROR("Exception occurred while sending update condition check "
                  "request to DUC=%s: %s",
                  ducTypeToString(type).c_str(),
                  e.what());
        return ReturnCode::ERROR;
    }
}

ReturnCode DUCServiceManager::getCheckUpdateConditionResult(
    DUCType type,
    CheckUpdateConditionResult &checkcondition_Result)
{
    if (m_clients[type] == nullptr || !m_clients[type]->isConnected())
    {
        LOG_ERROR("client not create or server is not online");
        return ReturnCode::ERROR;
    }
    auto client = getDUCClient(type);
    try
    {
        ReturnCode ret =
            client->getCheckUpdateConditionResult(checkcondition_Result);
        LOG_INFO("Retrieved update condition check result from DUC=%s: "
                 "result=%d, passed=%d, error code=%d",
                 ducTypeToString(type).c_str(),
                 static_cast<int>(ret),
                 checkcondition_Result.passed(),
                 static_cast<int>(checkcondition_Result.errorCode()));
        return ret;
    }
    catch (const dds::core::Exception &e)
    {
        LOG_ERROR("Exception occurred while retrieving update condition check "
                  "result from DUC=%s: %s",
                  ducTypeToString(type).c_str(),
                  e.what());
        return ReturnCode::ERROR;
    }
}

ReturnCode DUCServiceManager::startUpdate(DUCType type,
                                          UpdateMode mode,
                                          const UpdateDeviceList &update_list)
{
    if (m_clients[type] == nullptr || !m_clients[type]->isConnected())
    {
        LOG_ERROR("client not create or server is not online");
        return ReturnCode::ERROR;
    }
    auto client = getDUCClient(type);
    try
    {
        ReturnCode ret = client->startUpdate(mode, update_list);
        LOG_INFO("Sent start update request to DUC=%s: mode=%d, device "
                 "count=%zu, result=%d",
                 ducTypeToString(type).c_str(),
                 static_cast<int>(mode),
                 update_list.updateDeviceLists().size(),
                 static_cast<int>(ret));
        return ret;
    }
    catch (const dds::core::Exception &e)
    {
        LOG_ERROR("Exception occurred while sending start update request to "
                  "DUC=%s: %s",
                  ducTypeToString(type).c_str(),
                  e.what());
        return ReturnCode::ERROR;
    }
}

ReturnCode DUCServiceManager::getUpdateProgress(DUCType type,
                                                UpdateProgress &updateProgress)
{
    if (m_clients[type] == nullptr || !m_clients[type]->isConnected())
    {
        LOG_ERROR("client not create or server is not online");
        return ReturnCode::ERROR;
    }
    auto client = getDUCClient(type);
    try
    {
        ReturnCode ret = client->getUpdateProgress(updateProgress);
        LOG_INFO("Sent get progress request to DUC=%s: result=%d",
                 ducTypeToString(type).c_str(),
                 static_cast<int>(ret));
        return ret;
    }
    catch (const dds::core::Exception &e)
    {
        LOG_ERROR("Exception occurred while getting progress request from "
                  "DUC=%s: %s",
                  ducTypeToString(type).c_str(),
                  e.what());
        return ReturnCode::ERROR;
    }
}

ReturnCode DUCServiceManager::resumeUpdate(DUCType type)
{
    if (m_clients[type] == nullptr || !m_clients[type]->isConnected())
    {
        LOG_ERROR("client not create or server is not online");
        return ReturnCode::ERROR;
    }
    auto client = getDUCClient(type);
    try
    {
        ReturnCode ret = client->resumeUpdate();
        LOG_INFO("Sent resume update request to DUC=%s: result=%d",
                 ducTypeToString(type).c_str(),
                 static_cast<int>(ret));
        return ret;
    }
    catch (const dds::core::Exception &e)
    {
        LOG_ERROR("Exception occurred while sending resume update request to "
                  "DUC=%s: %s",
                  ducTypeToString(type).c_str(),
                  e.what());
        return ReturnCode::ERROR;
    }
}

ReturnCode DUCServiceManager::pauseUpdate(DUCType type)
{
    if (m_clients[type] == nullptr || !m_clients[type]->isConnected())
    {
        LOG_ERROR("client not create or server is not online");
        return ReturnCode::ERROR;
    }
    auto client = getDUCClient(type);
    try
    {
        ReturnCode ret = client->pauseUpdate();
        LOG_INFO("Sent pause update request to DUC=%s: result=%d",
                 ducTypeToString(type).c_str(),
                 static_cast<int>(ret));
        return ret;
    }
    catch (const dds::core::Exception &e)
    {
        LOG_ERROR("Exception occurred while sending pause update request to "
                  "DUC=%s: %s",
                  ducTypeToString(type).c_str(),
                  e.what());
        return ReturnCode::ERROR;
    }
}

// Activation method
ReturnCode DUCServiceManager::activate(DUCType type)
{
    if (m_clients[type] == nullptr || !m_clients[type]->isConnected())
    {
        LOG_ERROR("client not create or server is not online");
        return ReturnCode::ERROR;
    }
    auto client = getDUCClient(type);
    try
    {
        ReturnCode ret = client->activate();
        LOG_INFO("Sent activation request to DUC=%s: result=%d",
                 ducTypeToString(type).c_str(),
                 static_cast<int>(ret));
        return ret;
    }
    catch (const dds::core::Exception &e)
    {
        LOG_ERROR("Exception occurred while sending activation request to "
                  "DUC=%s: %s",
                  ducTypeToString(type).c_str(),
                  e.what());
        return ReturnCode::ERROR;
    }
}

// Rollback related methods
ReturnCode DUCServiceManager::rollback(DUCType type,
                                       const RollbackComponentList &components)
{
    if (m_clients[type] == nullptr || !m_clients[type]->isConnected())
    {
        LOG_ERROR("client not create or server is not online");
        return ReturnCode::ERROR;
    }
    auto client = getDUCClient(type);
    try
    {
        ReturnCode ret = client->rollback(components);
        LOG_INFO("Sent rollback request to DUC=%s: component count=%zu, "
                 "result=%d",
                 ducTypeToString(type).c_str(),
                 components.rollbackLists().size(),
                 static_cast<int>(ret));
        return ret;
    }
    catch (const dds::core::Exception &e)
    {
        LOG_ERROR("Exception occurred while sending rollback request to "
                  "DUC=%s: %s",
                  ducTypeToString(type).c_str(),
                  e.what());
        return ReturnCode::ERROR;
    }
}

ReturnCode DUCServiceManager::getRollbackProgress(
    DUCType type,
    UpdateProgress &rollbackProgress)
{
    if (m_clients[type] == nullptr || !m_clients[type]->isConnected())
    {
        LOG_ERROR("client not create or server is not online");
        return ReturnCode::ERROR;
    }
    auto client = getDUCClient(type);
    try
    {
        ReturnCode ret = client->getRollbackProgress(rollbackProgress);
        LOG_INFO("Sent get rollback progress request to DUC=%s: result=%d",
                 ducTypeToString(type).c_str(),
                 static_cast<int>(ret));
        return ret;
    }
    catch (const dds::core::Exception &e)
    {
        LOG_ERROR("Exception occurred while getting rollback progress request "
                  "from DUC=%s: %s",
                  ducTypeToString(type).c_str(),
                  e.what());
        return ReturnCode::ERROR;
    }
}

// Log upload method
ReturnCode DUCServiceManager::uploadLog(DUCType type)
{
    if (m_clients[type] == nullptr || !m_clients[type]->isConnected())
    {
        LOG_ERROR("client not create or server is not online");
        return ReturnCode::ERROR;
    }
    auto client = getDUCClient(type);
    try
    {
        ReturnCode ret = client->uploadLog();
        LOG_INFO("Sent log upload request to DUC=%s: result=%d",
                 ducTypeToString(type).c_str(),
                 static_cast<int>(ret));
        return ret;
    }
    catch (const dds::core::Exception &e)
    {
        LOG_ERROR("Exception occurred while sending log upload request to "
                  "DUC=%s: %s",
                  ducTypeToString(type).c_str(),
                  e.what());
        return ReturnCode::ERROR;
    }
}

void DUCServiceManager::shutdown()
{
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!m_initialized)
    {
        return;
    }

    LOG_INFO("Shutting down DUCServiceManager");

    // Clean up subscriptions
    m_TopicSubscribers.clear();

    // Clean up clients
    m_clients.clear();

    m_initialized = false;
}

} // namespace fotamaster
} // namespace seres