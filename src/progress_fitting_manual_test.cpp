/**
 * @file progress_fitting_manual_test.cpp
 * @brief 进度拟合模块手动测试程序
 *
 * 本程序用于手动测试进度计算的准确性，验证权重分配和进度计算算法。
 */

#include "ev_loop/eventloop_manager.h"
#include "logger/logger.h"
#include "progress_fitting/progress_fitting.h"

#include <chrono>
#include <iomanip>
#include <iostream>
#include <thread>

using namespace seres::fotamaster;
using namespace seres::ota_duc_service;

class ProgressTestManager
{
public:
    ProgressTestManager()
    {
        // 初始化日志系统
        LogConfig logConfig;
        logConfig.console_output = true;
        logConfig.log_level = LogLevel::kLogLevelInfo;
        base::Singleton<Logger>::Instance().Init(logConfig);

        // 获取事件循环管理器（自动初始化）
        auto &eventLoopManager = base::Singleton<EventLoopManager>::Instance();
        m_eventLoop = eventLoopManager.GetDefaultLoop();

        // 初始化进度拟合模块
        m_progressFitting.Initialize(
            [this](uint8_t progress, bool allFinished) {
                this->onProgressUpdate(progress, allFinished);
            },
            0.5); // 500ms间隔
    }

    void onProgressUpdate(uint8_t progress, bool allFinished)
    {
        m_lastProgress = progress;
        m_allFinished = allFinished;

        std::cout << std::fixed << std::setprecision(1);
        std::cout << "[PROGRESS] " << static_cast<int>(progress) << "%";
        if (allFinished)
        {
            std::cout << " (ALL FINISHED)";
        }
        std::cout << std::endl;
    }

    // 创建测试设备
    std::vector<InventoryInfo> createDevices(const std::string &prefix,
                                             int count)
    {
        std::vector<InventoryInfo> devices;
        for (int i = 1; i <= count; ++i)
        {
            InventoryInfo device;
            device.ecuName(prefix + "_ECU_" + std::to_string(i));
            device.serialNumber(prefix + "_" + std::to_string(i));
            device.partNumber(prefix + "_PART_" + std::to_string(i));
            devices.push_back(device);
        }
        return devices;
    }

    // 模拟设备进度更新
    void simulateProgress(DUCType ducType,
                          const std::vector<std::string> &deviceIds,
                          const std::vector<uint8_t> &progresses,
                          const std::string &description)
    {
        std::cout << "\n[SIMULATION] " << description << std::endl;

        UpdateProgress updateProgress;
        updateProgress.allFinished(false);

        std::vector<DeviceUpdateProgress> progressList;
        for (size_t i = 0; i < deviceIds.size() && i < progresses.size(); ++i)
        {
            DeviceUpdateProgress deviceProgress;
            deviceProgress.deviceId(deviceIds[i]);
            deviceProgress.deviceName("Device_" + deviceIds[i]);
            deviceProgress.progressPercent(progresses[i]);
            deviceProgress.status(progresses[i] >= 100
                                      ? DeviceUpdateStatus::SUCCESS
                                      : DeviceUpdateStatus::UPDATING);
            progressList.push_back(deviceProgress);

            std::cout << "  Device " << deviceIds[i] << ": "
                      << static_cast<int>(progresses[i]) << "%" << std::endl;
        }

        updateProgress.progressLists(progressList);

        // 调用进度更新
        m_progressFitting.onUpdateProgress(ducType, updateProgress);

        // 等待一段时间让计算完成
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }

    // 模拟设备进度更新（带详细日志）
    void simulateProgressWithDetails(DUCType ducType,
                                     const std::vector<std::string> &deviceIds,
                                     const std::vector<uint8_t> &progresses)
    {
        std::string domainName;
        switch (ducType)
        {
        case DUCType::CDC:
            domainName = "CDC";
            break;
        case DUCType::MDC:
            domainName = "MDC";
            break;
        case DUCType::ZCU:
            domainName = "ZCU";
            break;
        default:
            domainName = "UNKNOWN";
            break;
        }

        UpdateProgress updateProgress;
        updateProgress.allFinished(false);

        std::vector<DeviceUpdateProgress> progressList;
        std::cout << "  " << domainName << "域设备进度: ";

        for (size_t i = 0; i < deviceIds.size() && i < progresses.size(); ++i)
        {
            DeviceUpdateProgress deviceProgress;
            deviceProgress.deviceId(deviceIds[i]);
            deviceProgress.deviceName("Device_" + deviceIds[i]);
            deviceProgress.progressPercent(progresses[i]);
            deviceProgress.status(progresses[i] >= 100
                                      ? DeviceUpdateStatus::SUCCESS
                                      : DeviceUpdateStatus::UPDATING);
            progressList.push_back(deviceProgress);

            std::cout << deviceIds[i] << "=" << static_cast<int>(progresses[i])
                      << "%";
            if (i < deviceIds.size() - 1)
                std::cout << ", ";
        }
        std::cout << std::endl;

        updateProgress.progressLists(progressList);

        // 调用进度更新
        m_progressFitting.onUpdateProgress(ducType, updateProgress);
    }

    void runTest1_SingleDomain()
    {
        std::cout << "\n" << std::string(60, '=') << std::endl;
        std::cout << "TEST 1: 单域控多设备权重测试" << std::endl;
        std::cout << std::string(60, '=') << std::endl;

        // 添加CDC域的3个设备
        auto cdcDevices = createDevices("CDC", 3);
        m_progressFitting.AddUpdateDevice(DUCType::CDC, cdcDevices);

        m_progressFitting.Start();

        // 测试场景1：设备进度 0%, 50%, 100%
        simulateProgress(DUCType::CDC,
                         {"CDC_1", "CDC_2", "CDC_3"},
                         {0, 50, 100},
                         "设备进度: 0%, 50%, 100%");
        std::cout << "期望域控进度: (0*0.333 + 50*0.333 + 100*0.333) = 50%"
                  << std::endl;
        std::cout << "期望总体进度: 50% * 1.0 = 50%" << std::endl;

        // 运行事件循环让定时器工作
        for (int i = 0; i < 6; ++i)
        {
            m_eventLoop->LoopOnce();
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }

        // 测试场景2：设备进度 30%, 60%, 90%
        simulateProgress(DUCType::CDC,
                         {"CDC_1", "CDC_2", "CDC_3"},
                         {30, 60, 90},
                         "设备进度: 30%, 60%, 90%");
        std::cout << "期望域控进度: (30*0.333 + 60*0.333 + 90*0.333) = 60%"
                  << std::endl;
        std::cout << "期望总体进度: 60% * 1.0 = 60%" << std::endl;

        // 运行事件循环让定时器工作
        for (int i = 0; i < 6; ++i)
        {
            m_eventLoop->LoopOnce();
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }

        m_progressFitting.Stop();
        m_progressFitting.Reset();
    }

    void runTest2_MultipleDomains()
    {
        std::cout << "\n" << std::string(60, '=') << std::endl;
        std::cout << "TEST 2: 多域控权重分配测试" << std::endl;
        std::cout << std::string(60, '=') << std::endl;

        // 添加不同数量的设备到各个域
        auto cdcDevices = createDevices("CDC", 2); // 2个CDC设备
        auto mdcDevices = createDevices("MDC", 3); // 3个MDC设备
        auto zcuDevices = createDevices("ZCU", 1); // 1个ZCU设备

        m_progressFitting.AddUpdateDevice(DUCType::CDC, cdcDevices);
        m_progressFitting.AddUpdateDevice(DUCType::MDC, mdcDevices);
        m_progressFitting.AddUpdateDevice(DUCType::ZCU, zcuDevices);

        std::cout
            << "CDC域: 2个设备，每个设备权重: 1/2 = 0.5，域权重: 1/3 = 0.333"
            << std::endl;
        std::cout
            << "MDC域: 3个设备，每个设备权重: 1/3 = 0.333，域权重: 1/3 = 0.333"
            << std::endl;
        std::cout
            << "ZCU域: 1个设备，每个设备权重: 1/1 = 1.0，域权重: 1/3 = 0.333"
            << std::endl;

        m_progressFitting.Start();

        // 设置各域控的设备进度
        simulateProgress(DUCType::CDC,
                         {"CDC_1", "CDC_2"},
                         {20, 80},
                         "CDC域设备进度: 20%, 80%");
        std::cout << "CDC域控进度: (20*0.5 + 80*0.5) = 50%" << std::endl;

        simulateProgress(DUCType::MDC,
                         {"MDC_1", "MDC_2", "MDC_3"},
                         {30, 60, 90},
                         "MDC域设备进度: 30%, 60%, 90%");
        std::cout << "MDC域控进度: (30*0.333 + 60*0.333 + 90*0.333) = 60%"
                  << std::endl;

        simulateProgress(DUCType::ZCU, {"ZCU_1"}, {75}, "ZCU域设备进度: 75%");
        std::cout << "ZCU域控进度: 75% * 1.0 = 75%" << std::endl;

        std::cout << "\n总体进度计算:" << std::endl;
        std::cout << "总体进度 = (50*0.333 + 60*0.333 + 75*0.333) = 61.67%"
                  << std::endl;

        // 运行事件循环让定时器工作
        for (int i = 0; i < 6; ++i)
        {
            m_eventLoop->LoopOnce();
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }

        m_progressFitting.Stop();
        m_progressFitting.Reset();
    }

    void runTest3_ProgressiveUpdate()
    {
        std::cout << "\n" << std::string(60, '=') << std::endl;
        std::cout << "TEST 3: 渐进式更新测试" << std::endl;
        std::cout << std::string(60, '=') << std::endl;

        // 添加设备
        auto devices = createDevices("PROG", 2);
        m_progressFitting.AddUpdateDevice(DUCType::CDC, devices);

        m_progressFitting.Start();

        // 模拟渐进式更新过程
        std::vector<std::pair<std::vector<uint8_t>, std::string>> steps = {
            {{0, 0}, "初始状态"},
            {{25, 10}, "设备1: 25%, 设备2: 10% -> 期望: 17.5%"},
            {{50, 30}, "设备1: 50%, 设备2: 30% -> 期望: 40%"},
            {{75, 60}, "设备1: 75%, 设备2: 60% -> 期望: 67.5%"},
            {{100, 90}, "设备1: 100%, 设备2: 90% -> 期望: 95%"},
            {{100, 100}, "设备1: 100%, 设备2: 100% -> 期望: 100%"}};

        for (const auto &step : steps)
        {
            simulateProgress(DUCType::CDC,
                             {"PROG_1", "PROG_2"},
                             step.first,
                             step.second);
            // 运行事件循环让定时器工作
            for (int j = 0; j < 8; ++j)
            {
                m_eventLoop->LoopOnce();
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
            }
        }

        m_progressFitting.Stop();
    }

    void runCompleteOTASimulation()
    {
        std::cout << "\n" << std::string(80, '=') << std::endl;
        std::cout << "完整OTA更新过程模拟 - 从0%到100%" << std::endl;
        std::cout << std::string(80, '=') << std::endl;

        // 重置进度拟合模块
        m_progressFitting.Reset();

        // 添加三个域的设备
        auto cdcDevices = createDevices("CDC", 2); // CDC域：2个设备
        auto mdcDevices = createDevices("MDC", 3); // MDC域：3个设备
        auto zcuDevices = createDevices("ZCU", 1); // ZCU域：1个设备

        m_progressFitting.AddUpdateDevice(DUCType::CDC, cdcDevices);
        m_progressFitting.AddUpdateDevice(DUCType::MDC, mdcDevices);
        m_progressFitting.AddUpdateDevice(DUCType::ZCU, zcuDevices);

        std::cout << "设备配置:" << std::endl;
        std::cout << "  CDC域: 2个设备 (CDC_1, CDC_2)" << std::endl;
        std::cout << "  MDC域: 3个设备 (MDC_1, MDC_2, MDC_3)" << std::endl;
        std::cout << "  ZCU域: 1个设备 (ZCU_1)" << std::endl;
        std::cout << std::string(80, '-') << std::endl;

        m_progressFitting.Start();

        // 模拟完整的更新过程，每10%一个步骤
        for (int step = 0; step <= 15; ++step)
        {
            int baseProgress = step * 10;

            std::cout << "\n[步骤 " << step + 1
                      << "/11] 模拟进度: " << baseProgress << "%" << std::endl;

            // CDC域设备进度 (稍微快一些)
            int cdc1_progress = std::min(100, baseProgress + 5);
            int cdc2_progress = std::min(100, baseProgress);

            // MDC域设备进度 (中等速度)
            int mdc1_progress = std::min(100, baseProgress);
            int mdc2_progress = std::min(100, baseProgress - 5);
            int mdc3_progress = std::min(100, baseProgress + 3);

            // ZCU域设备进度 (稍微慢一些)
            int zcu1_progress = std::min(100, baseProgress - 10);

            // 确保进度不为负数
            cdc2_progress = std::max(0, cdc2_progress);
            mdc2_progress = std::max(0, mdc2_progress);
            zcu1_progress = std::max(0, zcu1_progress);

            // 更新CDC域进度
            simulateProgressWithDetails(DUCType::CDC,
                                        {"CDC_1", "CDC_2"},
                                        {static_cast<uint8_t>(cdc1_progress),
                                         static_cast<uint8_t>(cdc2_progress)});

            // 更新MDC域进度
            simulateProgressWithDetails(DUCType::MDC,
                                        {"MDC_1", "MDC_2", "MDC_3"},
                                        {static_cast<uint8_t>(mdc1_progress),
                                         static_cast<uint8_t>(mdc2_progress),
                                         static_cast<uint8_t>(mdc3_progress)});

            // 更新ZCU域进度
            simulateProgressWithDetails(DUCType::ZCU,
                                        {"ZCU_1"},
                                        {static_cast<uint8_t>(zcu1_progress)});

            // 计算期望的域控进度
            float cdc_expected = (cdc1_progress + cdc2_progress) / 2.0f;
            float mdc_expected =
                (mdc1_progress + mdc2_progress + mdc3_progress) / 3.0f;
            float zcu_expected = zcu1_progress;
            float overall_expected =
                (cdc_expected + mdc_expected + zcu_expected) / 3.0f;

            std::cout << "期望进度: CDC=" << std::fixed << std::setprecision(1)
                      << cdc_expected << "%, MDC=" << mdc_expected
                      << "%, ZCU=" << zcu_expected
                      << "%, 总体=" << overall_expected << "%" << std::endl;

            // 运行事件循环让定时器工作
            for (int j = 0; j < 10; ++j)
            {
                m_eventLoop->LoopOnce();
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
            }

            std::cout << "实际总体进度: " << static_cast<int>(m_lastProgress)
                      << "%" << std::endl;
            std::cout << std::string(80, '-') << std::endl;

            // 如果所有设备都达到100%，提前结束
            if (cdc1_progress >= 100 && cdc2_progress >= 100 &&
                mdc1_progress >= 100 && mdc2_progress >= 100 &&
                mdc3_progress >= 100 && zcu1_progress >= 100)
            {
                std::cout << "所有设备已达到100%，更新完成！" << std::endl;
                break;
            }
        }

        // m_progressFitting.Stop();
        std::cout << "\n完整OTA更新过程模拟结束" << std::endl;
        std::cout << "最终进度: " << static_cast<int>(m_lastProgress) << "%"
                  << std::endl;
        std::cout << "全部完成: " << (m_allFinished ? "是" : "否") << std::endl;
    }

    void printFinalResults()
    {
        std::cout << "\n" << std::string(60, '=') << std::endl;
        std::cout << "测试完成" << std::endl;
        std::cout << "最终进度: " << static_cast<int>(m_lastProgress) << "%"
                  << std::endl;
        std::cout << "全部完成: " << (m_allFinished ? "是" : "否") << std::endl;
        std::cout << std::string(60, '=') << std::endl;
    }

private:
    ProgressFitting m_progressFitting;
    EventLoop *m_eventLoop = nullptr;
    uint8_t m_lastProgress = 0;
    bool m_allFinished = false;
};

int main()
{
    std::cout << "进度拟合模块权重计算测试程序" << std::endl;
    std::cout << "本程序将测试进度计算的准确性和权重分配" << std::endl;

    try
    {
        ProgressTestManager testManager;

        // 运行完整的OTA更新模拟
        testManager.runCompleteOTASimulation();

        testManager.printFinalResults();

        std::cout << "\n测试程序执行完成！" << std::endl;
    }
    catch (const std::exception &e)
    {
        std::cerr << "测试过程中发生异常: " << e.what() << std::endl;
        return -1;
    }

    return 0;
}
