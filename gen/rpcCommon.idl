module dds { module rpc {

    typedef octet GuidPrefix_t[12];

    struct EntityId_t {
        octet entityKey[3];
        octet entityKind;
    };

    struct GUID_t {
        GuidPrefix_t guidPrefix;
        EntityId_t entityId;
    };
    struct SequenceNumber_t {
        long high;
        unsigned long low;
    };

    struct SampleIdentity {
        GUID_t writer_guid;
        SequenceNumber_t sequence_number;
    };

    enum RemoteExceptionCode_t
    {
        REMOTE_EX_OK,
        REMOTE_EX_UNSUPPORTED,
        REMOTE_EX_INVALID_ARGUMENT,
        REMOTE_EX_OUT_OF_RESOURCES,
        REMOTE_EX_UNKNOWN_OPERATION,
        REMOTE_EX_UNKNOWN_EXCEPTION
    };

    typedef string<255> InstanceName;

    struct RequestHeader {
        SampleIdentity requestId;
        InstanceName instanceName;
    };

    struct ReplyHeader {
        SampleIdentity relatedRequestId;
        //dds::rpc::RemoteExceptionCode_t remoteEx;
    };

};};
