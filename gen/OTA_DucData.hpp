/****************************************************************

  Generated by Eclipse Cyclone DDS IDL to CXX Translator
  File name: /home/<USER>/dds/scom-idl/OTA/OTA_DucData.idl
  Source: OTA_DucData.hpp
  Cyclone DDS: v0.11.0

*****************************************************************/
#ifndef DDSCXX_OTA_DUCDATA_HPP_50E87A31A3DDFA27360AF3FE235813AC
#define DDSCXX_OTA_DUCDATA_HPP_50E87A31A3DDFA27360AF3FE235813AC

#include <utility>
#include <ostream>
#include <cstdint>
#include <vector>
#include <string>
#include <variant>
#include <dds/core/Exception.hpp>


namespace seres
{
namespace ota_duc_service
{
const int32_t DomainParticipant_ID = 1001;

const std::string CDC_SERVICE_NAME = "OTA_CDC_Service";

const std::string MDC_SERVICE_NAME = "OTA_MDC_Service";

const std::string ZCU_SERVICE_NAME = "OTA_ZCU_Service";

const std::string CDC_TOPIC_NAME = "OTA_CDC_TOPIC";

const std::string MDC_TOPIC_NAME = "OTA_MDC_TOPIC";

const std::string ZCU_TOPIC_NAME = "OTA_ZCU_TOPIC";

enum class DUCType
{
  CDC,
  MDC,
  ZCU};

std::ostream& operator<<(std::ostream& os, DUCType const& rhs);

class SelectedInventoryList
{
private:
 std::vector<std::string> inventoryLists_;

public:
  SelectedInventoryList() = default;

  explicit SelectedInventoryList(
    const std::vector<std::string>& inventoryLists) :
    inventoryLists_(inventoryLists) { }

  const std::vector<std::string>& inventoryLists() const { return this->inventoryLists_; }
  std::vector<std::string>& inventoryLists() { return this->inventoryLists_; }
  void inventoryLists(const std::vector<std::string>& _val_) { this->inventoryLists_ = _val_; }
  void inventoryLists(std::vector<std::string>&& _val_) { this->inventoryLists_ = std::move(_val_); }

  bool operator==(const SelectedInventoryList& _other) const
  {
    (void) _other;
    return inventoryLists_ == _other.inventoryLists_;
  }

  bool operator!=(const SelectedInventoryList& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, SelectedInventoryList const& rhs);

class InventoryInfo
{
private:
 std::string partNumber_;
 std::string softwareVersion_;
 std::string supplierCode_;
 std::string ecuName_;
 std::string serialNumber_;
 std::string hardwareVersion_;
 std::string ecuBatchNumber_;
 std::string bootloaderVersion_;
 std::string backupVersion_;
 bool SeamlessModeSupport_ = false;

public:
  InventoryInfo() = default;

  explicit InventoryInfo(
    const std::string& partNumber,
    const std::string& softwareVersion,
    const std::string& supplierCode,
    const std::string& ecuName,
    const std::string& serialNumber,
    const std::string& hardwareVersion,
    const std::string& ecuBatchNumber,
    const std::string& bootloaderVersion,
    const std::string& backupVersion,
    bool SeamlessModeSupport) :
    partNumber_(partNumber),
    softwareVersion_(softwareVersion),
    supplierCode_(supplierCode),
    ecuName_(ecuName),
    serialNumber_(serialNumber),
    hardwareVersion_(hardwareVersion),
    ecuBatchNumber_(ecuBatchNumber),
    bootloaderVersion_(bootloaderVersion),
    backupVersion_(backupVersion),
    SeamlessModeSupport_(SeamlessModeSupport) { }

  const std::string& partNumber() const { return this->partNumber_; }
  std::string& partNumber() { return this->partNumber_; }
  void partNumber(const std::string& _val_) { this->partNumber_ = _val_; }
  void partNumber(std::string&& _val_) { this->partNumber_ = std::move(_val_); }
  const std::string& softwareVersion() const { return this->softwareVersion_; }
  std::string& softwareVersion() { return this->softwareVersion_; }
  void softwareVersion(const std::string& _val_) { this->softwareVersion_ = _val_; }
  void softwareVersion(std::string&& _val_) { this->softwareVersion_ = std::move(_val_); }
  const std::string& supplierCode() const { return this->supplierCode_; }
  std::string& supplierCode() { return this->supplierCode_; }
  void supplierCode(const std::string& _val_) { this->supplierCode_ = _val_; }
  void supplierCode(std::string&& _val_) { this->supplierCode_ = std::move(_val_); }
  const std::string& ecuName() const { return this->ecuName_; }
  std::string& ecuName() { return this->ecuName_; }
  void ecuName(const std::string& _val_) { this->ecuName_ = _val_; }
  void ecuName(std::string&& _val_) { this->ecuName_ = std::move(_val_); }
  const std::string& serialNumber() const { return this->serialNumber_; }
  std::string& serialNumber() { return this->serialNumber_; }
  void serialNumber(const std::string& _val_) { this->serialNumber_ = _val_; }
  void serialNumber(std::string&& _val_) { this->serialNumber_ = std::move(_val_); }
  const std::string& hardwareVersion() const { return this->hardwareVersion_; }
  std::string& hardwareVersion() { return this->hardwareVersion_; }
  void hardwareVersion(const std::string& _val_) { this->hardwareVersion_ = _val_; }
  void hardwareVersion(std::string&& _val_) { this->hardwareVersion_ = std::move(_val_); }
  const std::string& ecuBatchNumber() const { return this->ecuBatchNumber_; }
  std::string& ecuBatchNumber() { return this->ecuBatchNumber_; }
  void ecuBatchNumber(const std::string& _val_) { this->ecuBatchNumber_ = _val_; }
  void ecuBatchNumber(std::string&& _val_) { this->ecuBatchNumber_ = std::move(_val_); }
  const std::string& bootloaderVersion() const { return this->bootloaderVersion_; }
  std::string& bootloaderVersion() { return this->bootloaderVersion_; }
  void bootloaderVersion(const std::string& _val_) { this->bootloaderVersion_ = _val_; }
  void bootloaderVersion(std::string&& _val_) { this->bootloaderVersion_ = std::move(_val_); }
  const std::string& backupVersion() const { return this->backupVersion_; }
  std::string& backupVersion() { return this->backupVersion_; }
  void backupVersion(const std::string& _val_) { this->backupVersion_ = _val_; }
  void backupVersion(std::string&& _val_) { this->backupVersion_ = std::move(_val_); }
  bool SeamlessModeSupport() const { return this->SeamlessModeSupport_; }
  bool& SeamlessModeSupport() { return this->SeamlessModeSupport_; }
  void SeamlessModeSupport(bool _val_) { this->SeamlessModeSupport_ = _val_; }

  bool operator==(const InventoryInfo& _other) const
  {
    (void) _other;
    return partNumber_ == _other.partNumber_ &&
      softwareVersion_ == _other.softwareVersion_ &&
      supplierCode_ == _other.supplierCode_ &&
      ecuName_ == _other.ecuName_ &&
      serialNumber_ == _other.serialNumber_ &&
      hardwareVersion_ == _other.hardwareVersion_ &&
      ecuBatchNumber_ == _other.ecuBatchNumber_ &&
      bootloaderVersion_ == _other.bootloaderVersion_ &&
      backupVersion_ == _other.backupVersion_ &&
      SeamlessModeSupport_ == _other.SeamlessModeSupport_;
  }

  bool operator!=(const InventoryInfo& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, InventoryInfo const& rhs);

class InventoryResult
{
private:
 std::vector<::seres::ota_duc_service::InventoryInfo> InventoryLists_;

public:
  InventoryResult() = default;

  explicit InventoryResult(
    const std::vector<::seres::ota_duc_service::InventoryInfo>& InventoryLists) :
    InventoryLists_(InventoryLists) { }

  const std::vector<::seres::ota_duc_service::InventoryInfo>& InventoryLists() const { return this->InventoryLists_; }
  std::vector<::seres::ota_duc_service::InventoryInfo>& InventoryLists() { return this->InventoryLists_; }
  void InventoryLists(const std::vector<::seres::ota_duc_service::InventoryInfo>& _val_) { this->InventoryLists_ = _val_; }
  void InventoryLists(std::vector<::seres::ota_duc_service::InventoryInfo>&& _val_) { this->InventoryLists_ = std::move(_val_); }

  bool operator==(const InventoryResult& _other) const
  {
    (void) _other;
    return InventoryLists_ == _other.InventoryLists_;
  }

  bool operator!=(const InventoryResult& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, InventoryResult const& rhs);

class DownloadRequirement
{
private:
 std::string deviceId_;
 uint64_t diskRequirement_ = 0;

public:
  DownloadRequirement() = default;

  explicit DownloadRequirement(
    const std::string& deviceId,
    uint64_t diskRequirement) :
    deviceId_(deviceId),
    diskRequirement_(diskRequirement) { }

  const std::string& deviceId() const { return this->deviceId_; }
  std::string& deviceId() { return this->deviceId_; }
  void deviceId(const std::string& _val_) { this->deviceId_ = _val_; }
  void deviceId(std::string&& _val_) { this->deviceId_ = std::move(_val_); }
  uint64_t diskRequirement() const { return this->diskRequirement_; }
  uint64_t& diskRequirement() { return this->diskRequirement_; }
  void diskRequirement(uint64_t _val_) { this->diskRequirement_ = _val_; }

  bool operator==(const DownloadRequirement& _other) const
  {
    (void) _other;
    return deviceId_ == _other.deviceId_ &&
      diskRequirement_ == _other.diskRequirement_;
  }

  bool operator!=(const DownloadRequirement& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DownloadRequirement const& rhs);

class DownloadConditionLists
{
private:
 std::vector<::seres::ota_duc_service::DownloadRequirement> downloadRequirementLists_;

public:
  DownloadConditionLists() = default;

  explicit DownloadConditionLists(
    const std::vector<::seres::ota_duc_service::DownloadRequirement>& downloadRequirementLists) :
    downloadRequirementLists_(downloadRequirementLists) { }

  const std::vector<::seres::ota_duc_service::DownloadRequirement>& downloadRequirementLists() const { return this->downloadRequirementLists_; }
  std::vector<::seres::ota_duc_service::DownloadRequirement>& downloadRequirementLists() { return this->downloadRequirementLists_; }
  void downloadRequirementLists(const std::vector<::seres::ota_duc_service::DownloadRequirement>& _val_) { this->downloadRequirementLists_ = _val_; }
  void downloadRequirementLists(std::vector<::seres::ota_duc_service::DownloadRequirement>&& _val_) { this->downloadRequirementLists_ = std::move(_val_); }

  bool operator==(const DownloadConditionLists& _other) const
  {
    (void) _other;
    return downloadRequirementLists_ == _other.downloadRequirementLists_;
  }

  bool operator!=(const DownloadConditionLists& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DownloadConditionLists const& rhs);

enum class DownloadConditionResult
{
  NOEXCEPTION,
  NetworkERROR,
  DISK_NOT_ENOUGH,
  PARAMS_INVALID};

std::ostream& operator<<(std::ostream& os, DownloadConditionResult const& rhs);

class DownloadTaskInfo
{
private:
 std::string taskId_;
 std::string packageVersion_;
 std::string packageName_;
 std::string packageUrl_;
 std::string packageSize_;
 std::string packageMd5_;

public:
  DownloadTaskInfo() = default;

  explicit DownloadTaskInfo(
    const std::string& taskId,
    const std::string& packageVersion,
    const std::string& packageName,
    const std::string& packageUrl,
    const std::string& packageSize,
    const std::string& packageMd5) :
    taskId_(taskId),
    packageVersion_(packageVersion),
    packageName_(packageName),
    packageUrl_(packageUrl),
    packageSize_(packageSize),
    packageMd5_(packageMd5) { }

  const std::string& taskId() const { return this->taskId_; }
  std::string& taskId() { return this->taskId_; }
  void taskId(const std::string& _val_) { this->taskId_ = _val_; }
  void taskId(std::string&& _val_) { this->taskId_ = std::move(_val_); }
  const std::string& packageVersion() const { return this->packageVersion_; }
  std::string& packageVersion() { return this->packageVersion_; }
  void packageVersion(const std::string& _val_) { this->packageVersion_ = _val_; }
  void packageVersion(std::string&& _val_) { this->packageVersion_ = std::move(_val_); }
  const std::string& packageName() const { return this->packageName_; }
  std::string& packageName() { return this->packageName_; }
  void packageName(const std::string& _val_) { this->packageName_ = _val_; }
  void packageName(std::string&& _val_) { this->packageName_ = std::move(_val_); }
  const std::string& packageUrl() const { return this->packageUrl_; }
  std::string& packageUrl() { return this->packageUrl_; }
  void packageUrl(const std::string& _val_) { this->packageUrl_ = _val_; }
  void packageUrl(std::string&& _val_) { this->packageUrl_ = std::move(_val_); }
  const std::string& packageSize() const { return this->packageSize_; }
  std::string& packageSize() { return this->packageSize_; }
  void packageSize(const std::string& _val_) { this->packageSize_ = _val_; }
  void packageSize(std::string&& _val_) { this->packageSize_ = std::move(_val_); }
  const std::string& packageMd5() const { return this->packageMd5_; }
  std::string& packageMd5() { return this->packageMd5_; }
  void packageMd5(const std::string& _val_) { this->packageMd5_ = _val_; }
  void packageMd5(std::string&& _val_) { this->packageMd5_ = std::move(_val_); }

  bool operator==(const DownloadTaskInfo& _other) const
  {
    (void) _other;
    return taskId_ == _other.taskId_ &&
      packageVersion_ == _other.packageVersion_ &&
      packageName_ == _other.packageName_ &&
      packageUrl_ == _other.packageUrl_ &&
      packageSize_ == _other.packageSize_ &&
      packageMd5_ == _other.packageMd5_;
  }

  bool operator!=(const DownloadTaskInfo& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DownloadTaskInfo const& rhs);

class DownloadTaskLists
{
private:
 std::vector<::seres::ota_duc_service::DownloadTaskInfo> taskLists_;

public:
  DownloadTaskLists() = default;

  explicit DownloadTaskLists(
    const std::vector<::seres::ota_duc_service::DownloadTaskInfo>& taskLists) :
    taskLists_(taskLists) { }

  const std::vector<::seres::ota_duc_service::DownloadTaskInfo>& taskLists() const { return this->taskLists_; }
  std::vector<::seres::ota_duc_service::DownloadTaskInfo>& taskLists() { return this->taskLists_; }
  void taskLists(const std::vector<::seres::ota_duc_service::DownloadTaskInfo>& _val_) { this->taskLists_ = _val_; }
  void taskLists(std::vector<::seres::ota_duc_service::DownloadTaskInfo>&& _val_) { this->taskLists_ = std::move(_val_); }

  bool operator==(const DownloadTaskLists& _other) const
  {
    (void) _other;
    return taskLists_ == _other.taskLists_;
  }

  bool operator!=(const DownloadTaskLists& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DownloadTaskLists const& rhs);

enum class DownloadCtrl
{
  PAUSE,
  RESUME,
  CANCEL};

std::ostream& operator<<(std::ostream& os, DownloadCtrl const& rhs);

enum class DownloadStatus
{
  DOWNLOADING,
  DOWNLOAD_SUCCESS,
  DOWNLOAD_FAIL,
  DOWNLOAD_PAUSE,
  PACKAGE_INCOMPLETE};

std::ostream& operator<<(std::ostream& os, DownloadStatus const& rhs);

class DownloadProgressInfo
{
private:
 uint8_t progressPercent_ = 0;
 std::string packageName_;
 uint64_t downloadedSize_ = 0;
 uint64_t totalSize_ = 0;
 ::seres::ota_duc_service::DownloadStatus status_ = ::seres::ota_duc_service::DownloadStatus::DOWNLOADING;

public:
  DownloadProgressInfo() = default;

  explicit DownloadProgressInfo(
    uint8_t progressPercent,
    const std::string& packageName,
    uint64_t downloadedSize,
    uint64_t totalSize,
    ::seres::ota_duc_service::DownloadStatus status) :
    progressPercent_(progressPercent),
    packageName_(packageName),
    downloadedSize_(downloadedSize),
    totalSize_(totalSize),
    status_(status) { }

  uint8_t progressPercent() const { return this->progressPercent_; }
  uint8_t& progressPercent() { return this->progressPercent_; }
  void progressPercent(uint8_t _val_) { this->progressPercent_ = _val_; }
  const std::string& packageName() const { return this->packageName_; }
  std::string& packageName() { return this->packageName_; }
  void packageName(const std::string& _val_) { this->packageName_ = _val_; }
  void packageName(std::string&& _val_) { this->packageName_ = std::move(_val_); }
  uint64_t downloadedSize() const { return this->downloadedSize_; }
  uint64_t& downloadedSize() { return this->downloadedSize_; }
  void downloadedSize(uint64_t _val_) { this->downloadedSize_ = _val_; }
  uint64_t totalSize() const { return this->totalSize_; }
  uint64_t& totalSize() { return this->totalSize_; }
  void totalSize(uint64_t _val_) { this->totalSize_ = _val_; }
  ::seres::ota_duc_service::DownloadStatus status() const { return this->status_; }
  ::seres::ota_duc_service::DownloadStatus& status() { return this->status_; }
  void status(::seres::ota_duc_service::DownloadStatus _val_) { this->status_ = _val_; }

  bool operator==(const DownloadProgressInfo& _other) const
  {
    (void) _other;
    return progressPercent_ == _other.progressPercent_ &&
      packageName_ == _other.packageName_ &&
      downloadedSize_ == _other.downloadedSize_ &&
      totalSize_ == _other.totalSize_ &&
      status_ == _other.status_;
  }

  bool operator!=(const DownloadProgressInfo& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DownloadProgressInfo const& rhs);

class DownloadProgress
{
private:
 bool allFinished_ = false;
 std::vector<::seres::ota_duc_service::DownloadProgressInfo> progressLists_;

public:
  DownloadProgress() = default;

  explicit DownloadProgress(
    bool allFinished,
    const std::vector<::seres::ota_duc_service::DownloadProgressInfo>& progressLists) :
    allFinished_(allFinished),
    progressLists_(progressLists) { }

  bool allFinished() const { return this->allFinished_; }
  bool& allFinished() { return this->allFinished_; }
  void allFinished(bool _val_) { this->allFinished_ = _val_; }
  const std::vector<::seres::ota_duc_service::DownloadProgressInfo>& progressLists() const { return this->progressLists_; }
  std::vector<::seres::ota_duc_service::DownloadProgressInfo>& progressLists() { return this->progressLists_; }
  void progressLists(const std::vector<::seres::ota_duc_service::DownloadProgressInfo>& _val_) { this->progressLists_ = _val_; }
  void progressLists(std::vector<::seres::ota_duc_service::DownloadProgressInfo>&& _val_) { this->progressLists_ = std::move(_val_); }

  bool operator==(const DownloadProgress& _other) const
  {
    (void) _other;
    return allFinished_ == _other.allFinished_ &&
      progressLists_ == _other.progressLists_;
  }

  bool operator!=(const DownloadProgress& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DownloadProgress const& rhs);

class UzipPackagesResult
{
private:
 bool successed_ = false;
 std::string errorMsg_;

public:
  UzipPackagesResult() = default;

  explicit UzipPackagesResult(
    bool successed,
    const std::string& errorMsg) :
    successed_(successed),
    errorMsg_(errorMsg) { }

  bool successed() const { return this->successed_; }
  bool& successed() { return this->successed_; }
  void successed(bool _val_) { this->successed_ = _val_; }
  const std::string& errorMsg() const { return this->errorMsg_; }
  std::string& errorMsg() { return this->errorMsg_; }
  void errorMsg(const std::string& _val_) { this->errorMsg_ = _val_; }
  void errorMsg(std::string&& _val_) { this->errorMsg_ = std::move(_val_); }

  bool operator==(const UzipPackagesResult& _other) const
  {
    (void) _other;
    return successed_ == _other.successed_ &&
      errorMsg_ == _other.errorMsg_;
  }

  bool operator!=(const UzipPackagesResult& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, UzipPackagesResult const& rhs);

class PackagesVerifyResult
{
private:
 bool successed_ = false;
 std::string errorMsg_;

public:
  PackagesVerifyResult() = default;

  explicit PackagesVerifyResult(
    bool successed,
    const std::string& errorMsg) :
    successed_(successed),
    errorMsg_(errorMsg) { }

  bool successed() const { return this->successed_; }
  bool& successed() { return this->successed_; }
  void successed(bool _val_) { this->successed_ = _val_; }
  const std::string& errorMsg() const { return this->errorMsg_; }
  std::string& errorMsg() { return this->errorMsg_; }
  void errorMsg(const std::string& _val_) { this->errorMsg_ = _val_; }
  void errorMsg(std::string&& _val_) { this->errorMsg_ = std::move(_val_); }

  bool operator==(const PackagesVerifyResult& _other) const
  {
    (void) _other;
    return successed_ == _other.successed_ &&
      errorMsg_ == _other.errorMsg_;
  }

  bool operator!=(const PackagesVerifyResult& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, PackagesVerifyResult const& rhs);

enum class UpdateConditionErrorCode
{
  NOERROR,
  ERROR1,
  ERROR2};

std::ostream& operator<<(std::ostream& os, UpdateConditionErrorCode const& rhs);

class CheckUpdateConditionResult
{
private:
 bool passed_ = false;
 ::seres::ota_duc_service::UpdateConditionErrorCode errorCode_ = ::seres::ota_duc_service::UpdateConditionErrorCode::NOERROR;

public:
  CheckUpdateConditionResult() = default;

  explicit CheckUpdateConditionResult(
    bool passed,
    ::seres::ota_duc_service::UpdateConditionErrorCode errorCode) :
    passed_(passed),
    errorCode_(errorCode) { }

  bool passed() const { return this->passed_; }
  bool& passed() { return this->passed_; }
  void passed(bool _val_) { this->passed_ = _val_; }
  ::seres::ota_duc_service::UpdateConditionErrorCode errorCode() const { return this->errorCode_; }
  ::seres::ota_duc_service::UpdateConditionErrorCode& errorCode() { return this->errorCode_; }
  void errorCode(::seres::ota_duc_service::UpdateConditionErrorCode _val_) { this->errorCode_ = _val_; }

  bool operator==(const CheckUpdateConditionResult& _other) const
  {
    (void) _other;
    return passed_ == _other.passed_ &&
      errorCode_ == _other.errorCode_;
  }

  bool operator!=(const CheckUpdateConditionResult& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, CheckUpdateConditionResult const& rhs);

enum class UpdateMode
{
  SeamlessMode,
  FormalMode};

std::ostream& operator<<(std::ostream& os, UpdateMode const& rhs);

class UpdateDeviceList
{
private:
 std::vector<::seres::ota_duc_service::InventoryInfo> updateDeviceLists_;

public:
  UpdateDeviceList() = default;

  explicit UpdateDeviceList(
    const std::vector<::seres::ota_duc_service::InventoryInfo>& updateDeviceLists) :
    updateDeviceLists_(updateDeviceLists) { }

  const std::vector<::seres::ota_duc_service::InventoryInfo>& updateDeviceLists() const { return this->updateDeviceLists_; }
  std::vector<::seres::ota_duc_service::InventoryInfo>& updateDeviceLists() { return this->updateDeviceLists_; }
  void updateDeviceLists(const std::vector<::seres::ota_duc_service::InventoryInfo>& _val_) { this->updateDeviceLists_ = _val_; }
  void updateDeviceLists(std::vector<::seres::ota_duc_service::InventoryInfo>&& _val_) { this->updateDeviceLists_ = std::move(_val_); }

  bool operator==(const UpdateDeviceList& _other) const
  {
    (void) _other;
    return updateDeviceLists_ == _other.updateDeviceLists_;
  }

  bool operator!=(const UpdateDeviceList& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, UpdateDeviceList const& rhs);

enum class DeviceUpdateStatus
{
  IDLE,
  UPDATING,
  SUCCESS,
  FAILURE};

std::ostream& operator<<(std::ostream& os, DeviceUpdateStatus const& rhs);

enum class UpdateErrorReason
{
  ERROR_REASON_1,
  ERROR_REASON_2,
  ERROR_REASON_3};

std::ostream& operator<<(std::ostream& os, UpdateErrorReason const& rhs);

class DeviceUpdateProgress
{
private:
 uint8_t progressPercent_ = 0;
 std::string deviceName_;
 std::string deviceId_;
 ::seres::ota_duc_service::DeviceUpdateStatus status_ = ::seres::ota_duc_service::DeviceUpdateStatus::IDLE;
 ::seres::ota_duc_service::UpdateErrorReason errorReason_ = ::seres::ota_duc_service::UpdateErrorReason::ERROR_REASON_1;

public:
  DeviceUpdateProgress() = default;

  explicit DeviceUpdateProgress(
    uint8_t progressPercent,
    const std::string& deviceName,
    const std::string& deviceId,
    ::seres::ota_duc_service::DeviceUpdateStatus status,
    ::seres::ota_duc_service::UpdateErrorReason errorReason) :
    progressPercent_(progressPercent),
    deviceName_(deviceName),
    deviceId_(deviceId),
    status_(status),
    errorReason_(errorReason) { }

  uint8_t progressPercent() const { return this->progressPercent_; }
  uint8_t& progressPercent() { return this->progressPercent_; }
  void progressPercent(uint8_t _val_) { this->progressPercent_ = _val_; }
  const std::string& deviceName() const { return this->deviceName_; }
  std::string& deviceName() { return this->deviceName_; }
  void deviceName(const std::string& _val_) { this->deviceName_ = _val_; }
  void deviceName(std::string&& _val_) { this->deviceName_ = std::move(_val_); }
  const std::string& deviceId() const { return this->deviceId_; }
  std::string& deviceId() { return this->deviceId_; }
  void deviceId(const std::string& _val_) { this->deviceId_ = _val_; }
  void deviceId(std::string&& _val_) { this->deviceId_ = std::move(_val_); }
  ::seres::ota_duc_service::DeviceUpdateStatus status() const { return this->status_; }
  ::seres::ota_duc_service::DeviceUpdateStatus& status() { return this->status_; }
  void status(::seres::ota_duc_service::DeviceUpdateStatus _val_) { this->status_ = _val_; }
  ::seres::ota_duc_service::UpdateErrorReason errorReason() const { return this->errorReason_; }
  ::seres::ota_duc_service::UpdateErrorReason& errorReason() { return this->errorReason_; }
  void errorReason(::seres::ota_duc_service::UpdateErrorReason _val_) { this->errorReason_ = _val_; }

  bool operator==(const DeviceUpdateProgress& _other) const
  {
    (void) _other;
    return progressPercent_ == _other.progressPercent_ &&
      deviceName_ == _other.deviceName_ &&
      deviceId_ == _other.deviceId_ &&
      status_ == _other.status_ &&
      errorReason_ == _other.errorReason_;
  }

  bool operator!=(const DeviceUpdateProgress& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DeviceUpdateProgress const& rhs);

class UpdateProgress
{
private:
 bool allFinished_ = false;
 std::vector<::seres::ota_duc_service::DeviceUpdateProgress> progressLists_;

public:
  UpdateProgress() = default;

  explicit UpdateProgress(
    bool allFinished,
    const std::vector<::seres::ota_duc_service::DeviceUpdateProgress>& progressLists) :
    allFinished_(allFinished),
    progressLists_(progressLists) { }

  bool allFinished() const { return this->allFinished_; }
  bool& allFinished() { return this->allFinished_; }
  void allFinished(bool _val_) { this->allFinished_ = _val_; }
  const std::vector<::seres::ota_duc_service::DeviceUpdateProgress>& progressLists() const { return this->progressLists_; }
  std::vector<::seres::ota_duc_service::DeviceUpdateProgress>& progressLists() { return this->progressLists_; }
  void progressLists(const std::vector<::seres::ota_duc_service::DeviceUpdateProgress>& _val_) { this->progressLists_ = _val_; }
  void progressLists(std::vector<::seres::ota_duc_service::DeviceUpdateProgress>&& _val_) { this->progressLists_ = std::move(_val_); }

  bool operator==(const UpdateProgress& _other) const
  {
    (void) _other;
    return allFinished_ == _other.allFinished_ &&
      progressLists_ == _other.progressLists_;
  }

  bool operator!=(const UpdateProgress& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, UpdateProgress const& rhs);

class RollbackComponentList
{
private:
 std::vector<::seres::ota_duc_service::InventoryInfo> rollbackLists_;

public:
  RollbackComponentList() = default;

  explicit RollbackComponentList(
    const std::vector<::seres::ota_duc_service::InventoryInfo>& rollbackLists) :
    rollbackLists_(rollbackLists) { }

  const std::vector<::seres::ota_duc_service::InventoryInfo>& rollbackLists() const { return this->rollbackLists_; }
  std::vector<::seres::ota_duc_service::InventoryInfo>& rollbackLists() { return this->rollbackLists_; }
  void rollbackLists(const std::vector<::seres::ota_duc_service::InventoryInfo>& _val_) { this->rollbackLists_ = _val_; }
  void rollbackLists(std::vector<::seres::ota_duc_service::InventoryInfo>&& _val_) { this->rollbackLists_ = std::move(_val_); }

  bool operator==(const RollbackComponentList& _other) const
  {
    (void) _other;
    return rollbackLists_ == _other.rollbackLists_;
  }

  bool operator!=(const RollbackComponentList& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, RollbackComponentList const& rhs);

enum class OTA_TopicData
{
  INVENTORY_RESULT,
  DOWNLOAD_PROGRESS,
  UZIP_PACKAGES_RESULT,
  PACKAGES_VERIFY_RESULT,
  CHECK_UPDATE_CONDITION_RESULT,
  UPDATE_PROGRESS};

std::ostream& operator<<(std::ostream& os, OTA_TopicData const& rhs);

class OTA_DucDataUnion
{
private:
  ::seres::ota_duc_service::OTA_TopicData m__d;

  std::variant<::seres::ota_duc_service::InventoryResult, ::seres::ota_duc_service::DownloadProgress, ::seres::ota_duc_service::UzipPackagesResult, ::seres::ota_duc_service::PackagesVerifyResult, ::seres::ota_duc_service::CheckUpdateConditionResult, ::seres::ota_duc_service::UpdateProgress> m__u;

  static const ::seres::ota_duc_service::OTA_TopicData _default_discriminator = ::seres::ota_duc_service::OTA_TopicData::INVENTORY_RESULT;

  static ::seres::ota_duc_service::OTA_TopicData _is_discriminator(const ::seres::ota_duc_service::OTA_TopicData d)
  {
    switch (d) {
      case ::seres::ota_duc_service::OTA_TopicData::INVENTORY_RESULT:
        return ::seres::ota_duc_service::OTA_TopicData::INVENTORY_RESULT;
      case ::seres::ota_duc_service::OTA_TopicData::DOWNLOAD_PROGRESS:
        return ::seres::ota_duc_service::OTA_TopicData::DOWNLOAD_PROGRESS;
      case ::seres::ota_duc_service::OTA_TopicData::UZIP_PACKAGES_RESULT:
        return ::seres::ota_duc_service::OTA_TopicData::UZIP_PACKAGES_RESULT;
      case ::seres::ota_duc_service::OTA_TopicData::PACKAGES_VERIFY_RESULT:
        return ::seres::ota_duc_service::OTA_TopicData::PACKAGES_VERIFY_RESULT;
      case ::seres::ota_duc_service::OTA_TopicData::CHECK_UPDATE_CONDITION_RESULT:
        return ::seres::ota_duc_service::OTA_TopicData::CHECK_UPDATE_CONDITION_RESULT;
      case ::seres::ota_duc_service::OTA_TopicData::UPDATE_PROGRESS:
        return ::seres::ota_duc_service::OTA_TopicData::UPDATE_PROGRESS;
    }
    return _default_discriminator;
  }

  static bool _is_compatible_discriminator(const ::seres::ota_duc_service::OTA_TopicData d1, const ::seres::ota_duc_service::OTA_TopicData d2)
  {
    return _is_discriminator(d1) == _is_discriminator(d2);
  }

public:
  OTA_DucDataUnion() :
      m__d(_default_discriminator),
      m__u()
 { }

  ::seres::ota_duc_service::OTA_TopicData _d() const
  {
    return m__d;
  }

  void _d(::seres::ota_duc_service::OTA_TopicData d)
  {
    if (!_is_compatible_discriminator(m__d, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator value does not match current discriminator");
    }
    m__d = d;
  }

  const ::seres::ota_duc_service::InventoryResult &inventoryResult() const
  {
    if (!_is_compatible_discriminator(m__d, ::seres::ota_duc_service::OTA_TopicData::INVENTORY_RESULT)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::InventoryResult>(m__u);
  }

  ::seres::ota_duc_service::InventoryResult& inventoryResult()
  {
    if (!_is_compatible_discriminator(m__d, ::seres::ota_duc_service::OTA_TopicData::INVENTORY_RESULT)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::InventoryResult>(m__u);
  }

  void inventoryResult(const ::seres::ota_duc_service::InventoryResult& u, ::seres::ota_duc_service::OTA_TopicData d = ::seres::ota_duc_service::OTA_TopicData::INVENTORY_RESULT)
  {
    if (!_is_compatible_discriminator(::seres::ota_duc_service::OTA_TopicData::INVENTORY_RESULT, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  void inventoryResult(::seres::ota_duc_service::InventoryResult&& u, ::seres::ota_duc_service::OTA_TopicData d = ::seres::ota_duc_service::OTA_TopicData::INVENTORY_RESULT)
  {
    if (!_is_compatible_discriminator(::seres::ota_duc_service::OTA_TopicData::INVENTORY_RESULT, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  const ::seres::ota_duc_service::DownloadProgress &downloadProgress() const
  {
    if (!_is_compatible_discriminator(m__d, ::seres::ota_duc_service::OTA_TopicData::DOWNLOAD_PROGRESS)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DownloadProgress>(m__u);
  }

  ::seres::ota_duc_service::DownloadProgress& downloadProgress()
  {
    if (!_is_compatible_discriminator(m__d, ::seres::ota_duc_service::OTA_TopicData::DOWNLOAD_PROGRESS)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DownloadProgress>(m__u);
  }

  void downloadProgress(const ::seres::ota_duc_service::DownloadProgress& u, ::seres::ota_duc_service::OTA_TopicData d = ::seres::ota_duc_service::OTA_TopicData::DOWNLOAD_PROGRESS)
  {
    if (!_is_compatible_discriminator(::seres::ota_duc_service::OTA_TopicData::DOWNLOAD_PROGRESS, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  void downloadProgress(::seres::ota_duc_service::DownloadProgress&& u, ::seres::ota_duc_service::OTA_TopicData d = ::seres::ota_duc_service::OTA_TopicData::DOWNLOAD_PROGRESS)
  {
    if (!_is_compatible_discriminator(::seres::ota_duc_service::OTA_TopicData::DOWNLOAD_PROGRESS, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  const ::seres::ota_duc_service::UzipPackagesResult &uzipPackagesResult() const
  {
    if (!_is_compatible_discriminator(m__d, ::seres::ota_duc_service::OTA_TopicData::UZIP_PACKAGES_RESULT)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::UzipPackagesResult>(m__u);
  }

  ::seres::ota_duc_service::UzipPackagesResult& uzipPackagesResult()
  {
    if (!_is_compatible_discriminator(m__d, ::seres::ota_duc_service::OTA_TopicData::UZIP_PACKAGES_RESULT)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::UzipPackagesResult>(m__u);
  }

  void uzipPackagesResult(const ::seres::ota_duc_service::UzipPackagesResult& u, ::seres::ota_duc_service::OTA_TopicData d = ::seres::ota_duc_service::OTA_TopicData::UZIP_PACKAGES_RESULT)
  {
    if (!_is_compatible_discriminator(::seres::ota_duc_service::OTA_TopicData::UZIP_PACKAGES_RESULT, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  void uzipPackagesResult(::seres::ota_duc_service::UzipPackagesResult&& u, ::seres::ota_duc_service::OTA_TopicData d = ::seres::ota_duc_service::OTA_TopicData::UZIP_PACKAGES_RESULT)
  {
    if (!_is_compatible_discriminator(::seres::ota_duc_service::OTA_TopicData::UZIP_PACKAGES_RESULT, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  const ::seres::ota_duc_service::PackagesVerifyResult &packagesVerifyResult() const
  {
    if (!_is_compatible_discriminator(m__d, ::seres::ota_duc_service::OTA_TopicData::PACKAGES_VERIFY_RESULT)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::PackagesVerifyResult>(m__u);
  }

  ::seres::ota_duc_service::PackagesVerifyResult& packagesVerifyResult()
  {
    if (!_is_compatible_discriminator(m__d, ::seres::ota_duc_service::OTA_TopicData::PACKAGES_VERIFY_RESULT)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::PackagesVerifyResult>(m__u);
  }

  void packagesVerifyResult(const ::seres::ota_duc_service::PackagesVerifyResult& u, ::seres::ota_duc_service::OTA_TopicData d = ::seres::ota_duc_service::OTA_TopicData::PACKAGES_VERIFY_RESULT)
  {
    if (!_is_compatible_discriminator(::seres::ota_duc_service::OTA_TopicData::PACKAGES_VERIFY_RESULT, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  void packagesVerifyResult(::seres::ota_duc_service::PackagesVerifyResult&& u, ::seres::ota_duc_service::OTA_TopicData d = ::seres::ota_duc_service::OTA_TopicData::PACKAGES_VERIFY_RESULT)
  {
    if (!_is_compatible_discriminator(::seres::ota_duc_service::OTA_TopicData::PACKAGES_VERIFY_RESULT, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  const ::seres::ota_duc_service::CheckUpdateConditionResult &checkUpdateConditionResult() const
  {
    if (!_is_compatible_discriminator(m__d, ::seres::ota_duc_service::OTA_TopicData::CHECK_UPDATE_CONDITION_RESULT)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::CheckUpdateConditionResult>(m__u);
  }

  ::seres::ota_duc_service::CheckUpdateConditionResult& checkUpdateConditionResult()
  {
    if (!_is_compatible_discriminator(m__d, ::seres::ota_duc_service::OTA_TopicData::CHECK_UPDATE_CONDITION_RESULT)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::CheckUpdateConditionResult>(m__u);
  }

  void checkUpdateConditionResult(const ::seres::ota_duc_service::CheckUpdateConditionResult& u, ::seres::ota_duc_service::OTA_TopicData d = ::seres::ota_duc_service::OTA_TopicData::CHECK_UPDATE_CONDITION_RESULT)
  {
    if (!_is_compatible_discriminator(::seres::ota_duc_service::OTA_TopicData::CHECK_UPDATE_CONDITION_RESULT, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  void checkUpdateConditionResult(::seres::ota_duc_service::CheckUpdateConditionResult&& u, ::seres::ota_duc_service::OTA_TopicData d = ::seres::ota_duc_service::OTA_TopicData::CHECK_UPDATE_CONDITION_RESULT)
  {
    if (!_is_compatible_discriminator(::seres::ota_duc_service::OTA_TopicData::CHECK_UPDATE_CONDITION_RESULT, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  const ::seres::ota_duc_service::UpdateProgress &updateProgress() const
  {
    if (!_is_compatible_discriminator(m__d, ::seres::ota_duc_service::OTA_TopicData::UPDATE_PROGRESS)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::UpdateProgress>(m__u);
  }

  ::seres::ota_duc_service::UpdateProgress& updateProgress()
  {
    if (!_is_compatible_discriminator(m__d, ::seres::ota_duc_service::OTA_TopicData::UPDATE_PROGRESS)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::UpdateProgress>(m__u);
  }

  void updateProgress(const ::seres::ota_duc_service::UpdateProgress& u, ::seres::ota_duc_service::OTA_TopicData d = ::seres::ota_duc_service::OTA_TopicData::UPDATE_PROGRESS)
  {
    if (!_is_compatible_discriminator(::seres::ota_duc_service::OTA_TopicData::UPDATE_PROGRESS, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  void updateProgress(::seres::ota_duc_service::UpdateProgress&& u, ::seres::ota_duc_service::OTA_TopicData d = ::seres::ota_duc_service::OTA_TopicData::UPDATE_PROGRESS)
  {
    if (!_is_compatible_discriminator(::seres::ota_duc_service::OTA_TopicData::UPDATE_PROGRESS, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  bool operator==(const OTA_DucDataUnion& _other) const
  {
    if (_d() != _other._d()) return false;
    switch (_d()) {
      case ::seres::ota_duc_service::OTA_TopicData::INVENTORY_RESULT:
        return inventoryResult() == _other.inventoryResult();
      case ::seres::ota_duc_service::OTA_TopicData::DOWNLOAD_PROGRESS:
        return downloadProgress() == _other.downloadProgress();
      case ::seres::ota_duc_service::OTA_TopicData::UZIP_PACKAGES_RESULT:
        return uzipPackagesResult() == _other.uzipPackagesResult();
      case ::seres::ota_duc_service::OTA_TopicData::PACKAGES_VERIFY_RESULT:
        return packagesVerifyResult() == _other.packagesVerifyResult();
      case ::seres::ota_duc_service::OTA_TopicData::CHECK_UPDATE_CONDITION_RESULT:
        return checkUpdateConditionResult() == _other.checkUpdateConditionResult();
      case ::seres::ota_duc_service::OTA_TopicData::UPDATE_PROGRESS:
        return updateProgress() == _other.updateProgress();
    }
    return true;
  }

  bool operator!=(const OTA_DucDataUnion& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, OTA_DucDataUnion const& rhs);
} //namespace ota_duc_service

} //namespace seres

#include "dds/topic/TopicTraits.hpp"
#include "org/eclipse/cyclonedds/topic/datatopic.hpp"

namespace org {
namespace eclipse {
namespace cyclonedds {
namespace topic {

template <> constexpr const char* TopicTraits<::seres::ota_duc_service::SelectedInventoryList>::getTypeName()
{
  return "seres::ota_duc_service::SelectedInventoryList";
}

template <> constexpr bool TopicTraits<::seres::ota_duc_service::SelectedInventoryList>::isSelfContained()
{
  return false;
}

template <> constexpr bool TopicTraits<::seres::ota_duc_service::SelectedInventoryList>::isKeyless()
{
  return true;
}

template <> constexpr const char* TopicTraits<::seres::ota_duc_service::InventoryInfo>::getTypeName()
{
  return "seres::ota_duc_service::InventoryInfo";
}

template <> constexpr bool TopicTraits<::seres::ota_duc_service::InventoryInfo>::isSelfContained()
{
  return false;
}

template <> constexpr bool TopicTraits<::seres::ota_duc_service::InventoryInfo>::isKeyless()
{
  return true;
}

template <> constexpr const char* TopicTraits<::seres::ota_duc_service::InventoryResult>::getTypeName()
{
  return "seres::ota_duc_service::InventoryResult";
}

template <> constexpr bool TopicTraits<::seres::ota_duc_service::InventoryResult>::isSelfContained()
{
  return false;
}

template <> constexpr bool TopicTraits<::seres::ota_duc_service::InventoryResult>::isKeyless()
{
  return true;
}

template <> constexpr const char* TopicTraits<::seres::ota_duc_service::DownloadRequirement>::getTypeName()
{
  return "seres::ota_duc_service::DownloadRequirement";
}

template <> constexpr bool TopicTraits<::seres::ota_duc_service::DownloadRequirement>::isSelfContained()
{
  return false;
}

template <> constexpr bool TopicTraits<::seres::ota_duc_service::DownloadRequirement>::isKeyless()
{
  return true;
}

template <> constexpr const char* TopicTraits<::seres::ota_duc_service::DownloadConditionLists>::getTypeName()
{
  return "seres::ota_duc_service::DownloadConditionLists";
}

template <> constexpr bool TopicTraits<::seres::ota_duc_service::DownloadConditionLists>::isSelfContained()
{
  return false;
}

template <> constexpr bool TopicTraits<::seres::ota_duc_service::DownloadConditionLists>::isKeyless()
{
  return true;
}

template <> constexpr const char* TopicTraits<::seres::ota_duc_service::DownloadTaskInfo>::getTypeName()
{
  return "seres::ota_duc_service::DownloadTaskInfo";
}

template <> constexpr bool TopicTraits<::seres::ota_duc_service::DownloadTaskInfo>::isSelfContained()
{
  return false;
}

template <> constexpr bool TopicTraits<::seres::ota_duc_service::DownloadTaskInfo>::isKeyless()
{
  return true;
}

template <> constexpr const char* TopicTraits<::seres::ota_duc_service::DownloadTaskLists>::getTypeName()
{
  return "seres::ota_duc_service::DownloadTaskLists";
}

template <> constexpr bool TopicTraits<::seres::ota_duc_service::DownloadTaskLists>::isSelfContained()
{
  return false;
}

template <> constexpr bool TopicTraits<::seres::ota_duc_service::DownloadTaskLists>::isKeyless()
{
  return true;
}

template <> constexpr const char* TopicTraits<::seres::ota_duc_service::DownloadProgressInfo>::getTypeName()
{
  return "seres::ota_duc_service::DownloadProgressInfo";
}

template <> constexpr bool TopicTraits<::seres::ota_duc_service::DownloadProgressInfo>::isSelfContained()
{
  return false;
}

template <> constexpr bool TopicTraits<::seres::ota_duc_service::DownloadProgressInfo>::isKeyless()
{
  return true;
}

template <> constexpr const char* TopicTraits<::seres::ota_duc_service::DownloadProgress>::getTypeName()
{
  return "seres::ota_duc_service::DownloadProgress";
}

template <> constexpr bool TopicTraits<::seres::ota_duc_service::DownloadProgress>::isSelfContained()
{
  return false;
}

template <> constexpr bool TopicTraits<::seres::ota_duc_service::DownloadProgress>::isKeyless()
{
  return true;
}

template <> constexpr const char* TopicTraits<::seres::ota_duc_service::UzipPackagesResult>::getTypeName()
{
  return "seres::ota_duc_service::UzipPackagesResult";
}

template <> constexpr bool TopicTraits<::seres::ota_duc_service::UzipPackagesResult>::isSelfContained()
{
  return false;
}

template <> constexpr bool TopicTraits<::seres::ota_duc_service::UzipPackagesResult>::isKeyless()
{
  return true;
}

template <> constexpr const char* TopicTraits<::seres::ota_duc_service::PackagesVerifyResult>::getTypeName()
{
  return "seres::ota_duc_service::PackagesVerifyResult";
}

template <> constexpr bool TopicTraits<::seres::ota_duc_service::PackagesVerifyResult>::isSelfContained()
{
  return false;
}

template <> constexpr bool TopicTraits<::seres::ota_duc_service::PackagesVerifyResult>::isKeyless()
{
  return true;
}

template <> constexpr const char* TopicTraits<::seres::ota_duc_service::CheckUpdateConditionResult>::getTypeName()
{
  return "seres::ota_duc_service::CheckUpdateConditionResult";
}

template <> constexpr bool TopicTraits<::seres::ota_duc_service::CheckUpdateConditionResult>::isKeyless()
{
  return true;
}

template <> constexpr const char* TopicTraits<::seres::ota_duc_service::UpdateDeviceList>::getTypeName()
{
  return "seres::ota_duc_service::UpdateDeviceList";
}

template <> constexpr bool TopicTraits<::seres::ota_duc_service::UpdateDeviceList>::isSelfContained()
{
  return false;
}

template <> constexpr bool TopicTraits<::seres::ota_duc_service::UpdateDeviceList>::isKeyless()
{
  return true;
}

template <> constexpr const char* TopicTraits<::seres::ota_duc_service::DeviceUpdateProgress>::getTypeName()
{
  return "seres::ota_duc_service::DeviceUpdateProgress";
}

template <> constexpr bool TopicTraits<::seres::ota_duc_service::DeviceUpdateProgress>::isSelfContained()
{
  return false;
}

template <> constexpr bool TopicTraits<::seres::ota_duc_service::DeviceUpdateProgress>::isKeyless()
{
  return true;
}

template <> constexpr const char* TopicTraits<::seres::ota_duc_service::UpdateProgress>::getTypeName()
{
  return "seres::ota_duc_service::UpdateProgress";
}

template <> constexpr bool TopicTraits<::seres::ota_duc_service::UpdateProgress>::isSelfContained()
{
  return false;
}

template <> constexpr bool TopicTraits<::seres::ota_duc_service::UpdateProgress>::isKeyless()
{
  return true;
}

template <> constexpr const char* TopicTraits<::seres::ota_duc_service::RollbackComponentList>::getTypeName()
{
  return "seres::ota_duc_service::RollbackComponentList";
}

template <> constexpr bool TopicTraits<::seres::ota_duc_service::RollbackComponentList>::isSelfContained()
{
  return false;
}

template <> constexpr bool TopicTraits<::seres::ota_duc_service::RollbackComponentList>::isKeyless()
{
  return true;
}

template <> constexpr const char* TopicTraits<::seres::ota_duc_service::OTA_DucDataUnion>::getTypeName()
{
  return "seres::ota_duc_service::OTA_DucDataUnion";
}

template <> constexpr bool TopicTraits<::seres::ota_duc_service::OTA_DucDataUnion>::isSelfContained()
{
  return false;
}

template <> constexpr bool TopicTraits<::seres::ota_duc_service::OTA_DucDataUnion>::isKeyless()
{
  return true;
}

} //namespace topic
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

namespace dds {
namespace topic {

template <>
struct topic_type_name<::seres::ota_duc_service::SelectedInventoryList>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::seres::ota_duc_service::SelectedInventoryList>::getTypeName();
    }
};

template <>
struct topic_type_name<::seres::ota_duc_service::InventoryInfo>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::seres::ota_duc_service::InventoryInfo>::getTypeName();
    }
};

template <>
struct topic_type_name<::seres::ota_duc_service::InventoryResult>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::seres::ota_duc_service::InventoryResult>::getTypeName();
    }
};

template <>
struct topic_type_name<::seres::ota_duc_service::DownloadRequirement>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::seres::ota_duc_service::DownloadRequirement>::getTypeName();
    }
};

template <>
struct topic_type_name<::seres::ota_duc_service::DownloadConditionLists>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::seres::ota_duc_service::DownloadConditionLists>::getTypeName();
    }
};

template <>
struct topic_type_name<::seres::ota_duc_service::DownloadTaskInfo>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::seres::ota_duc_service::DownloadTaskInfo>::getTypeName();
    }
};

template <>
struct topic_type_name<::seres::ota_duc_service::DownloadTaskLists>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::seres::ota_duc_service::DownloadTaskLists>::getTypeName();
    }
};

template <>
struct topic_type_name<::seres::ota_duc_service::DownloadProgressInfo>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::seres::ota_duc_service::DownloadProgressInfo>::getTypeName();
    }
};

template <>
struct topic_type_name<::seres::ota_duc_service::DownloadProgress>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::seres::ota_duc_service::DownloadProgress>::getTypeName();
    }
};

template <>
struct topic_type_name<::seres::ota_duc_service::UzipPackagesResult>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::seres::ota_duc_service::UzipPackagesResult>::getTypeName();
    }
};

template <>
struct topic_type_name<::seres::ota_duc_service::PackagesVerifyResult>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::seres::ota_duc_service::PackagesVerifyResult>::getTypeName();
    }
};

template <>
struct topic_type_name<::seres::ota_duc_service::CheckUpdateConditionResult>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::seres::ota_duc_service::CheckUpdateConditionResult>::getTypeName();
    }
};

template <>
struct topic_type_name<::seres::ota_duc_service::UpdateDeviceList>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::seres::ota_duc_service::UpdateDeviceList>::getTypeName();
    }
};

template <>
struct topic_type_name<::seres::ota_duc_service::DeviceUpdateProgress>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::seres::ota_duc_service::DeviceUpdateProgress>::getTypeName();
    }
};

template <>
struct topic_type_name<::seres::ota_duc_service::UpdateProgress>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::seres::ota_duc_service::UpdateProgress>::getTypeName();
    }
};

template <>
struct topic_type_name<::seres::ota_duc_service::RollbackComponentList>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::seres::ota_duc_service::RollbackComponentList>::getTypeName();
    }
};

template <>
struct topic_type_name<::seres::ota_duc_service::OTA_DucDataUnion>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::seres::ota_duc_service::OTA_DucDataUnion>::getTypeName();
    }
};

}
}

REGISTER_TOPIC_TYPE(::seres::ota_duc_service::SelectedInventoryList)
REGISTER_TOPIC_TYPE(::seres::ota_duc_service::InventoryInfo)
REGISTER_TOPIC_TYPE(::seres::ota_duc_service::InventoryResult)
REGISTER_TOPIC_TYPE(::seres::ota_duc_service::DownloadRequirement)
REGISTER_TOPIC_TYPE(::seres::ota_duc_service::DownloadConditionLists)
REGISTER_TOPIC_TYPE(::seres::ota_duc_service::DownloadTaskInfo)
REGISTER_TOPIC_TYPE(::seres::ota_duc_service::DownloadTaskLists)
REGISTER_TOPIC_TYPE(::seres::ota_duc_service::DownloadProgressInfo)
REGISTER_TOPIC_TYPE(::seres::ota_duc_service::DownloadProgress)
REGISTER_TOPIC_TYPE(::seres::ota_duc_service::UzipPackagesResult)
REGISTER_TOPIC_TYPE(::seres::ota_duc_service::PackagesVerifyResult)
REGISTER_TOPIC_TYPE(::seres::ota_duc_service::CheckUpdateConditionResult)
REGISTER_TOPIC_TYPE(::seres::ota_duc_service::UpdateDeviceList)
REGISTER_TOPIC_TYPE(::seres::ota_duc_service::DeviceUpdateProgress)
REGISTER_TOPIC_TYPE(::seres::ota_duc_service::UpdateProgress)
REGISTER_TOPIC_TYPE(::seres::ota_duc_service::RollbackComponentList)
REGISTER_TOPIC_TYPE(::seres::ota_duc_service::OTA_DucDataUnion)

namespace org{
namespace eclipse{
namespace cyclonedds{
namespace core{
namespace cdr{

template<>
::seres::ota_duc_service::DUCType enum_conversion<::seres::ota_duc_service::DUCType>(uint32_t in);

template<>
const propvec &get_type_props<::seres::ota_duc_service::SelectedInventoryList>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__SelectedInventoryList = get_type_props<::seres::ota_duc_service::SelectedInventoryList>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::SelectedInventoryList& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, false))
        return false;
      {
      uint32_t se_1 = uint32_t(instance.inventoryLists().size());
      if (!write(streamer, se_1))
        return false;
      for (uint32_t i_1 = 0; i_1 < se_1; i_1++) {
      if (!write_string(streamer, instance.inventoryLists()[i_1], 0))
        return false;
      }  //i_1
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::seres::ota_duc_service::SelectedInventoryList& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::SelectedInventoryList>();
  str.set_mode(cdr_stream::stream_mode::write, key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::SelectedInventoryList& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, false))
        return false;
      {
      uint32_t se_1 = uint32_t(instance.inventoryLists().size());
      if (!read(streamer, se_1))
        return false;
      instance.inventoryLists().resize(se_1);
      for (uint32_t i_1 = 0; i_1 < se_1; i_1++) {
      if (!read_string(streamer, instance.inventoryLists()[i_1], 0))
        return false;
      }  //i_1
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::seres::ota_duc_service::SelectedInventoryList& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::SelectedInventoryList>();
  str.set_mode(cdr_stream::stream_mode::read, key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::SelectedInventoryList& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, false))
        return false;
      {
      uint32_t se_1 = uint32_t(instance.inventoryLists().size());
      if (!move(streamer, se_1))
        return false;
      for (uint32_t i_1 = 0; i_1 < se_1; i_1++) {
      if (!move_string(streamer, instance.inventoryLists()[i_1], 0))
        return false;
      }  //i_1
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::seres::ota_duc_service::SelectedInventoryList& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::SelectedInventoryList>();
  str.set_mode(cdr_stream::stream_mode::move, key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::SelectedInventoryList& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, false))
        return false;
      {
      uint32_t se_1 = 0;
      if (!max(streamer, se_1))
        return false;
      for (uint32_t i_1 = 0; i_1 < se_1; i_1++) {
      if (!max_string(streamer, instance.inventoryLists()[i_1], 0))
        return false;
      }  //i_1
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      streamer.position(SIZE_MAX);
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::seres::ota_duc_service::SelectedInventoryList& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::SelectedInventoryList>();
  str.set_mode(cdr_stream::stream_mode::max, key);
  return max(str, instance, props.data()); 
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::InventoryInfo>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__InventoryInfo = get_type_props<::seres::ota_duc_service::InventoryInfo>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::InventoryInfo& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write_string(streamer, instance.partNumber(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!write_string(streamer, instance.softwareVersion(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!write_string(streamer, instance.supplierCode(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!write_string(streamer, instance.ecuName(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!write_string(streamer, instance.serialNumber(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 5:
      if (!streamer.start_member(*prop))
        return false;
      if (!write_string(streamer, instance.hardwareVersion(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 6:
      if (!streamer.start_member(*prop))
        return false;
      if (!write_string(streamer, instance.ecuBatchNumber(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 7:
      if (!streamer.start_member(*prop))
        return false;
      if (!write_string(streamer, instance.bootloaderVersion(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 8:
      if (!streamer.start_member(*prop))
        return false;
      if (!write_string(streamer, instance.backupVersion(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 9:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.SeamlessModeSupport()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::seres::ota_duc_service::InventoryInfo& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::InventoryInfo>();
  str.set_mode(cdr_stream::stream_mode::write, key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::InventoryInfo& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read_string(streamer, instance.partNumber(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!read_string(streamer, instance.softwareVersion(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!read_string(streamer, instance.supplierCode(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!read_string(streamer, instance.ecuName(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!read_string(streamer, instance.serialNumber(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 5:
      if (!streamer.start_member(*prop))
        return false;
      if (!read_string(streamer, instance.hardwareVersion(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 6:
      if (!streamer.start_member(*prop))
        return false;
      if (!read_string(streamer, instance.ecuBatchNumber(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 7:
      if (!streamer.start_member(*prop))
        return false;
      if (!read_string(streamer, instance.bootloaderVersion(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 8:
      if (!streamer.start_member(*prop))
        return false;
      if (!read_string(streamer, instance.backupVersion(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 9:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.SeamlessModeSupport()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::seres::ota_duc_service::InventoryInfo& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::InventoryInfo>();
  str.set_mode(cdr_stream::stream_mode::read, key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::InventoryInfo& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move_string(streamer, instance.partNumber(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!move_string(streamer, instance.softwareVersion(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!move_string(streamer, instance.supplierCode(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!move_string(streamer, instance.ecuName(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!move_string(streamer, instance.serialNumber(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 5:
      if (!streamer.start_member(*prop))
        return false;
      if (!move_string(streamer, instance.hardwareVersion(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 6:
      if (!streamer.start_member(*prop))
        return false;
      if (!move_string(streamer, instance.ecuBatchNumber(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 7:
      if (!streamer.start_member(*prop))
        return false;
      if (!move_string(streamer, instance.bootloaderVersion(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 8:
      if (!streamer.start_member(*prop))
        return false;
      if (!move_string(streamer, instance.backupVersion(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 9:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.SeamlessModeSupport()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::seres::ota_duc_service::InventoryInfo& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::InventoryInfo>();
  str.set_mode(cdr_stream::stream_mode::move, key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::InventoryInfo& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max_string(streamer, instance.partNumber(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!max_string(streamer, instance.softwareVersion(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!max_string(streamer, instance.supplierCode(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!max_string(streamer, instance.ecuName(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!max_string(streamer, instance.serialNumber(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 5:
      if (!streamer.start_member(*prop))
        return false;
      if (!max_string(streamer, instance.hardwareVersion(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 6:
      if (!streamer.start_member(*prop))
        return false;
      if (!max_string(streamer, instance.ecuBatchNumber(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 7:
      if (!streamer.start_member(*prop))
        return false;
      if (!max_string(streamer, instance.bootloaderVersion(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 8:
      if (!streamer.start_member(*prop))
        return false;
      if (!max_string(streamer, instance.backupVersion(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 9:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.SeamlessModeSupport()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::seres::ota_duc_service::InventoryInfo& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::InventoryInfo>();
  str.set_mode(cdr_stream::stream_mode::max, key);
  return max(str, instance, props.data()); 
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::InventoryResult>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__InventoryResult = get_type_props<::seres::ota_duc_service::InventoryResult>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::InventoryResult& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, false))
        return false;
      {
      uint32_t se_1 = uint32_t(instance.InventoryLists().size());
      if (!write(streamer, se_1))
        return false;
      for (uint32_t i_1 = 0; i_1 < se_1; i_1++) {
      if (!write(streamer, instance.InventoryLists()[i_1], prop))
        return false;
      }  //i_1
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::seres::ota_duc_service::InventoryResult& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::InventoryResult>();
  str.set_mode(cdr_stream::stream_mode::write, key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::InventoryResult& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, false))
        return false;
      {
      uint32_t se_1 = uint32_t(instance.InventoryLists().size());
      if (!read(streamer, se_1))
        return false;
      instance.InventoryLists().resize(se_1);
      for (uint32_t i_1 = 0; i_1 < se_1; i_1++) {
      if (!read(streamer, instance.InventoryLists()[i_1], prop))
        return false;
      }  //i_1
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::seres::ota_duc_service::InventoryResult& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::InventoryResult>();
  str.set_mode(cdr_stream::stream_mode::read, key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::InventoryResult& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, false))
        return false;
      {
      uint32_t se_1 = uint32_t(instance.InventoryLists().size());
      if (!move(streamer, se_1))
        return false;
      for (uint32_t i_1 = 0; i_1 < se_1; i_1++) {
      if (!move(streamer, instance.InventoryLists()[i_1], prop))
        return false;
      }  //i_1
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::seres::ota_duc_service::InventoryResult& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::InventoryResult>();
  str.set_mode(cdr_stream::stream_mode::move, key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::InventoryResult& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, false))
        return false;
      {
      uint32_t se_1 = 0;
      if (!max(streamer, se_1))
        return false;
      for (uint32_t i_1 = 0; i_1 < se_1; i_1++) {
      if (!max(streamer, instance.InventoryLists()[i_1], prop))
        return false;
      }  //i_1
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      streamer.position(SIZE_MAX);
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::seres::ota_duc_service::InventoryResult& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::InventoryResult>();
  str.set_mode(cdr_stream::stream_mode::max, key);
  return max(str, instance, props.data()); 
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DownloadRequirement>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DownloadRequirement = get_type_props<::seres::ota_duc_service::DownloadRequirement>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DownloadRequirement& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write_string(streamer, instance.deviceId(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.diskRequirement()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::seres::ota_duc_service::DownloadRequirement& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::DownloadRequirement>();
  str.set_mode(cdr_stream::stream_mode::write, key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DownloadRequirement& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read_string(streamer, instance.deviceId(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.diskRequirement()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::seres::ota_duc_service::DownloadRequirement& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::DownloadRequirement>();
  str.set_mode(cdr_stream::stream_mode::read, key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DownloadRequirement& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move_string(streamer, instance.deviceId(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.diskRequirement()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::seres::ota_duc_service::DownloadRequirement& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::DownloadRequirement>();
  str.set_mode(cdr_stream::stream_mode::move, key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DownloadRequirement& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max_string(streamer, instance.deviceId(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.diskRequirement()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::seres::ota_duc_service::DownloadRequirement& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::DownloadRequirement>();
  str.set_mode(cdr_stream::stream_mode::max, key);
  return max(str, instance, props.data()); 
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DownloadConditionLists>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DownloadConditionLists = get_type_props<::seres::ota_duc_service::DownloadConditionLists>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DownloadConditionLists& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, false))
        return false;
      {
      uint32_t se_1 = uint32_t(instance.downloadRequirementLists().size());
      if (!write(streamer, se_1))
        return false;
      for (uint32_t i_1 = 0; i_1 < se_1; i_1++) {
      if (!write(streamer, instance.downloadRequirementLists()[i_1], prop))
        return false;
      }  //i_1
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::seres::ota_duc_service::DownloadConditionLists& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::DownloadConditionLists>();
  str.set_mode(cdr_stream::stream_mode::write, key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DownloadConditionLists& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, false))
        return false;
      {
      uint32_t se_1 = uint32_t(instance.downloadRequirementLists().size());
      if (!read(streamer, se_1))
        return false;
      instance.downloadRequirementLists().resize(se_1);
      for (uint32_t i_1 = 0; i_1 < se_1; i_1++) {
      if (!read(streamer, instance.downloadRequirementLists()[i_1], prop))
        return false;
      }  //i_1
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::seres::ota_duc_service::DownloadConditionLists& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::DownloadConditionLists>();
  str.set_mode(cdr_stream::stream_mode::read, key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DownloadConditionLists& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, false))
        return false;
      {
      uint32_t se_1 = uint32_t(instance.downloadRequirementLists().size());
      if (!move(streamer, se_1))
        return false;
      for (uint32_t i_1 = 0; i_1 < se_1; i_1++) {
      if (!move(streamer, instance.downloadRequirementLists()[i_1], prop))
        return false;
      }  //i_1
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::seres::ota_duc_service::DownloadConditionLists& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::DownloadConditionLists>();
  str.set_mode(cdr_stream::stream_mode::move, key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DownloadConditionLists& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, false))
        return false;
      {
      uint32_t se_1 = 0;
      if (!max(streamer, se_1))
        return false;
      for (uint32_t i_1 = 0; i_1 < se_1; i_1++) {
      if (!max(streamer, instance.downloadRequirementLists()[i_1], prop))
        return false;
      }  //i_1
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      streamer.position(SIZE_MAX);
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::seres::ota_duc_service::DownloadConditionLists& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::DownloadConditionLists>();
  str.set_mode(cdr_stream::stream_mode::max, key);
  return max(str, instance, props.data()); 
}

template<>
::seres::ota_duc_service::DownloadConditionResult enum_conversion<::seres::ota_duc_service::DownloadConditionResult>(uint32_t in);

template<>
const propvec &get_type_props<::seres::ota_duc_service::DownloadTaskInfo>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DownloadTaskInfo = get_type_props<::seres::ota_duc_service::DownloadTaskInfo>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DownloadTaskInfo& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write_string(streamer, instance.taskId(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!write_string(streamer, instance.packageVersion(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!write_string(streamer, instance.packageName(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!write_string(streamer, instance.packageUrl(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!write_string(streamer, instance.packageSize(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 5:
      if (!streamer.start_member(*prop))
        return false;
      if (!write_string(streamer, instance.packageMd5(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::seres::ota_duc_service::DownloadTaskInfo& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::DownloadTaskInfo>();
  str.set_mode(cdr_stream::stream_mode::write, key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DownloadTaskInfo& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read_string(streamer, instance.taskId(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!read_string(streamer, instance.packageVersion(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!read_string(streamer, instance.packageName(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!read_string(streamer, instance.packageUrl(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!read_string(streamer, instance.packageSize(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 5:
      if (!streamer.start_member(*prop))
        return false;
      if (!read_string(streamer, instance.packageMd5(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::seres::ota_duc_service::DownloadTaskInfo& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::DownloadTaskInfo>();
  str.set_mode(cdr_stream::stream_mode::read, key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DownloadTaskInfo& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move_string(streamer, instance.taskId(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!move_string(streamer, instance.packageVersion(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!move_string(streamer, instance.packageName(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!move_string(streamer, instance.packageUrl(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!move_string(streamer, instance.packageSize(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 5:
      if (!streamer.start_member(*prop))
        return false;
      if (!move_string(streamer, instance.packageMd5(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::seres::ota_duc_service::DownloadTaskInfo& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::DownloadTaskInfo>();
  str.set_mode(cdr_stream::stream_mode::move, key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DownloadTaskInfo& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max_string(streamer, instance.taskId(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!max_string(streamer, instance.packageVersion(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!max_string(streamer, instance.packageName(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!max_string(streamer, instance.packageUrl(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!max_string(streamer, instance.packageSize(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 5:
      if (!streamer.start_member(*prop))
        return false;
      if (!max_string(streamer, instance.packageMd5(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::seres::ota_duc_service::DownloadTaskInfo& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::DownloadTaskInfo>();
  str.set_mode(cdr_stream::stream_mode::max, key);
  return max(str, instance, props.data()); 
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DownloadTaskLists>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DownloadTaskLists = get_type_props<::seres::ota_duc_service::DownloadTaskLists>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DownloadTaskLists& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, false))
        return false;
      {
      uint32_t se_1 = uint32_t(instance.taskLists().size());
      if (!write(streamer, se_1))
        return false;
      for (uint32_t i_1 = 0; i_1 < se_1; i_1++) {
      if (!write(streamer, instance.taskLists()[i_1], prop))
        return false;
      }  //i_1
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::seres::ota_duc_service::DownloadTaskLists& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::DownloadTaskLists>();
  str.set_mode(cdr_stream::stream_mode::write, key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DownloadTaskLists& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, false))
        return false;
      {
      uint32_t se_1 = uint32_t(instance.taskLists().size());
      if (!read(streamer, se_1))
        return false;
      instance.taskLists().resize(se_1);
      for (uint32_t i_1 = 0; i_1 < se_1; i_1++) {
      if (!read(streamer, instance.taskLists()[i_1], prop))
        return false;
      }  //i_1
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::seres::ota_duc_service::DownloadTaskLists& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::DownloadTaskLists>();
  str.set_mode(cdr_stream::stream_mode::read, key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DownloadTaskLists& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, false))
        return false;
      {
      uint32_t se_1 = uint32_t(instance.taskLists().size());
      if (!move(streamer, se_1))
        return false;
      for (uint32_t i_1 = 0; i_1 < se_1; i_1++) {
      if (!move(streamer, instance.taskLists()[i_1], prop))
        return false;
      }  //i_1
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::seres::ota_duc_service::DownloadTaskLists& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::DownloadTaskLists>();
  str.set_mode(cdr_stream::stream_mode::move, key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DownloadTaskLists& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, false))
        return false;
      {
      uint32_t se_1 = 0;
      if (!max(streamer, se_1))
        return false;
      for (uint32_t i_1 = 0; i_1 < se_1; i_1++) {
      if (!max(streamer, instance.taskLists()[i_1], prop))
        return false;
      }  //i_1
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      streamer.position(SIZE_MAX);
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::seres::ota_duc_service::DownloadTaskLists& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::DownloadTaskLists>();
  str.set_mode(cdr_stream::stream_mode::max, key);
  return max(str, instance, props.data()); 
}

template<>
::seres::ota_duc_service::DownloadCtrl enum_conversion<::seres::ota_duc_service::DownloadCtrl>(uint32_t in);

template<>
::seres::ota_duc_service::DownloadStatus enum_conversion<::seres::ota_duc_service::DownloadStatus>(uint32_t in);

template<>
const propvec &get_type_props<::seres::ota_duc_service::DownloadProgressInfo>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DownloadProgressInfo = get_type_props<::seres::ota_duc_service::DownloadProgressInfo>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DownloadProgressInfo& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.progressPercent()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!write_string(streamer, instance.packageName(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.downloadedSize()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.totalSize()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.status()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::seres::ota_duc_service::DownloadProgressInfo& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::DownloadProgressInfo>();
  str.set_mode(cdr_stream::stream_mode::write, key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DownloadProgressInfo& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.progressPercent()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!read_string(streamer, instance.packageName(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.downloadedSize()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.totalSize()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.status()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::seres::ota_duc_service::DownloadProgressInfo& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::DownloadProgressInfo>();
  str.set_mode(cdr_stream::stream_mode::read, key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DownloadProgressInfo& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.progressPercent()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!move_string(streamer, instance.packageName(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.downloadedSize()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.totalSize()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.status()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::seres::ota_duc_service::DownloadProgressInfo& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::DownloadProgressInfo>();
  str.set_mode(cdr_stream::stream_mode::move, key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DownloadProgressInfo& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.progressPercent()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!max_string(streamer, instance.packageName(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.downloadedSize()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.totalSize()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.status()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::seres::ota_duc_service::DownloadProgressInfo& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::DownloadProgressInfo>();
  str.set_mode(cdr_stream::stream_mode::max, key);
  return max(str, instance, props.data()); 
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DownloadProgress>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DownloadProgress = get_type_props<::seres::ota_duc_service::DownloadProgress>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DownloadProgress& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.allFinished()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, false))
        return false;
      {
      uint32_t se_1 = uint32_t(instance.progressLists().size());
      if (!write(streamer, se_1))
        return false;
      for (uint32_t i_1 = 0; i_1 < se_1; i_1++) {
      if (!write(streamer, instance.progressLists()[i_1], prop))
        return false;
      }  //i_1
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::seres::ota_duc_service::DownloadProgress& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::DownloadProgress>();
  str.set_mode(cdr_stream::stream_mode::write, key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DownloadProgress& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.allFinished()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, false))
        return false;
      {
      uint32_t se_1 = uint32_t(instance.progressLists().size());
      if (!read(streamer, se_1))
        return false;
      instance.progressLists().resize(se_1);
      for (uint32_t i_1 = 0; i_1 < se_1; i_1++) {
      if (!read(streamer, instance.progressLists()[i_1], prop))
        return false;
      }  //i_1
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::seres::ota_duc_service::DownloadProgress& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::DownloadProgress>();
  str.set_mode(cdr_stream::stream_mode::read, key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DownloadProgress& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.allFinished()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, false))
        return false;
      {
      uint32_t se_1 = uint32_t(instance.progressLists().size());
      if (!move(streamer, se_1))
        return false;
      for (uint32_t i_1 = 0; i_1 < se_1; i_1++) {
      if (!move(streamer, instance.progressLists()[i_1], prop))
        return false;
      }  //i_1
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::seres::ota_duc_service::DownloadProgress& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::DownloadProgress>();
  str.set_mode(cdr_stream::stream_mode::move, key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DownloadProgress& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.allFinished()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, false))
        return false;
      {
      uint32_t se_1 = 0;
      if (!max(streamer, se_1))
        return false;
      for (uint32_t i_1 = 0; i_1 < se_1; i_1++) {
      if (!max(streamer, instance.progressLists()[i_1], prop))
        return false;
      }  //i_1
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      streamer.position(SIZE_MAX);
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::seres::ota_duc_service::DownloadProgress& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::DownloadProgress>();
  str.set_mode(cdr_stream::stream_mode::max, key);
  return max(str, instance, props.data()); 
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::UzipPackagesResult>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__UzipPackagesResult = get_type_props<::seres::ota_duc_service::UzipPackagesResult>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::UzipPackagesResult& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.successed()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!write_string(streamer, instance.errorMsg(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::seres::ota_duc_service::UzipPackagesResult& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::UzipPackagesResult>();
  str.set_mode(cdr_stream::stream_mode::write, key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::UzipPackagesResult& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.successed()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!read_string(streamer, instance.errorMsg(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::seres::ota_duc_service::UzipPackagesResult& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::UzipPackagesResult>();
  str.set_mode(cdr_stream::stream_mode::read, key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::UzipPackagesResult& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.successed()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!move_string(streamer, instance.errorMsg(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::seres::ota_duc_service::UzipPackagesResult& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::UzipPackagesResult>();
  str.set_mode(cdr_stream::stream_mode::move, key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::UzipPackagesResult& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.successed()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!max_string(streamer, instance.errorMsg(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::seres::ota_duc_service::UzipPackagesResult& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::UzipPackagesResult>();
  str.set_mode(cdr_stream::stream_mode::max, key);
  return max(str, instance, props.data()); 
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::PackagesVerifyResult>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__PackagesVerifyResult = get_type_props<::seres::ota_duc_service::PackagesVerifyResult>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::PackagesVerifyResult& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.successed()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!write_string(streamer, instance.errorMsg(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::seres::ota_duc_service::PackagesVerifyResult& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::PackagesVerifyResult>();
  str.set_mode(cdr_stream::stream_mode::write, key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::PackagesVerifyResult& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.successed()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!read_string(streamer, instance.errorMsg(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::seres::ota_duc_service::PackagesVerifyResult& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::PackagesVerifyResult>();
  str.set_mode(cdr_stream::stream_mode::read, key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::PackagesVerifyResult& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.successed()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!move_string(streamer, instance.errorMsg(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::seres::ota_duc_service::PackagesVerifyResult& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::PackagesVerifyResult>();
  str.set_mode(cdr_stream::stream_mode::move, key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::PackagesVerifyResult& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.successed()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!max_string(streamer, instance.errorMsg(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::seres::ota_duc_service::PackagesVerifyResult& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::PackagesVerifyResult>();
  str.set_mode(cdr_stream::stream_mode::max, key);
  return max(str, instance, props.data()); 
}

template<>
::seres::ota_duc_service::UpdateConditionErrorCode enum_conversion<::seres::ota_duc_service::UpdateConditionErrorCode>(uint32_t in);

template<>
const propvec &get_type_props<::seres::ota_duc_service::CheckUpdateConditionResult>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__CheckUpdateConditionResult = get_type_props<::seres::ota_duc_service::CheckUpdateConditionResult>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::CheckUpdateConditionResult& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.passed()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.errorCode()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::seres::ota_duc_service::CheckUpdateConditionResult& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::CheckUpdateConditionResult>();
  str.set_mode(cdr_stream::stream_mode::write, key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::CheckUpdateConditionResult& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.passed()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.errorCode()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::seres::ota_duc_service::CheckUpdateConditionResult& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::CheckUpdateConditionResult>();
  str.set_mode(cdr_stream::stream_mode::read, key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::CheckUpdateConditionResult& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.passed()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.errorCode()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::seres::ota_duc_service::CheckUpdateConditionResult& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::CheckUpdateConditionResult>();
  str.set_mode(cdr_stream::stream_mode::move, key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::CheckUpdateConditionResult& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.passed()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.errorCode()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::seres::ota_duc_service::CheckUpdateConditionResult& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::CheckUpdateConditionResult>();
  str.set_mode(cdr_stream::stream_mode::max, key);
  return max(str, instance, props.data()); 
}

template<>
::seres::ota_duc_service::UpdateMode enum_conversion<::seres::ota_duc_service::UpdateMode>(uint32_t in);

template<>
const propvec &get_type_props<::seres::ota_duc_service::UpdateDeviceList>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__UpdateDeviceList = get_type_props<::seres::ota_duc_service::UpdateDeviceList>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::UpdateDeviceList& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, false))
        return false;
      {
      uint32_t se_1 = uint32_t(instance.updateDeviceLists().size());
      if (!write(streamer, se_1))
        return false;
      for (uint32_t i_1 = 0; i_1 < se_1; i_1++) {
      if (!write(streamer, instance.updateDeviceLists()[i_1], prop))
        return false;
      }  //i_1
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::seres::ota_duc_service::UpdateDeviceList& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::UpdateDeviceList>();
  str.set_mode(cdr_stream::stream_mode::write, key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::UpdateDeviceList& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, false))
        return false;
      {
      uint32_t se_1 = uint32_t(instance.updateDeviceLists().size());
      if (!read(streamer, se_1))
        return false;
      instance.updateDeviceLists().resize(se_1);
      for (uint32_t i_1 = 0; i_1 < se_1; i_1++) {
      if (!read(streamer, instance.updateDeviceLists()[i_1], prop))
        return false;
      }  //i_1
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::seres::ota_duc_service::UpdateDeviceList& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::UpdateDeviceList>();
  str.set_mode(cdr_stream::stream_mode::read, key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::UpdateDeviceList& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, false))
        return false;
      {
      uint32_t se_1 = uint32_t(instance.updateDeviceLists().size());
      if (!move(streamer, se_1))
        return false;
      for (uint32_t i_1 = 0; i_1 < se_1; i_1++) {
      if (!move(streamer, instance.updateDeviceLists()[i_1], prop))
        return false;
      }  //i_1
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::seres::ota_duc_service::UpdateDeviceList& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::UpdateDeviceList>();
  str.set_mode(cdr_stream::stream_mode::move, key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::UpdateDeviceList& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, false))
        return false;
      {
      uint32_t se_1 = 0;
      if (!max(streamer, se_1))
        return false;
      for (uint32_t i_1 = 0; i_1 < se_1; i_1++) {
      if (!max(streamer, instance.updateDeviceLists()[i_1], prop))
        return false;
      }  //i_1
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      streamer.position(SIZE_MAX);
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::seres::ota_duc_service::UpdateDeviceList& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::UpdateDeviceList>();
  str.set_mode(cdr_stream::stream_mode::max, key);
  return max(str, instance, props.data()); 
}

template<>
::seres::ota_duc_service::DeviceUpdateStatus enum_conversion<::seres::ota_duc_service::DeviceUpdateStatus>(uint32_t in);

template<>
::seres::ota_duc_service::UpdateErrorReason enum_conversion<::seres::ota_duc_service::UpdateErrorReason>(uint32_t in);

template<>
const propvec &get_type_props<::seres::ota_duc_service::DeviceUpdateProgress>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DeviceUpdateProgress = get_type_props<::seres::ota_duc_service::DeviceUpdateProgress>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DeviceUpdateProgress& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.progressPercent()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!write_string(streamer, instance.deviceName(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!write_string(streamer, instance.deviceId(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.status()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.errorReason()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::seres::ota_duc_service::DeviceUpdateProgress& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::DeviceUpdateProgress>();
  str.set_mode(cdr_stream::stream_mode::write, key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DeviceUpdateProgress& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.progressPercent()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!read_string(streamer, instance.deviceName(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!read_string(streamer, instance.deviceId(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.status()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.errorReason()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::seres::ota_duc_service::DeviceUpdateProgress& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::DeviceUpdateProgress>();
  str.set_mode(cdr_stream::stream_mode::read, key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DeviceUpdateProgress& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.progressPercent()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!move_string(streamer, instance.deviceName(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!move_string(streamer, instance.deviceId(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.status()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.errorReason()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::seres::ota_duc_service::DeviceUpdateProgress& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::DeviceUpdateProgress>();
  str.set_mode(cdr_stream::stream_mode::move, key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DeviceUpdateProgress& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.progressPercent()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!max_string(streamer, instance.deviceName(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!max_string(streamer, instance.deviceId(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.status()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.errorReason()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::seres::ota_duc_service::DeviceUpdateProgress& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::DeviceUpdateProgress>();
  str.set_mode(cdr_stream::stream_mode::max, key);
  return max(str, instance, props.data()); 
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::UpdateProgress>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__UpdateProgress = get_type_props<::seres::ota_duc_service::UpdateProgress>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::UpdateProgress& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.allFinished()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, false))
        return false;
      {
      uint32_t se_1 = uint32_t(instance.progressLists().size());
      if (!write(streamer, se_1))
        return false;
      for (uint32_t i_1 = 0; i_1 < se_1; i_1++) {
      if (!write(streamer, instance.progressLists()[i_1], prop))
        return false;
      }  //i_1
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::seres::ota_duc_service::UpdateProgress& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::UpdateProgress>();
  str.set_mode(cdr_stream::stream_mode::write, key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::UpdateProgress& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.allFinished()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, false))
        return false;
      {
      uint32_t se_1 = uint32_t(instance.progressLists().size());
      if (!read(streamer, se_1))
        return false;
      instance.progressLists().resize(se_1);
      for (uint32_t i_1 = 0; i_1 < se_1; i_1++) {
      if (!read(streamer, instance.progressLists()[i_1], prop))
        return false;
      }  //i_1
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::seres::ota_duc_service::UpdateProgress& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::UpdateProgress>();
  str.set_mode(cdr_stream::stream_mode::read, key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::UpdateProgress& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.allFinished()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, false))
        return false;
      {
      uint32_t se_1 = uint32_t(instance.progressLists().size());
      if (!move(streamer, se_1))
        return false;
      for (uint32_t i_1 = 0; i_1 < se_1; i_1++) {
      if (!move(streamer, instance.progressLists()[i_1], prop))
        return false;
      }  //i_1
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::seres::ota_duc_service::UpdateProgress& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::UpdateProgress>();
  str.set_mode(cdr_stream::stream_mode::move, key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::UpdateProgress& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.allFinished()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, false))
        return false;
      {
      uint32_t se_1 = 0;
      if (!max(streamer, se_1))
        return false;
      for (uint32_t i_1 = 0; i_1 < se_1; i_1++) {
      if (!max(streamer, instance.progressLists()[i_1], prop))
        return false;
      }  //i_1
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      streamer.position(SIZE_MAX);
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::seres::ota_duc_service::UpdateProgress& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::UpdateProgress>();
  str.set_mode(cdr_stream::stream_mode::max, key);
  return max(str, instance, props.data()); 
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::RollbackComponentList>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__RollbackComponentList = get_type_props<::seres::ota_duc_service::RollbackComponentList>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::RollbackComponentList& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, false))
        return false;
      {
      uint32_t se_1 = uint32_t(instance.rollbackLists().size());
      if (!write(streamer, se_1))
        return false;
      for (uint32_t i_1 = 0; i_1 < se_1; i_1++) {
      if (!write(streamer, instance.rollbackLists()[i_1], prop))
        return false;
      }  //i_1
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::seres::ota_duc_service::RollbackComponentList& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::RollbackComponentList>();
  str.set_mode(cdr_stream::stream_mode::write, key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::RollbackComponentList& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, false))
        return false;
      {
      uint32_t se_1 = uint32_t(instance.rollbackLists().size());
      if (!read(streamer, se_1))
        return false;
      instance.rollbackLists().resize(se_1);
      for (uint32_t i_1 = 0; i_1 < se_1; i_1++) {
      if (!read(streamer, instance.rollbackLists()[i_1], prop))
        return false;
      }  //i_1
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::seres::ota_duc_service::RollbackComponentList& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::RollbackComponentList>();
  str.set_mode(cdr_stream::stream_mode::read, key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::RollbackComponentList& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, false))
        return false;
      {
      uint32_t se_1 = uint32_t(instance.rollbackLists().size());
      if (!move(streamer, se_1))
        return false;
      for (uint32_t i_1 = 0; i_1 < se_1; i_1++) {
      if (!move(streamer, instance.rollbackLists()[i_1], prop))
        return false;
      }  //i_1
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::seres::ota_duc_service::RollbackComponentList& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::RollbackComponentList>();
  str.set_mode(cdr_stream::stream_mode::move, key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::RollbackComponentList& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, false))
        return false;
      {
      uint32_t se_1 = 0;
      if (!max(streamer, se_1))
        return false;
      for (uint32_t i_1 = 0; i_1 < se_1; i_1++) {
      if (!max(streamer, instance.rollbackLists()[i_1], prop))
        return false;
      }  //i_1
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      streamer.position(SIZE_MAX);
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::seres::ota_duc_service::RollbackComponentList& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::RollbackComponentList>();
  str.set_mode(cdr_stream::stream_mode::max, key);
  return max(str, instance, props.data()); 
}

template<>
::seres::ota_duc_service::OTA_TopicData enum_conversion<::seres::ota_duc_service::OTA_TopicData>(uint32_t in);

template<>
const propvec &get_type_props<::seres::ota_duc_service::OTA_DucDataUnion>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__OTA_DucDataUnion = get_type_props<::seres::ota_duc_service::OTA_DucDataUnion>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::OTA_DucDataUnion& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  const auto d = instance._d();
  if (!write(streamer, d))
    return false;
  switch(d)
  {
    case ::seres::ota_duc_service::OTA_TopicData::INVENTORY_RESULT:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::InventoryResult>()[0]);
      if (!write(streamer, instance.inventoryResult(), prop))
        return false;
      }
      break;
    case ::seres::ota_duc_service::OTA_TopicData::DOWNLOAD_PROGRESS:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DownloadProgress>()[0]);
      if (!write(streamer, instance.downloadProgress(), prop))
        return false;
      }
      break;
    case ::seres::ota_duc_service::OTA_TopicData::UZIP_PACKAGES_RESULT:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::UzipPackagesResult>()[0]);
      if (!write(streamer, instance.uzipPackagesResult(), prop))
        return false;
      }
      break;
    case ::seres::ota_duc_service::OTA_TopicData::PACKAGES_VERIFY_RESULT:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::PackagesVerifyResult>()[0]);
      if (!write(streamer, instance.packagesVerifyResult(), prop))
        return false;
      }
      break;
    case ::seres::ota_duc_service::OTA_TopicData::CHECK_UPDATE_CONDITION_RESULT:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::CheckUpdateConditionResult>()[0]);
      if (!write(streamer, instance.checkUpdateConditionResult(), prop))
        return false;
      }
      break;
    case ::seres::ota_duc_service::OTA_TopicData::UPDATE_PROGRESS:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::UpdateProgress>()[0]);
      if (!write(streamer, instance.updateProgress(), prop))
        return false;
      }
      break;
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::seres::ota_duc_service::OTA_DucDataUnion& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::OTA_DucDataUnion>();
  str.set_mode(cdr_stream::stream_mode::write, key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::OTA_DucDataUnion& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto d = instance._d();
  if (!read(streamer, d))
    return false;
  switch(d)
  {
    case ::seres::ota_duc_service::OTA_TopicData::INVENTORY_RESULT:
    {
      auto obj = decl_ref_type(instance.inventoryResult())();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::InventoryResult>()[0]);
      if (!read(streamer, obj, prop))
        return false;
      instance.inventoryResult(obj);
    }
    break;
    case ::seres::ota_duc_service::OTA_TopicData::DOWNLOAD_PROGRESS:
    {
      auto obj = decl_ref_type(instance.downloadProgress())();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DownloadProgress>()[0]);
      if (!read(streamer, obj, prop))
        return false;
      instance.downloadProgress(obj);
    }
    break;
    case ::seres::ota_duc_service::OTA_TopicData::UZIP_PACKAGES_RESULT:
    {
      auto obj = decl_ref_type(instance.uzipPackagesResult())();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::UzipPackagesResult>()[0]);
      if (!read(streamer, obj, prop))
        return false;
      instance.uzipPackagesResult(obj);
    }
    break;
    case ::seres::ota_duc_service::OTA_TopicData::PACKAGES_VERIFY_RESULT:
    {
      auto obj = decl_ref_type(instance.packagesVerifyResult())();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::PackagesVerifyResult>()[0]);
      if (!read(streamer, obj, prop))
        return false;
      instance.packagesVerifyResult(obj);
    }
    break;
    case ::seres::ota_duc_service::OTA_TopicData::CHECK_UPDATE_CONDITION_RESULT:
    {
      auto obj = decl_ref_type(instance.checkUpdateConditionResult())();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::CheckUpdateConditionResult>()[0]);
      if (!read(streamer, obj, prop))
        return false;
      instance.checkUpdateConditionResult(obj);
    }
    break;
    case ::seres::ota_duc_service::OTA_TopicData::UPDATE_PROGRESS:
    {
      auto obj = decl_ref_type(instance.updateProgress())();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::UpdateProgress>()[0]);
      if (!read(streamer, obj, prop))
        return false;
      instance.updateProgress(obj);
    }
    break;
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::seres::ota_duc_service::OTA_DucDataUnion& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::OTA_DucDataUnion>();
  str.set_mode(cdr_stream::stream_mode::read, key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::OTA_DucDataUnion& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  const auto d = instance._d();
  if (!move(streamer, d))
    return false;
  switch(d)
  {
    case ::seres::ota_duc_service::OTA_TopicData::INVENTORY_RESULT:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::InventoryResult>()[0]);
      if (!move(streamer, instance.inventoryResult(), prop))
        return false;
      }
      break;
    case ::seres::ota_duc_service::OTA_TopicData::DOWNLOAD_PROGRESS:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DownloadProgress>()[0]);
      if (!move(streamer, instance.downloadProgress(), prop))
        return false;
      }
      break;
    case ::seres::ota_duc_service::OTA_TopicData::UZIP_PACKAGES_RESULT:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::UzipPackagesResult>()[0]);
      if (!move(streamer, instance.uzipPackagesResult(), prop))
        return false;
      }
      break;
    case ::seres::ota_duc_service::OTA_TopicData::PACKAGES_VERIFY_RESULT:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::PackagesVerifyResult>()[0]);
      if (!move(streamer, instance.packagesVerifyResult(), prop))
        return false;
      }
      break;
    case ::seres::ota_duc_service::OTA_TopicData::CHECK_UPDATE_CONDITION_RESULT:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::CheckUpdateConditionResult>()[0]);
      if (!move(streamer, instance.checkUpdateConditionResult(), prop))
        return false;
      }
      break;
    case ::seres::ota_duc_service::OTA_TopicData::UPDATE_PROGRESS:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::UpdateProgress>()[0]);
      if (!move(streamer, instance.updateProgress(), prop))
        return false;
      }
      break;
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::seres::ota_duc_service::OTA_DucDataUnion& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::OTA_DucDataUnion>();
  str.set_mode(cdr_stream::stream_mode::move, key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::OTA_DucDataUnion& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  const auto d = instance._d();
  if (!max(streamer, d))
    return false;
  size_t union_max = streamer.position();
  size_t alignment_max = streamer.alignment();
  {
    size_t pos = streamer.position();
    size_t alignment = streamer.alignment();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::InventoryResult>()[0]);
      if (!max(streamer, instance.inventoryResult(), prop))
        return false;
    if (union_max < streamer.position()) {
      union_max = streamer.position();
      alignment_max = streamer.alignment();
    }
    streamer.position(pos);
    streamer.alignment(alignment);
  }
  {
    size_t pos = streamer.position();
    size_t alignment = streamer.alignment();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DownloadProgress>()[0]);
      if (!max(streamer, instance.downloadProgress(), prop))
        return false;
    if (union_max < streamer.position()) {
      union_max = streamer.position();
      alignment_max = streamer.alignment();
    }
    streamer.position(pos);
    streamer.alignment(alignment);
  }
  {
    size_t pos = streamer.position();
    size_t alignment = streamer.alignment();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::UzipPackagesResult>()[0]);
      if (!max(streamer, instance.uzipPackagesResult(), prop))
        return false;
    if (union_max < streamer.position()) {
      union_max = streamer.position();
      alignment_max = streamer.alignment();
    }
    streamer.position(pos);
    streamer.alignment(alignment);
  }
  {
    size_t pos = streamer.position();
    size_t alignment = streamer.alignment();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::PackagesVerifyResult>()[0]);
      if (!max(streamer, instance.packagesVerifyResult(), prop))
        return false;
    if (union_max < streamer.position()) {
      union_max = streamer.position();
      alignment_max = streamer.alignment();
    }
    streamer.position(pos);
    streamer.alignment(alignment);
  }
  {
    size_t pos = streamer.position();
    size_t alignment = streamer.alignment();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::CheckUpdateConditionResult>()[0]);
      if (!max(streamer, instance.checkUpdateConditionResult(), prop))
        return false;
    if (union_max < streamer.position()) {
      union_max = streamer.position();
      alignment_max = streamer.alignment();
    }
    streamer.position(pos);
    streamer.alignment(alignment);
  }
  {
    size_t pos = streamer.position();
    size_t alignment = streamer.alignment();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::UpdateProgress>()[0]);
      if (!max(streamer, instance.updateProgress(), prop))
        return false;
    if (union_max < streamer.position()) {
      union_max = streamer.position();
      alignment_max = streamer.alignment();
    }
    streamer.position(pos);
    streamer.alignment(alignment);
  }
  streamer.position(union_max);
  streamer.alignment(alignment_max);
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::seres::ota_duc_service::OTA_DucDataUnion& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::OTA_DucDataUnion>();
  str.set_mode(cdr_stream::stream_mode::max, key);
  return max(str, instance, props.data()); 
}

} //namespace cdr
} //namespace core
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

#endif // DDSCXX_OTA_DUCDATA_HPP_50E87A31A3DDFA27360AF3FE235813AC
