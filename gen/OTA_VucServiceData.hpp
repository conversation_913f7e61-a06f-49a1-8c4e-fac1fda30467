/****************************************************************

  Generated by Eclipse Cyclone DDS IDL to CXX Translator
  File name: /home/<USER>/workspace/fotamaster/gen/OTA_VucServiceData.idl
  Source: OTA_VucServiceData.hpp
  Cyclone DDS: v0.11.0

*****************************************************************/
#ifndef DDSCXX_OTA_VUCSERVICEDATA_HPP_DF4ED10AC7951DF940198A5264CFBE5A
#define DDSCXX_OTA_VUCSERVICEDATA_HPP_DF4ED10AC7951DF940198A5264CFBE5A

#include <utility>
#include <ostream>
#include <cstdint>
#include <vector>
#include <string>
#include <variant>
#include <dds/core/Exception.hpp>


namespace seres
{
namespace ota_vuc_service
{
const std::string kVucServiceTopicName = "OtaVucService.topic";

const uint32_t kVucDomainID = 42;

class ComponentInfo
{
private:
 std::string partNumber_;
 std::string softwareVersion_;
 std::string supplierCode_;
 std::string ecuName_;
 std::string serialNumber_;
 std::string hardwareVersion_;
 std::string ecuBatchNumber_;
 std::string bootloaderVersion_;
 std::string backupVersion_;

public:
  ComponentInfo() = default;

  explicit ComponentInfo(
    const std::string& partNumber,
    const std::string& softwareVersion,
    const std::string& supplierCode,
    const std::string& ecuName,
    const std::string& serialNumber,
    const std::string& hardwareVersion,
    const std::string& ecuBatchNumber,
    const std::string& bootloaderVersion,
    const std::string& backupVersion) :
    partNumber_(partNumber),
    softwareVersion_(softwareVersion),
    supplierCode_(supplierCode),
    ecuName_(ecuName),
    serialNumber_(serialNumber),
    hardwareVersion_(hardwareVersion),
    ecuBatchNumber_(ecuBatchNumber),
    bootloaderVersion_(bootloaderVersion),
    backupVersion_(backupVersion) { }

  const std::string& partNumber() const { return this->partNumber_; }
  std::string& partNumber() { return this->partNumber_; }
  void partNumber(const std::string& _val_) { this->partNumber_ = _val_; }
  void partNumber(std::string&& _val_) { this->partNumber_ = std::move(_val_); }
  const std::string& softwareVersion() const { return this->softwareVersion_; }
  std::string& softwareVersion() { return this->softwareVersion_; }
  void softwareVersion(const std::string& _val_) { this->softwareVersion_ = _val_; }
  void softwareVersion(std::string&& _val_) { this->softwareVersion_ = std::move(_val_); }
  const std::string& supplierCode() const { return this->supplierCode_; }
  std::string& supplierCode() { return this->supplierCode_; }
  void supplierCode(const std::string& _val_) { this->supplierCode_ = _val_; }
  void supplierCode(std::string&& _val_) { this->supplierCode_ = std::move(_val_); }
  const std::string& ecuName() const { return this->ecuName_; }
  std::string& ecuName() { return this->ecuName_; }
  void ecuName(const std::string& _val_) { this->ecuName_ = _val_; }
  void ecuName(std::string&& _val_) { this->ecuName_ = std::move(_val_); }
  const std::string& serialNumber() const { return this->serialNumber_; }
  std::string& serialNumber() { return this->serialNumber_; }
  void serialNumber(const std::string& _val_) { this->serialNumber_ = _val_; }
  void serialNumber(std::string&& _val_) { this->serialNumber_ = std::move(_val_); }
  const std::string& hardwareVersion() const { return this->hardwareVersion_; }
  std::string& hardwareVersion() { return this->hardwareVersion_; }
  void hardwareVersion(const std::string& _val_) { this->hardwareVersion_ = _val_; }
  void hardwareVersion(std::string&& _val_) { this->hardwareVersion_ = std::move(_val_); }
  const std::string& ecuBatchNumber() const { return this->ecuBatchNumber_; }
  std::string& ecuBatchNumber() { return this->ecuBatchNumber_; }
  void ecuBatchNumber(const std::string& _val_) { this->ecuBatchNumber_ = _val_; }
  void ecuBatchNumber(std::string&& _val_) { this->ecuBatchNumber_ = std::move(_val_); }
  const std::string& bootloaderVersion() const { return this->bootloaderVersion_; }
  std::string& bootloaderVersion() { return this->bootloaderVersion_; }
  void bootloaderVersion(const std::string& _val_) { this->bootloaderVersion_ = _val_; }
  void bootloaderVersion(std::string&& _val_) { this->bootloaderVersion_ = std::move(_val_); }
  const std::string& backupVersion() const { return this->backupVersion_; }
  std::string& backupVersion() { return this->backupVersion_; }
  void backupVersion(const std::string& _val_) { this->backupVersion_ = _val_; }
  void backupVersion(std::string&& _val_) { this->backupVersion_ = std::move(_val_); }

  bool operator==(const ComponentInfo& _other) const
  {
    (void) _other;
    return partNumber_ == _other.partNumber_ &&
      softwareVersion_ == _other.softwareVersion_ &&
      supplierCode_ == _other.supplierCode_ &&
      ecuName_ == _other.ecuName_ &&
      serialNumber_ == _other.serialNumber_ &&
      hardwareVersion_ == _other.hardwareVersion_ &&
      ecuBatchNumber_ == _other.ecuBatchNumber_ &&
      bootloaderVersion_ == _other.bootloaderVersion_ &&
      backupVersion_ == _other.backupVersion_;
  }

  bool operator!=(const ComponentInfo& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, ComponentInfo const& rhs);

class InventoryInfoList
{
private:
 std::vector<::seres::ota_vuc_service::ComponentInfo> componentLists_;

public:
  InventoryInfoList() = default;

  explicit InventoryInfoList(
    const std::vector<::seres::ota_vuc_service::ComponentInfo>& componentLists) :
    componentLists_(componentLists) { }

  const std::vector<::seres::ota_vuc_service::ComponentInfo>& componentLists() const { return this->componentLists_; }
  std::vector<::seres::ota_vuc_service::ComponentInfo>& componentLists() { return this->componentLists_; }
  void componentLists(const std::vector<::seres::ota_vuc_service::ComponentInfo>& _val_) { this->componentLists_ = _val_; }
  void componentLists(std::vector<::seres::ota_vuc_service::ComponentInfo>&& _val_) { this->componentLists_ = std::move(_val_); }

  bool operator==(const InventoryInfoList& _other) const
  {
    (void) _other;
    return componentLists_ == _other.componentLists_;
  }

  bool operator!=(const InventoryInfoList& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, InventoryInfoList const& rhs);

class UpgradeTask
{
private:
 std::string upgradePackageInfo_;
 std::string upgradeInfo_;

public:
  UpgradeTask() = default;

  explicit UpgradeTask(
    const std::string& upgradePackageInfo,
    const std::string& upgradeInfo) :
    upgradePackageInfo_(upgradePackageInfo),
    upgradeInfo_(upgradeInfo) { }

  const std::string& upgradePackageInfo() const { return this->upgradePackageInfo_; }
  std::string& upgradePackageInfo() { return this->upgradePackageInfo_; }
  void upgradePackageInfo(const std::string& _val_) { this->upgradePackageInfo_ = _val_; }
  void upgradePackageInfo(std::string&& _val_) { this->upgradePackageInfo_ = std::move(_val_); }
  const std::string& upgradeInfo() const { return this->upgradeInfo_; }
  std::string& upgradeInfo() { return this->upgradeInfo_; }
  void upgradeInfo(const std::string& _val_) { this->upgradeInfo_ = _val_; }
  void upgradeInfo(std::string&& _val_) { this->upgradeInfo_ = std::move(_val_); }

  bool operator==(const UpgradeTask& _other) const
  {
    (void) _other;
    return upgradePackageInfo_ == _other.upgradePackageInfo_ &&
      upgradeInfo_ == _other.upgradeInfo_;
  }

  bool operator!=(const UpgradeTask& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, UpgradeTask const& rhs);

enum class DownloadFailReason
{
  kDiskSpaceNotEnough,
  kNetworkAnomaly,
  kNetworkInterrupt,
  kIllegalArgs,
  kVerifyFailed};

std::ostream& operator<<(std::ostream& os, DownloadFailReason const& rhs);

class TotalDownloadProgress
{
private:
 int32_t progress_ = 0;

public:
  TotalDownloadProgress() = default;

  explicit TotalDownloadProgress(
    int32_t progress) :
    progress_(progress) { }

  int32_t progress() const { return this->progress_; }
  int32_t& progress() { return this->progress_; }
  void progress(int32_t _val_) { this->progress_ = _val_; }

  bool operator==(const TotalDownloadProgress& _other) const
  {
    (void) _other;
    return progress_ == _other.progress_;
  }

  bool operator!=(const TotalDownloadProgress& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, TotalDownloadProgress const& rhs);

enum class UseSeamlessUpgrade
{
  kNo,
  kYes};

std::ostream& operator<<(std::ostream& os, UseSeamlessUpgrade const& rhs);

class SeamlessUpgradeMode
{
private:
 ::seres::ota_vuc_service::UseSeamlessUpgrade use_seamless_upgrade_ = ::seres::ota_vuc_service::UseSeamlessUpgrade::kNo;

public:
  SeamlessUpgradeMode() = default;

  explicit SeamlessUpgradeMode(
    ::seres::ota_vuc_service::UseSeamlessUpgrade use_seamless_upgrade) :
    use_seamless_upgrade_(use_seamless_upgrade) { }

  ::seres::ota_vuc_service::UseSeamlessUpgrade use_seamless_upgrade() const { return this->use_seamless_upgrade_; }
  ::seres::ota_vuc_service::UseSeamlessUpgrade& use_seamless_upgrade() { return this->use_seamless_upgrade_; }
  void use_seamless_upgrade(::seres::ota_vuc_service::UseSeamlessUpgrade _val_) { this->use_seamless_upgrade_ = _val_; }

  bool operator==(const SeamlessUpgradeMode& _other) const
  {
    (void) _other;
    return use_seamless_upgrade_ == _other.use_seamless_upgrade_;
  }

  bool operator!=(const SeamlessUpgradeMode& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, SeamlessUpgradeMode const& rhs);

class NewVersionNotify
{
private:
 std::string history_version_;
 std::string new_version_;

public:
  NewVersionNotify() = default;

  explicit NewVersionNotify(
    const std::string& history_version,
    const std::string& new_version) :
    history_version_(history_version),
    new_version_(new_version) { }

  const std::string& history_version() const { return this->history_version_; }
  std::string& history_version() { return this->history_version_; }
  void history_version(const std::string& _val_) { this->history_version_ = _val_; }
  void history_version(std::string&& _val_) { this->history_version_ = std::move(_val_); }
  const std::string& new_version() const { return this->new_version_; }
  std::string& new_version() { return this->new_version_; }
  void new_version(const std::string& _val_) { this->new_version_ = _val_; }
  void new_version(std::string&& _val_) { this->new_version_ = std::move(_val_); }

  bool operator==(const NewVersionNotify& _other) const
  {
    (void) _other;
    return history_version_ == _other.history_version_ &&
      new_version_ == _other.new_version_;
  }

  bool operator!=(const NewVersionNotify& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, NewVersionNotify const& rhs);

enum class UpgradeModeE
{
  kImmediately,
  kAppointment};

std::ostream& operator<<(std::ostream& os, UpgradeModeE const& rhs);

class UpgradeModeCtrl
{
private:
 ::seres::ota_vuc_service::UpgradeModeE upgrade_mode_ = ::seres::ota_vuc_service::UpgradeModeE::kImmediately;
 std::string time_;

public:
  UpgradeModeCtrl() = default;

  explicit UpgradeModeCtrl(
    ::seres::ota_vuc_service::UpgradeModeE upgrade_mode,
    const std::string& time) :
    upgrade_mode_(upgrade_mode),
    time_(time) { }

  ::seres::ota_vuc_service::UpgradeModeE upgrade_mode() const { return this->upgrade_mode_; }
  ::seres::ota_vuc_service::UpgradeModeE& upgrade_mode() { return this->upgrade_mode_; }
  void upgrade_mode(::seres::ota_vuc_service::UpgradeModeE _val_) { this->upgrade_mode_ = _val_; }
  const std::string& time() const { return this->time_; }
  std::string& time() { return this->time_; }
  void time(const std::string& _val_) { this->time_ = _val_; }
  void time(std::string&& _val_) { this->time_ = std::move(_val_); }

  bool operator==(const UpgradeModeCtrl& _other) const
  {
    (void) _other;
    return upgrade_mode_ == _other.upgrade_mode_ &&
      time_ == _other.time_;
  }

  bool operator!=(const UpgradeModeCtrl& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, UpgradeModeCtrl const& rhs);

class UpgradeCountdown
{
private:
 uint32_t time_ = 0;

public:
  UpgradeCountdown() = default;

  explicit UpgradeCountdown(
    uint32_t time) :
    time_(time) { }

  uint32_t time() const { return this->time_; }
  uint32_t& time() { return this->time_; }
  void time(uint32_t _val_) { this->time_ = _val_; }

  bool operator==(const UpgradeCountdown& _other) const
  {
    (void) _other;
    return time_ == _other.time_;
  }

  bool operator!=(const UpgradeCountdown& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, UpgradeCountdown const& rhs);

enum class UpgradeNotifyE
{
  kNotUpgrading,
  kUpgrading};

std::ostream& operator<<(std::ostream& os, UpgradeNotifyE const& rhs);

class UpgradeNotify
{
private:
 ::seres::ota_vuc_service::UpgradeNotifyE upgrade_notify_ = ::seres::ota_vuc_service::UpgradeNotifyE::kNotUpgrading;

public:
  UpgradeNotify() = default;

  explicit UpgradeNotify(
    ::seres::ota_vuc_service::UpgradeNotifyE upgrade_notify) :
    upgrade_notify_(upgrade_notify) { }

  ::seres::ota_vuc_service::UpgradeNotifyE upgrade_notify() const { return this->upgrade_notify_; }
  ::seres::ota_vuc_service::UpgradeNotifyE& upgrade_notify() { return this->upgrade_notify_; }
  void upgrade_notify(::seres::ota_vuc_service::UpgradeNotifyE _val_) { this->upgrade_notify_ = _val_; }

  bool operator==(const UpgradeNotify& _other) const
  {
    (void) _other;
    return upgrade_notify_ == _other.upgrade_notify_;
  }

  bool operator!=(const UpgradeNotify& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, UpgradeNotify const& rhs);

enum class UpgradeFailReason
{
  kPowerOnFailure,
  kOther};

std::ostream& operator<<(std::ostream& os, UpgradeFailReason const& rhs);

class UpgradeProgressItem
{
private:
 uint8_t progress_ = 0;
 std::string device_id_;
 ::seres::ota_vuc_service::UpgradeFailReason fail_reason_ = ::seres::ota_vuc_service::UpgradeFailReason::kPowerOnFailure;

public:
  UpgradeProgressItem() = default;

  explicit UpgradeProgressItem(
    uint8_t progress,
    const std::string& device_id,
    ::seres::ota_vuc_service::UpgradeFailReason fail_reason) :
    progress_(progress),
    device_id_(device_id),
    fail_reason_(fail_reason) { }

  uint8_t progress() const { return this->progress_; }
  uint8_t& progress() { return this->progress_; }
  void progress(uint8_t _val_) { this->progress_ = _val_; }
  const std::string& device_id() const { return this->device_id_; }
  std::string& device_id() { return this->device_id_; }
  void device_id(const std::string& _val_) { this->device_id_ = _val_; }
  void device_id(std::string&& _val_) { this->device_id_ = std::move(_val_); }
  ::seres::ota_vuc_service::UpgradeFailReason fail_reason() const { return this->fail_reason_; }
  ::seres::ota_vuc_service::UpgradeFailReason& fail_reason() { return this->fail_reason_; }
  void fail_reason(::seres::ota_vuc_service::UpgradeFailReason _val_) { this->fail_reason_ = _val_; }

  bool operator==(const UpgradeProgressItem& _other) const
  {
    (void) _other;
    return progress_ == _other.progress_ &&
      device_id_ == _other.device_id_ &&
      fail_reason_ == _other.fail_reason_;
  }

  bool operator!=(const UpgradeProgressItem& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, UpgradeProgressItem const& rhs);

class UpgradeProgress
{
private:
 int32_t total_progress_ = 0;
 std::vector<::seres::ota_vuc_service::UpgradeProgressItem> progress_lists_;

public:
  UpgradeProgress() = default;

  explicit UpgradeProgress(
    int32_t total_progress,
    const std::vector<::seres::ota_vuc_service::UpgradeProgressItem>& progress_lists) :
    total_progress_(total_progress),
    progress_lists_(progress_lists) { }

  int32_t total_progress() const { return this->total_progress_; }
  int32_t& total_progress() { return this->total_progress_; }
  void total_progress(int32_t _val_) { this->total_progress_ = _val_; }
  const std::vector<::seres::ota_vuc_service::UpgradeProgressItem>& progress_lists() const { return this->progress_lists_; }
  std::vector<::seres::ota_vuc_service::UpgradeProgressItem>& progress_lists() { return this->progress_lists_; }
  void progress_lists(const std::vector<::seres::ota_vuc_service::UpgradeProgressItem>& _val_) { this->progress_lists_ = _val_; }
  void progress_lists(std::vector<::seres::ota_vuc_service::UpgradeProgressItem>&& _val_) { this->progress_lists_ = std::move(_val_); }

  bool operator==(const UpgradeProgress& _other) const
  {
    (void) _other;
    return total_progress_ == _other.total_progress_ &&
      progress_lists_ == _other.progress_lists_;
  }

  bool operator!=(const UpgradeProgress& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, UpgradeProgress const& rhs);

enum class VucServiceTopic
{
  kReportInventoryInfo,
  kSeamlessUpgradeMode,
  kUpgradeTaskNotify,
  kReportDownloadProgress,
  kReportDownloadFailReason,
  kUpgradeModeCtrl,
  kRequestUpgradeCountdown,
  kUpgradeNotify,
  kReportUpgradeProgress};

std::ostream& operator<<(std::ostream& os, VucServiceTopic const& rhs);

class VucServiceDataUnion
{
private:
  ::seres::ota_vuc_service::VucServiceTopic m__d;

  std::variant<::seres::ota_vuc_service::InventoryInfoList, ::seres::ota_vuc_service::SeamlessUpgradeMode, ::seres::ota_vuc_service::UpgradeTask, ::seres::ota_vuc_service::TotalDownloadProgress, ::seres::ota_vuc_service::DownloadFailReason, ::seres::ota_vuc_service::UpgradeModeCtrl, ::seres::ota_vuc_service::UpgradeCountdown, ::seres::ota_vuc_service::UpgradeNotify, ::seres::ota_vuc_service::UpgradeProgress> m__u;

  static const ::seres::ota_vuc_service::VucServiceTopic _default_discriminator = ::seres::ota_vuc_service::VucServiceTopic::kReportInventoryInfo;

  static ::seres::ota_vuc_service::VucServiceTopic _is_discriminator(const ::seres::ota_vuc_service::VucServiceTopic d)
  {
    switch (d) {
      case ::seres::ota_vuc_service::VucServiceTopic::kReportInventoryInfo:
        return ::seres::ota_vuc_service::VucServiceTopic::kReportInventoryInfo;
      case ::seres::ota_vuc_service::VucServiceTopic::kSeamlessUpgradeMode:
        return ::seres::ota_vuc_service::VucServiceTopic::kSeamlessUpgradeMode;
      case ::seres::ota_vuc_service::VucServiceTopic::kUpgradeTaskNotify:
        return ::seres::ota_vuc_service::VucServiceTopic::kUpgradeTaskNotify;
      case ::seres::ota_vuc_service::VucServiceTopic::kReportDownloadProgress:
        return ::seres::ota_vuc_service::VucServiceTopic::kReportDownloadProgress;
      case ::seres::ota_vuc_service::VucServiceTopic::kReportDownloadFailReason:
        return ::seres::ota_vuc_service::VucServiceTopic::kReportDownloadFailReason;
      case ::seres::ota_vuc_service::VucServiceTopic::kUpgradeModeCtrl:
        return ::seres::ota_vuc_service::VucServiceTopic::kUpgradeModeCtrl;
      case ::seres::ota_vuc_service::VucServiceTopic::kRequestUpgradeCountdown:
        return ::seres::ota_vuc_service::VucServiceTopic::kRequestUpgradeCountdown;
      case ::seres::ota_vuc_service::VucServiceTopic::kUpgradeNotify:
        return ::seres::ota_vuc_service::VucServiceTopic::kUpgradeNotify;
      case ::seres::ota_vuc_service::VucServiceTopic::kReportUpgradeProgress:
        return ::seres::ota_vuc_service::VucServiceTopic::kReportUpgradeProgress;
    }
    return _default_discriminator;
  }

  static bool _is_compatible_discriminator(const ::seres::ota_vuc_service::VucServiceTopic d1, const ::seres::ota_vuc_service::VucServiceTopic d2)
  {
    return _is_discriminator(d1) == _is_discriminator(d2);
  }

public:
  VucServiceDataUnion() :
      m__d(_default_discriminator),
      m__u()
 { }

  ::seres::ota_vuc_service::VucServiceTopic _d() const
  {
    return m__d;
  }

  void _d(::seres::ota_vuc_service::VucServiceTopic d)
  {
    if (!_is_compatible_discriminator(m__d, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator value does not match current discriminator");
    }
    m__d = d;
  }

  const ::seres::ota_vuc_service::InventoryInfoList &inventory_info_list() const
  {
    if (!_is_compatible_discriminator(m__d, ::seres::ota_vuc_service::VucServiceTopic::kReportInventoryInfo)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_vuc_service::InventoryInfoList>(m__u);
  }

  ::seres::ota_vuc_service::InventoryInfoList& inventory_info_list()
  {
    if (!_is_compatible_discriminator(m__d, ::seres::ota_vuc_service::VucServiceTopic::kReportInventoryInfo)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_vuc_service::InventoryInfoList>(m__u);
  }

  void inventory_info_list(const ::seres::ota_vuc_service::InventoryInfoList& u, ::seres::ota_vuc_service::VucServiceTopic d = ::seres::ota_vuc_service::VucServiceTopic::kReportInventoryInfo)
  {
    if (!_is_compatible_discriminator(::seres::ota_vuc_service::VucServiceTopic::kReportInventoryInfo, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  void inventory_info_list(::seres::ota_vuc_service::InventoryInfoList&& u, ::seres::ota_vuc_service::VucServiceTopic d = ::seres::ota_vuc_service::VucServiceTopic::kReportInventoryInfo)
  {
    if (!_is_compatible_discriminator(::seres::ota_vuc_service::VucServiceTopic::kReportInventoryInfo, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  const ::seres::ota_vuc_service::SeamlessUpgradeMode &seamless_upgrade_mode() const
  {
    if (!_is_compatible_discriminator(m__d, ::seres::ota_vuc_service::VucServiceTopic::kSeamlessUpgradeMode)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_vuc_service::SeamlessUpgradeMode>(m__u);
  }

  ::seres::ota_vuc_service::SeamlessUpgradeMode& seamless_upgrade_mode()
  {
    if (!_is_compatible_discriminator(m__d, ::seres::ota_vuc_service::VucServiceTopic::kSeamlessUpgradeMode)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_vuc_service::SeamlessUpgradeMode>(m__u);
  }

  void seamless_upgrade_mode(const ::seres::ota_vuc_service::SeamlessUpgradeMode& u, ::seres::ota_vuc_service::VucServiceTopic d = ::seres::ota_vuc_service::VucServiceTopic::kSeamlessUpgradeMode)
  {
    if (!_is_compatible_discriminator(::seres::ota_vuc_service::VucServiceTopic::kSeamlessUpgradeMode, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  void seamless_upgrade_mode(::seres::ota_vuc_service::SeamlessUpgradeMode&& u, ::seres::ota_vuc_service::VucServiceTopic d = ::seres::ota_vuc_service::VucServiceTopic::kSeamlessUpgradeMode)
  {
    if (!_is_compatible_discriminator(::seres::ota_vuc_service::VucServiceTopic::kSeamlessUpgradeMode, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  const ::seres::ota_vuc_service::UpgradeTask &upgrade_task() const
  {
    if (!_is_compatible_discriminator(m__d, ::seres::ota_vuc_service::VucServiceTopic::kUpgradeTaskNotify)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_vuc_service::UpgradeTask>(m__u);
  }

  ::seres::ota_vuc_service::UpgradeTask& upgrade_task()
  {
    if (!_is_compatible_discriminator(m__d, ::seres::ota_vuc_service::VucServiceTopic::kUpgradeTaskNotify)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_vuc_service::UpgradeTask>(m__u);
  }

  void upgrade_task(const ::seres::ota_vuc_service::UpgradeTask& u, ::seres::ota_vuc_service::VucServiceTopic d = ::seres::ota_vuc_service::VucServiceTopic::kUpgradeTaskNotify)
  {
    if (!_is_compatible_discriminator(::seres::ota_vuc_service::VucServiceTopic::kUpgradeTaskNotify, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  void upgrade_task(::seres::ota_vuc_service::UpgradeTask&& u, ::seres::ota_vuc_service::VucServiceTopic d = ::seres::ota_vuc_service::VucServiceTopic::kUpgradeTaskNotify)
  {
    if (!_is_compatible_discriminator(::seres::ota_vuc_service::VucServiceTopic::kUpgradeTaskNotify, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  const ::seres::ota_vuc_service::TotalDownloadProgress &downlaod_progress() const
  {
    if (!_is_compatible_discriminator(m__d, ::seres::ota_vuc_service::VucServiceTopic::kReportDownloadProgress)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_vuc_service::TotalDownloadProgress>(m__u);
  }

  ::seres::ota_vuc_service::TotalDownloadProgress& downlaod_progress()
  {
    if (!_is_compatible_discriminator(m__d, ::seres::ota_vuc_service::VucServiceTopic::kReportDownloadProgress)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_vuc_service::TotalDownloadProgress>(m__u);
  }

  void downlaod_progress(const ::seres::ota_vuc_service::TotalDownloadProgress& u, ::seres::ota_vuc_service::VucServiceTopic d = ::seres::ota_vuc_service::VucServiceTopic::kReportDownloadProgress)
  {
    if (!_is_compatible_discriminator(::seres::ota_vuc_service::VucServiceTopic::kReportDownloadProgress, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  void downlaod_progress(::seres::ota_vuc_service::TotalDownloadProgress&& u, ::seres::ota_vuc_service::VucServiceTopic d = ::seres::ota_vuc_service::VucServiceTopic::kReportDownloadProgress)
  {
    if (!_is_compatible_discriminator(::seres::ota_vuc_service::VucServiceTopic::kReportDownloadProgress, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  ::seres::ota_vuc_service::DownloadFailReason download_fail_reason() const
  {
    if (!_is_compatible_discriminator(m__d, ::seres::ota_vuc_service::VucServiceTopic::kReportDownloadFailReason)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_vuc_service::DownloadFailReason>(m__u);
  }

  ::seres::ota_vuc_service::DownloadFailReason& download_fail_reason()
  {
    if (!_is_compatible_discriminator(m__d, ::seres::ota_vuc_service::VucServiceTopic::kReportDownloadFailReason)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_vuc_service::DownloadFailReason>(m__u);
  }

  void download_fail_reason(::seres::ota_vuc_service::DownloadFailReason u, ::seres::ota_vuc_service::VucServiceTopic d = ::seres::ota_vuc_service::VucServiceTopic::kReportDownloadFailReason)
  {
    if (!_is_compatible_discriminator(::seres::ota_vuc_service::VucServiceTopic::kReportDownloadFailReason, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  const ::seres::ota_vuc_service::UpgradeModeCtrl &upgrade_mode_ctrl() const
  {
    if (!_is_compatible_discriminator(m__d, ::seres::ota_vuc_service::VucServiceTopic::kUpgradeModeCtrl)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_vuc_service::UpgradeModeCtrl>(m__u);
  }

  ::seres::ota_vuc_service::UpgradeModeCtrl& upgrade_mode_ctrl()
  {
    if (!_is_compatible_discriminator(m__d, ::seres::ota_vuc_service::VucServiceTopic::kUpgradeModeCtrl)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_vuc_service::UpgradeModeCtrl>(m__u);
  }

  void upgrade_mode_ctrl(const ::seres::ota_vuc_service::UpgradeModeCtrl& u, ::seres::ota_vuc_service::VucServiceTopic d = ::seres::ota_vuc_service::VucServiceTopic::kUpgradeModeCtrl)
  {
    if (!_is_compatible_discriminator(::seres::ota_vuc_service::VucServiceTopic::kUpgradeModeCtrl, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  void upgrade_mode_ctrl(::seres::ota_vuc_service::UpgradeModeCtrl&& u, ::seres::ota_vuc_service::VucServiceTopic d = ::seres::ota_vuc_service::VucServiceTopic::kUpgradeModeCtrl)
  {
    if (!_is_compatible_discriminator(::seres::ota_vuc_service::VucServiceTopic::kUpgradeModeCtrl, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  const ::seres::ota_vuc_service::UpgradeCountdown &upgrade_countdown() const
  {
    if (!_is_compatible_discriminator(m__d, ::seres::ota_vuc_service::VucServiceTopic::kRequestUpgradeCountdown)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_vuc_service::UpgradeCountdown>(m__u);
  }

  ::seres::ota_vuc_service::UpgradeCountdown& upgrade_countdown()
  {
    if (!_is_compatible_discriminator(m__d, ::seres::ota_vuc_service::VucServiceTopic::kRequestUpgradeCountdown)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_vuc_service::UpgradeCountdown>(m__u);
  }

  void upgrade_countdown(const ::seres::ota_vuc_service::UpgradeCountdown& u, ::seres::ota_vuc_service::VucServiceTopic d = ::seres::ota_vuc_service::VucServiceTopic::kRequestUpgradeCountdown)
  {
    if (!_is_compatible_discriminator(::seres::ota_vuc_service::VucServiceTopic::kRequestUpgradeCountdown, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  void upgrade_countdown(::seres::ota_vuc_service::UpgradeCountdown&& u, ::seres::ota_vuc_service::VucServiceTopic d = ::seres::ota_vuc_service::VucServiceTopic::kRequestUpgradeCountdown)
  {
    if (!_is_compatible_discriminator(::seres::ota_vuc_service::VucServiceTopic::kRequestUpgradeCountdown, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  const ::seres::ota_vuc_service::UpgradeNotify &upgrade_notify() const
  {
    if (!_is_compatible_discriminator(m__d, ::seres::ota_vuc_service::VucServiceTopic::kUpgradeNotify)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_vuc_service::UpgradeNotify>(m__u);
  }

  ::seres::ota_vuc_service::UpgradeNotify& upgrade_notify()
  {
    if (!_is_compatible_discriminator(m__d, ::seres::ota_vuc_service::VucServiceTopic::kUpgradeNotify)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_vuc_service::UpgradeNotify>(m__u);
  }

  void upgrade_notify(const ::seres::ota_vuc_service::UpgradeNotify& u, ::seres::ota_vuc_service::VucServiceTopic d = ::seres::ota_vuc_service::VucServiceTopic::kUpgradeNotify)
  {
    if (!_is_compatible_discriminator(::seres::ota_vuc_service::VucServiceTopic::kUpgradeNotify, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  void upgrade_notify(::seres::ota_vuc_service::UpgradeNotify&& u, ::seres::ota_vuc_service::VucServiceTopic d = ::seres::ota_vuc_service::VucServiceTopic::kUpgradeNotify)
  {
    if (!_is_compatible_discriminator(::seres::ota_vuc_service::VucServiceTopic::kUpgradeNotify, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  const ::seres::ota_vuc_service::UpgradeProgress &upgrade_progress() const
  {
    if (!_is_compatible_discriminator(m__d, ::seres::ota_vuc_service::VucServiceTopic::kReportUpgradeProgress)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_vuc_service::UpgradeProgress>(m__u);
  }

  ::seres::ota_vuc_service::UpgradeProgress& upgrade_progress()
  {
    if (!_is_compatible_discriminator(m__d, ::seres::ota_vuc_service::VucServiceTopic::kReportUpgradeProgress)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_vuc_service::UpgradeProgress>(m__u);
  }

  void upgrade_progress(const ::seres::ota_vuc_service::UpgradeProgress& u, ::seres::ota_vuc_service::VucServiceTopic d = ::seres::ota_vuc_service::VucServiceTopic::kReportUpgradeProgress)
  {
    if (!_is_compatible_discriminator(::seres::ota_vuc_service::VucServiceTopic::kReportUpgradeProgress, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  void upgrade_progress(::seres::ota_vuc_service::UpgradeProgress&& u, ::seres::ota_vuc_service::VucServiceTopic d = ::seres::ota_vuc_service::VucServiceTopic::kReportUpgradeProgress)
  {
    if (!_is_compatible_discriminator(::seres::ota_vuc_service::VucServiceTopic::kReportUpgradeProgress, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  bool operator==(const VucServiceDataUnion& _other) const
  {
    if (_d() != _other._d()) return false;
    switch (_d()) {
      case ::seres::ota_vuc_service::VucServiceTopic::kReportInventoryInfo:
        return inventory_info_list() == _other.inventory_info_list();
      case ::seres::ota_vuc_service::VucServiceTopic::kSeamlessUpgradeMode:
        return seamless_upgrade_mode() == _other.seamless_upgrade_mode();
      case ::seres::ota_vuc_service::VucServiceTopic::kUpgradeTaskNotify:
        return upgrade_task() == _other.upgrade_task();
      case ::seres::ota_vuc_service::VucServiceTopic::kReportDownloadProgress:
        return downlaod_progress() == _other.downlaod_progress();
      case ::seres::ota_vuc_service::VucServiceTopic::kReportDownloadFailReason:
        return download_fail_reason() == _other.download_fail_reason();
      case ::seres::ota_vuc_service::VucServiceTopic::kUpgradeModeCtrl:
        return upgrade_mode_ctrl() == _other.upgrade_mode_ctrl();
      case ::seres::ota_vuc_service::VucServiceTopic::kRequestUpgradeCountdown:
        return upgrade_countdown() == _other.upgrade_countdown();
      case ::seres::ota_vuc_service::VucServiceTopic::kUpgradeNotify:
        return upgrade_notify() == _other.upgrade_notify();
      case ::seres::ota_vuc_service::VucServiceTopic::kReportUpgradeProgress:
        return upgrade_progress() == _other.upgrade_progress();
    }
    return true;
  }

  bool operator!=(const VucServiceDataUnion& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, VucServiceDataUnion const& rhs);
} //namespace ota_vuc_service

} //namespace seres

#include "dds/topic/TopicTraits.hpp"
#include "org/eclipse/cyclonedds/topic/datatopic.hpp"

namespace org {
namespace eclipse {
namespace cyclonedds {
namespace topic {

template <> constexpr const char* TopicTraits<::seres::ota_vuc_service::ComponentInfo>::getTypeName()
{
  return "seres::ota_vuc_service::ComponentInfo";
}

template <> constexpr bool TopicTraits<::seres::ota_vuc_service::ComponentInfo>::isSelfContained()
{
  return false;
}

template <> constexpr bool TopicTraits<::seres::ota_vuc_service::ComponentInfo>::isKeyless()
{
  return true;
}

template <> constexpr const char* TopicTraits<::seres::ota_vuc_service::InventoryInfoList>::getTypeName()
{
  return "seres::ota_vuc_service::InventoryInfoList";
}

template <> constexpr bool TopicTraits<::seres::ota_vuc_service::InventoryInfoList>::isSelfContained()
{
  return false;
}

template <> constexpr bool TopicTraits<::seres::ota_vuc_service::InventoryInfoList>::isKeyless()
{
  return true;
}

template <> constexpr const char* TopicTraits<::seres::ota_vuc_service::UpgradeTask>::getTypeName()
{
  return "seres::ota_vuc_service::UpgradeTask";
}

template <> constexpr bool TopicTraits<::seres::ota_vuc_service::UpgradeTask>::isSelfContained()
{
  return false;
}

template <> constexpr bool TopicTraits<::seres::ota_vuc_service::UpgradeTask>::isKeyless()
{
  return true;
}

template <> constexpr const char* TopicTraits<::seres::ota_vuc_service::TotalDownloadProgress>::getTypeName()
{
  return "seres::ota_vuc_service::TotalDownloadProgress";
}

template <> constexpr bool TopicTraits<::seres::ota_vuc_service::TotalDownloadProgress>::isKeyless()
{
  return true;
}

template <> constexpr const char* TopicTraits<::seres::ota_vuc_service::SeamlessUpgradeMode>::getTypeName()
{
  return "seres::ota_vuc_service::SeamlessUpgradeMode";
}

template <> constexpr bool TopicTraits<::seres::ota_vuc_service::SeamlessUpgradeMode>::isKeyless()
{
  return true;
}

template <> constexpr const char* TopicTraits<::seres::ota_vuc_service::NewVersionNotify>::getTypeName()
{
  return "seres::ota_vuc_service::NewVersionNotify";
}

template <> constexpr bool TopicTraits<::seres::ota_vuc_service::NewVersionNotify>::isSelfContained()
{
  return false;
}

template <> constexpr bool TopicTraits<::seres::ota_vuc_service::NewVersionNotify>::isKeyless()
{
  return true;
}

template <> constexpr const char* TopicTraits<::seres::ota_vuc_service::UpgradeModeCtrl>::getTypeName()
{
  return "seres::ota_vuc_service::UpgradeModeCtrl";
}

template <> constexpr bool TopicTraits<::seres::ota_vuc_service::UpgradeModeCtrl>::isSelfContained()
{
  return false;
}

template <> constexpr bool TopicTraits<::seres::ota_vuc_service::UpgradeModeCtrl>::isKeyless()
{
  return true;
}

template <> constexpr const char* TopicTraits<::seres::ota_vuc_service::UpgradeCountdown>::getTypeName()
{
  return "seres::ota_vuc_service::UpgradeCountdown";
}

template <> constexpr bool TopicTraits<::seres::ota_vuc_service::UpgradeCountdown>::isKeyless()
{
  return true;
}

template <> constexpr const char* TopicTraits<::seres::ota_vuc_service::UpgradeNotify>::getTypeName()
{
  return "seres::ota_vuc_service::UpgradeNotify";
}

template <> constexpr bool TopicTraits<::seres::ota_vuc_service::UpgradeNotify>::isKeyless()
{
  return true;
}

template <> constexpr const char* TopicTraits<::seres::ota_vuc_service::UpgradeProgressItem>::getTypeName()
{
  return "seres::ota_vuc_service::UpgradeProgressItem";
}

template <> constexpr bool TopicTraits<::seres::ota_vuc_service::UpgradeProgressItem>::isSelfContained()
{
  return false;
}

template <> constexpr bool TopicTraits<::seres::ota_vuc_service::UpgradeProgressItem>::isKeyless()
{
  return true;
}

template <> constexpr const char* TopicTraits<::seres::ota_vuc_service::UpgradeProgress>::getTypeName()
{
  return "seres::ota_vuc_service::UpgradeProgress";
}

template <> constexpr bool TopicTraits<::seres::ota_vuc_service::UpgradeProgress>::isSelfContained()
{
  return false;
}

template <> constexpr bool TopicTraits<::seres::ota_vuc_service::UpgradeProgress>::isKeyless()
{
  return true;
}

template <> constexpr const char* TopicTraits<::seres::ota_vuc_service::VucServiceDataUnion>::getTypeName()
{
  return "seres::ota_vuc_service::VucServiceDataUnion";
}

template <> constexpr bool TopicTraits<::seres::ota_vuc_service::VucServiceDataUnion>::isSelfContained()
{
  return false;
}

template <> constexpr bool TopicTraits<::seres::ota_vuc_service::VucServiceDataUnion>::isKeyless()
{
  return true;
}

} //namespace topic
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

namespace dds {
namespace topic {

template <>
struct topic_type_name<::seres::ota_vuc_service::ComponentInfo>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::seres::ota_vuc_service::ComponentInfo>::getTypeName();
    }
};

template <>
struct topic_type_name<::seres::ota_vuc_service::InventoryInfoList>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::seres::ota_vuc_service::InventoryInfoList>::getTypeName();
    }
};

template <>
struct topic_type_name<::seres::ota_vuc_service::UpgradeTask>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::seres::ota_vuc_service::UpgradeTask>::getTypeName();
    }
};

template <>
struct topic_type_name<::seres::ota_vuc_service::TotalDownloadProgress>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::seres::ota_vuc_service::TotalDownloadProgress>::getTypeName();
    }
};

template <>
struct topic_type_name<::seres::ota_vuc_service::SeamlessUpgradeMode>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::seres::ota_vuc_service::SeamlessUpgradeMode>::getTypeName();
    }
};

template <>
struct topic_type_name<::seres::ota_vuc_service::NewVersionNotify>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::seres::ota_vuc_service::NewVersionNotify>::getTypeName();
    }
};

template <>
struct topic_type_name<::seres::ota_vuc_service::UpgradeModeCtrl>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::seres::ota_vuc_service::UpgradeModeCtrl>::getTypeName();
    }
};

template <>
struct topic_type_name<::seres::ota_vuc_service::UpgradeCountdown>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::seres::ota_vuc_service::UpgradeCountdown>::getTypeName();
    }
};

template <>
struct topic_type_name<::seres::ota_vuc_service::UpgradeNotify>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::seres::ota_vuc_service::UpgradeNotify>::getTypeName();
    }
};

template <>
struct topic_type_name<::seres::ota_vuc_service::UpgradeProgressItem>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::seres::ota_vuc_service::UpgradeProgressItem>::getTypeName();
    }
};

template <>
struct topic_type_name<::seres::ota_vuc_service::UpgradeProgress>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::seres::ota_vuc_service::UpgradeProgress>::getTypeName();
    }
};

template <>
struct topic_type_name<::seres::ota_vuc_service::VucServiceDataUnion>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::seres::ota_vuc_service::VucServiceDataUnion>::getTypeName();
    }
};

}
}

REGISTER_TOPIC_TYPE(::seres::ota_vuc_service::ComponentInfo)
REGISTER_TOPIC_TYPE(::seres::ota_vuc_service::InventoryInfoList)
REGISTER_TOPIC_TYPE(::seres::ota_vuc_service::UpgradeTask)
REGISTER_TOPIC_TYPE(::seres::ota_vuc_service::TotalDownloadProgress)
REGISTER_TOPIC_TYPE(::seres::ota_vuc_service::SeamlessUpgradeMode)
REGISTER_TOPIC_TYPE(::seres::ota_vuc_service::NewVersionNotify)
REGISTER_TOPIC_TYPE(::seres::ota_vuc_service::UpgradeModeCtrl)
REGISTER_TOPIC_TYPE(::seres::ota_vuc_service::UpgradeCountdown)
REGISTER_TOPIC_TYPE(::seres::ota_vuc_service::UpgradeNotify)
REGISTER_TOPIC_TYPE(::seres::ota_vuc_service::UpgradeProgressItem)
REGISTER_TOPIC_TYPE(::seres::ota_vuc_service::UpgradeProgress)
REGISTER_TOPIC_TYPE(::seres::ota_vuc_service::VucServiceDataUnion)

namespace org{
namespace eclipse{
namespace cyclonedds{
namespace core{
namespace cdr{

template<>
const propvec &get_type_props<::seres::ota_vuc_service::ComponentInfo>();

namespace {
  static const volatile propvec &properties___seres__ota_vuc_service__ComponentInfo = get_type_props<::seres::ota_vuc_service::ComponentInfo>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_vuc_service::ComponentInfo& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write_string(streamer, instance.partNumber(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!write_string(streamer, instance.softwareVersion(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!write_string(streamer, instance.supplierCode(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!write_string(streamer, instance.ecuName(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!write_string(streamer, instance.serialNumber(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 5:
      if (!streamer.start_member(*prop))
        return false;
      if (!write_string(streamer, instance.hardwareVersion(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 6:
      if (!streamer.start_member(*prop))
        return false;
      if (!write_string(streamer, instance.ecuBatchNumber(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 7:
      if (!streamer.start_member(*prop))
        return false;
      if (!write_string(streamer, instance.bootloaderVersion(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 8:
      if (!streamer.start_member(*prop))
        return false;
      if (!write_string(streamer, instance.backupVersion(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::seres::ota_vuc_service::ComponentInfo& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_vuc_service::ComponentInfo>();
  str.set_mode(cdr_stream::stream_mode::write, key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_vuc_service::ComponentInfo& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read_string(streamer, instance.partNumber(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!read_string(streamer, instance.softwareVersion(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!read_string(streamer, instance.supplierCode(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!read_string(streamer, instance.ecuName(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!read_string(streamer, instance.serialNumber(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 5:
      if (!streamer.start_member(*prop))
        return false;
      if (!read_string(streamer, instance.hardwareVersion(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 6:
      if (!streamer.start_member(*prop))
        return false;
      if (!read_string(streamer, instance.ecuBatchNumber(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 7:
      if (!streamer.start_member(*prop))
        return false;
      if (!read_string(streamer, instance.bootloaderVersion(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 8:
      if (!streamer.start_member(*prop))
        return false;
      if (!read_string(streamer, instance.backupVersion(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::seres::ota_vuc_service::ComponentInfo& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_vuc_service::ComponentInfo>();
  str.set_mode(cdr_stream::stream_mode::read, key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_vuc_service::ComponentInfo& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move_string(streamer, instance.partNumber(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!move_string(streamer, instance.softwareVersion(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!move_string(streamer, instance.supplierCode(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!move_string(streamer, instance.ecuName(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!move_string(streamer, instance.serialNumber(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 5:
      if (!streamer.start_member(*prop))
        return false;
      if (!move_string(streamer, instance.hardwareVersion(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 6:
      if (!streamer.start_member(*prop))
        return false;
      if (!move_string(streamer, instance.ecuBatchNumber(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 7:
      if (!streamer.start_member(*prop))
        return false;
      if (!move_string(streamer, instance.bootloaderVersion(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 8:
      if (!streamer.start_member(*prop))
        return false;
      if (!move_string(streamer, instance.backupVersion(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::seres::ota_vuc_service::ComponentInfo& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_vuc_service::ComponentInfo>();
  str.set_mode(cdr_stream::stream_mode::move, key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_vuc_service::ComponentInfo& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max_string(streamer, instance.partNumber(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!max_string(streamer, instance.softwareVersion(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!max_string(streamer, instance.supplierCode(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!max_string(streamer, instance.ecuName(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!max_string(streamer, instance.serialNumber(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 5:
      if (!streamer.start_member(*prop))
        return false;
      if (!max_string(streamer, instance.hardwareVersion(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 6:
      if (!streamer.start_member(*prop))
        return false;
      if (!max_string(streamer, instance.ecuBatchNumber(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 7:
      if (!streamer.start_member(*prop))
        return false;
      if (!max_string(streamer, instance.bootloaderVersion(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 8:
      if (!streamer.start_member(*prop))
        return false;
      if (!max_string(streamer, instance.backupVersion(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::seres::ota_vuc_service::ComponentInfo& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_vuc_service::ComponentInfo>();
  str.set_mode(cdr_stream::stream_mode::max, key);
  return max(str, instance, props.data()); 
}

template<>
const propvec &get_type_props<::seres::ota_vuc_service::InventoryInfoList>();

namespace {
  static const volatile propvec &properties___seres__ota_vuc_service__InventoryInfoList = get_type_props<::seres::ota_vuc_service::InventoryInfoList>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_vuc_service::InventoryInfoList& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, false))
        return false;
      {
      uint32_t se_1 = uint32_t(instance.componentLists().size());
      if (!write(streamer, se_1))
        return false;
      for (uint32_t i_1 = 0; i_1 < se_1; i_1++) {
      if (!write(streamer, instance.componentLists()[i_1], prop))
        return false;
      }  //i_1
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::seres::ota_vuc_service::InventoryInfoList& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_vuc_service::InventoryInfoList>();
  str.set_mode(cdr_stream::stream_mode::write, key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_vuc_service::InventoryInfoList& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, false))
        return false;
      {
      uint32_t se_1 = uint32_t(instance.componentLists().size());
      if (!read(streamer, se_1))
        return false;
      instance.componentLists().resize(se_1);
      for (uint32_t i_1 = 0; i_1 < se_1; i_1++) {
      if (!read(streamer, instance.componentLists()[i_1], prop))
        return false;
      }  //i_1
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::seres::ota_vuc_service::InventoryInfoList& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_vuc_service::InventoryInfoList>();
  str.set_mode(cdr_stream::stream_mode::read, key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_vuc_service::InventoryInfoList& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, false))
        return false;
      {
      uint32_t se_1 = uint32_t(instance.componentLists().size());
      if (!move(streamer, se_1))
        return false;
      for (uint32_t i_1 = 0; i_1 < se_1; i_1++) {
      if (!move(streamer, instance.componentLists()[i_1], prop))
        return false;
      }  //i_1
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::seres::ota_vuc_service::InventoryInfoList& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_vuc_service::InventoryInfoList>();
  str.set_mode(cdr_stream::stream_mode::move, key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_vuc_service::InventoryInfoList& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, false))
        return false;
      {
      uint32_t se_1 = 0;
      if (!max(streamer, se_1))
        return false;
      for (uint32_t i_1 = 0; i_1 < se_1; i_1++) {
      if (!max(streamer, instance.componentLists()[i_1], prop))
        return false;
      }  //i_1
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      streamer.position(SIZE_MAX);
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::seres::ota_vuc_service::InventoryInfoList& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_vuc_service::InventoryInfoList>();
  str.set_mode(cdr_stream::stream_mode::max, key);
  return max(str, instance, props.data()); 
}

template<>
const propvec &get_type_props<::seres::ota_vuc_service::UpgradeTask>();

namespace {
  static const volatile propvec &properties___seres__ota_vuc_service__UpgradeTask = get_type_props<::seres::ota_vuc_service::UpgradeTask>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_vuc_service::UpgradeTask& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write_string(streamer, instance.upgradePackageInfo(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!write_string(streamer, instance.upgradeInfo(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::seres::ota_vuc_service::UpgradeTask& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_vuc_service::UpgradeTask>();
  str.set_mode(cdr_stream::stream_mode::write, key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_vuc_service::UpgradeTask& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read_string(streamer, instance.upgradePackageInfo(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!read_string(streamer, instance.upgradeInfo(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::seres::ota_vuc_service::UpgradeTask& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_vuc_service::UpgradeTask>();
  str.set_mode(cdr_stream::stream_mode::read, key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_vuc_service::UpgradeTask& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move_string(streamer, instance.upgradePackageInfo(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!move_string(streamer, instance.upgradeInfo(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::seres::ota_vuc_service::UpgradeTask& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_vuc_service::UpgradeTask>();
  str.set_mode(cdr_stream::stream_mode::move, key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_vuc_service::UpgradeTask& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max_string(streamer, instance.upgradePackageInfo(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!max_string(streamer, instance.upgradeInfo(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::seres::ota_vuc_service::UpgradeTask& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_vuc_service::UpgradeTask>();
  str.set_mode(cdr_stream::stream_mode::max, key);
  return max(str, instance, props.data()); 
}

template<>
::seres::ota_vuc_service::DownloadFailReason enum_conversion<::seres::ota_vuc_service::DownloadFailReason>(uint32_t in);

template<>
const propvec &get_type_props<::seres::ota_vuc_service::TotalDownloadProgress>();

namespace {
  static const volatile propvec &properties___seres__ota_vuc_service__TotalDownloadProgress = get_type_props<::seres::ota_vuc_service::TotalDownloadProgress>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_vuc_service::TotalDownloadProgress& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.progress()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::seres::ota_vuc_service::TotalDownloadProgress& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_vuc_service::TotalDownloadProgress>();
  str.set_mode(cdr_stream::stream_mode::write, key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_vuc_service::TotalDownloadProgress& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.progress()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::seres::ota_vuc_service::TotalDownloadProgress& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_vuc_service::TotalDownloadProgress>();
  str.set_mode(cdr_stream::stream_mode::read, key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_vuc_service::TotalDownloadProgress& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.progress()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::seres::ota_vuc_service::TotalDownloadProgress& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_vuc_service::TotalDownloadProgress>();
  str.set_mode(cdr_stream::stream_mode::move, key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_vuc_service::TotalDownloadProgress& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.progress()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::seres::ota_vuc_service::TotalDownloadProgress& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_vuc_service::TotalDownloadProgress>();
  str.set_mode(cdr_stream::stream_mode::max, key);
  return max(str, instance, props.data()); 
}

template<>
::seres::ota_vuc_service::UseSeamlessUpgrade enum_conversion<::seres::ota_vuc_service::UseSeamlessUpgrade>(uint32_t in);

template<>
const propvec &get_type_props<::seres::ota_vuc_service::SeamlessUpgradeMode>();

namespace {
  static const volatile propvec &properties___seres__ota_vuc_service__SeamlessUpgradeMode = get_type_props<::seres::ota_vuc_service::SeamlessUpgradeMode>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_vuc_service::SeamlessUpgradeMode& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.use_seamless_upgrade()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::seres::ota_vuc_service::SeamlessUpgradeMode& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_vuc_service::SeamlessUpgradeMode>();
  str.set_mode(cdr_stream::stream_mode::write, key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_vuc_service::SeamlessUpgradeMode& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.use_seamless_upgrade()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::seres::ota_vuc_service::SeamlessUpgradeMode& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_vuc_service::SeamlessUpgradeMode>();
  str.set_mode(cdr_stream::stream_mode::read, key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_vuc_service::SeamlessUpgradeMode& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.use_seamless_upgrade()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::seres::ota_vuc_service::SeamlessUpgradeMode& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_vuc_service::SeamlessUpgradeMode>();
  str.set_mode(cdr_stream::stream_mode::move, key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_vuc_service::SeamlessUpgradeMode& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.use_seamless_upgrade()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::seres::ota_vuc_service::SeamlessUpgradeMode& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_vuc_service::SeamlessUpgradeMode>();
  str.set_mode(cdr_stream::stream_mode::max, key);
  return max(str, instance, props.data()); 
}

template<>
const propvec &get_type_props<::seres::ota_vuc_service::NewVersionNotify>();

namespace {
  static const volatile propvec &properties___seres__ota_vuc_service__NewVersionNotify = get_type_props<::seres::ota_vuc_service::NewVersionNotify>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_vuc_service::NewVersionNotify& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write_string(streamer, instance.history_version(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!write_string(streamer, instance.new_version(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::seres::ota_vuc_service::NewVersionNotify& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_vuc_service::NewVersionNotify>();
  str.set_mode(cdr_stream::stream_mode::write, key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_vuc_service::NewVersionNotify& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read_string(streamer, instance.history_version(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!read_string(streamer, instance.new_version(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::seres::ota_vuc_service::NewVersionNotify& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_vuc_service::NewVersionNotify>();
  str.set_mode(cdr_stream::stream_mode::read, key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_vuc_service::NewVersionNotify& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move_string(streamer, instance.history_version(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!move_string(streamer, instance.new_version(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::seres::ota_vuc_service::NewVersionNotify& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_vuc_service::NewVersionNotify>();
  str.set_mode(cdr_stream::stream_mode::move, key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_vuc_service::NewVersionNotify& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max_string(streamer, instance.history_version(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!max_string(streamer, instance.new_version(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::seres::ota_vuc_service::NewVersionNotify& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_vuc_service::NewVersionNotify>();
  str.set_mode(cdr_stream::stream_mode::max, key);
  return max(str, instance, props.data()); 
}

template<>
::seres::ota_vuc_service::UpgradeModeE enum_conversion<::seres::ota_vuc_service::UpgradeModeE>(uint32_t in);

template<>
const propvec &get_type_props<::seres::ota_vuc_service::UpgradeModeCtrl>();

namespace {
  static const volatile propvec &properties___seres__ota_vuc_service__UpgradeModeCtrl = get_type_props<::seres::ota_vuc_service::UpgradeModeCtrl>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_vuc_service::UpgradeModeCtrl& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.upgrade_mode()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!write_string(streamer, instance.time(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::seres::ota_vuc_service::UpgradeModeCtrl& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_vuc_service::UpgradeModeCtrl>();
  str.set_mode(cdr_stream::stream_mode::write, key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_vuc_service::UpgradeModeCtrl& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.upgrade_mode()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!read_string(streamer, instance.time(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::seres::ota_vuc_service::UpgradeModeCtrl& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_vuc_service::UpgradeModeCtrl>();
  str.set_mode(cdr_stream::stream_mode::read, key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_vuc_service::UpgradeModeCtrl& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.upgrade_mode()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!move_string(streamer, instance.time(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::seres::ota_vuc_service::UpgradeModeCtrl& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_vuc_service::UpgradeModeCtrl>();
  str.set_mode(cdr_stream::stream_mode::move, key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_vuc_service::UpgradeModeCtrl& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.upgrade_mode()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!max_string(streamer, instance.time(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::seres::ota_vuc_service::UpgradeModeCtrl& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_vuc_service::UpgradeModeCtrl>();
  str.set_mode(cdr_stream::stream_mode::max, key);
  return max(str, instance, props.data()); 
}

template<>
const propvec &get_type_props<::seres::ota_vuc_service::UpgradeCountdown>();

namespace {
  static const volatile propvec &properties___seres__ota_vuc_service__UpgradeCountdown = get_type_props<::seres::ota_vuc_service::UpgradeCountdown>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_vuc_service::UpgradeCountdown& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.time()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::seres::ota_vuc_service::UpgradeCountdown& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_vuc_service::UpgradeCountdown>();
  str.set_mode(cdr_stream::stream_mode::write, key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_vuc_service::UpgradeCountdown& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.time()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::seres::ota_vuc_service::UpgradeCountdown& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_vuc_service::UpgradeCountdown>();
  str.set_mode(cdr_stream::stream_mode::read, key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_vuc_service::UpgradeCountdown& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.time()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::seres::ota_vuc_service::UpgradeCountdown& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_vuc_service::UpgradeCountdown>();
  str.set_mode(cdr_stream::stream_mode::move, key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_vuc_service::UpgradeCountdown& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.time()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::seres::ota_vuc_service::UpgradeCountdown& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_vuc_service::UpgradeCountdown>();
  str.set_mode(cdr_stream::stream_mode::max, key);
  return max(str, instance, props.data()); 
}

template<>
::seres::ota_vuc_service::UpgradeNotifyE enum_conversion<::seres::ota_vuc_service::UpgradeNotifyE>(uint32_t in);

template<>
const propvec &get_type_props<::seres::ota_vuc_service::UpgradeNotify>();

namespace {
  static const volatile propvec &properties___seres__ota_vuc_service__UpgradeNotify = get_type_props<::seres::ota_vuc_service::UpgradeNotify>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_vuc_service::UpgradeNotify& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.upgrade_notify()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::seres::ota_vuc_service::UpgradeNotify& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_vuc_service::UpgradeNotify>();
  str.set_mode(cdr_stream::stream_mode::write, key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_vuc_service::UpgradeNotify& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.upgrade_notify()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::seres::ota_vuc_service::UpgradeNotify& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_vuc_service::UpgradeNotify>();
  str.set_mode(cdr_stream::stream_mode::read, key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_vuc_service::UpgradeNotify& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.upgrade_notify()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::seres::ota_vuc_service::UpgradeNotify& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_vuc_service::UpgradeNotify>();
  str.set_mode(cdr_stream::stream_mode::move, key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_vuc_service::UpgradeNotify& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.upgrade_notify()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::seres::ota_vuc_service::UpgradeNotify& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_vuc_service::UpgradeNotify>();
  str.set_mode(cdr_stream::stream_mode::max, key);
  return max(str, instance, props.data()); 
}

template<>
::seres::ota_vuc_service::UpgradeFailReason enum_conversion<::seres::ota_vuc_service::UpgradeFailReason>(uint32_t in);

template<>
const propvec &get_type_props<::seres::ota_vuc_service::UpgradeProgressItem>();

namespace {
  static const volatile propvec &properties___seres__ota_vuc_service__UpgradeProgressItem = get_type_props<::seres::ota_vuc_service::UpgradeProgressItem>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_vuc_service::UpgradeProgressItem& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.progress()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!write_string(streamer, instance.device_id(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.fail_reason()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::seres::ota_vuc_service::UpgradeProgressItem& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_vuc_service::UpgradeProgressItem>();
  str.set_mode(cdr_stream::stream_mode::write, key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_vuc_service::UpgradeProgressItem& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.progress()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!read_string(streamer, instance.device_id(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.fail_reason()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::seres::ota_vuc_service::UpgradeProgressItem& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_vuc_service::UpgradeProgressItem>();
  str.set_mode(cdr_stream::stream_mode::read, key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_vuc_service::UpgradeProgressItem& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.progress()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!move_string(streamer, instance.device_id(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.fail_reason()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::seres::ota_vuc_service::UpgradeProgressItem& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_vuc_service::UpgradeProgressItem>();
  str.set_mode(cdr_stream::stream_mode::move, key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_vuc_service::UpgradeProgressItem& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.progress()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!max_string(streamer, instance.device_id(), 0))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.fail_reason()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::seres::ota_vuc_service::UpgradeProgressItem& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_vuc_service::UpgradeProgressItem>();
  str.set_mode(cdr_stream::stream_mode::max, key);
  return max(str, instance, props.data()); 
}

template<>
const propvec &get_type_props<::seres::ota_vuc_service::UpgradeProgress>();

namespace {
  static const volatile propvec &properties___seres__ota_vuc_service__UpgradeProgress = get_type_props<::seres::ota_vuc_service::UpgradeProgress>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_vuc_service::UpgradeProgress& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.total_progress()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, false))
        return false;
      {
      uint32_t se_1 = uint32_t(instance.progress_lists().size());
      if (!write(streamer, se_1))
        return false;
      for (uint32_t i_1 = 0; i_1 < se_1; i_1++) {
      if (!write(streamer, instance.progress_lists()[i_1], prop))
        return false;
      }  //i_1
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::seres::ota_vuc_service::UpgradeProgress& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_vuc_service::UpgradeProgress>();
  str.set_mode(cdr_stream::stream_mode::write, key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_vuc_service::UpgradeProgress& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.total_progress()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, false))
        return false;
      {
      uint32_t se_1 = uint32_t(instance.progress_lists().size());
      if (!read(streamer, se_1))
        return false;
      instance.progress_lists().resize(se_1);
      for (uint32_t i_1 = 0; i_1 < se_1; i_1++) {
      if (!read(streamer, instance.progress_lists()[i_1], prop))
        return false;
      }  //i_1
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::seres::ota_vuc_service::UpgradeProgress& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_vuc_service::UpgradeProgress>();
  str.set_mode(cdr_stream::stream_mode::read, key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_vuc_service::UpgradeProgress& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.total_progress()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, false))
        return false;
      {
      uint32_t se_1 = uint32_t(instance.progress_lists().size());
      if (!move(streamer, se_1))
        return false;
      for (uint32_t i_1 = 0; i_1 < se_1; i_1++) {
      if (!move(streamer, instance.progress_lists()[i_1], prop))
        return false;
      }  //i_1
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::seres::ota_vuc_service::UpgradeProgress& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_vuc_service::UpgradeProgress>();
  str.set_mode(cdr_stream::stream_mode::move, key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_vuc_service::UpgradeProgress& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.total_progress()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, false))
        return false;
      {
      uint32_t se_1 = 0;
      if (!max(streamer, se_1))
        return false;
      for (uint32_t i_1 = 0; i_1 < se_1; i_1++) {
      if (!max(streamer, instance.progress_lists()[i_1], prop))
        return false;
      }  //i_1
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      streamer.position(SIZE_MAX);
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::seres::ota_vuc_service::UpgradeProgress& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_vuc_service::UpgradeProgress>();
  str.set_mode(cdr_stream::stream_mode::max, key);
  return max(str, instance, props.data()); 
}

template<>
::seres::ota_vuc_service::VucServiceTopic enum_conversion<::seres::ota_vuc_service::VucServiceTopic>(uint32_t in);

template<>
const propvec &get_type_props<::seres::ota_vuc_service::VucServiceDataUnion>();

namespace {
  static const volatile propvec &properties___seres__ota_vuc_service__VucServiceDataUnion = get_type_props<::seres::ota_vuc_service::VucServiceDataUnion>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_vuc_service::VucServiceDataUnion& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  const auto d = instance._d();
  if (!write(streamer, d))
    return false;
  switch(d)
  {
    case ::seres::ota_vuc_service::VucServiceTopic::kReportInventoryInfo:
      {
      const auto &prop = &(get_type_props<::seres::ota_vuc_service::InventoryInfoList>()[0]);
      if (!write(streamer, instance.inventory_info_list(), prop))
        return false;
      }
      break;
    case ::seres::ota_vuc_service::VucServiceTopic::kSeamlessUpgradeMode:
      {
      const auto &prop = &(get_type_props<::seres::ota_vuc_service::SeamlessUpgradeMode>()[0]);
      if (!write(streamer, instance.seamless_upgrade_mode(), prop))
        return false;
      }
      break;
    case ::seres::ota_vuc_service::VucServiceTopic::kUpgradeTaskNotify:
      {
      const auto &prop = &(get_type_props<::seres::ota_vuc_service::UpgradeTask>()[0]);
      if (!write(streamer, instance.upgrade_task(), prop))
        return false;
      }
      break;
    case ::seres::ota_vuc_service::VucServiceTopic::kReportDownloadProgress:
      {
      const auto &prop = &(get_type_props<::seres::ota_vuc_service::TotalDownloadProgress>()[0]);
      if (!write(streamer, instance.downlaod_progress(), prop))
        return false;
      }
      break;
    case ::seres::ota_vuc_service::VucServiceTopic::kReportDownloadFailReason:
      {
      if (!write(streamer, instance.download_fail_reason()))
        return false;
      }
      break;
    case ::seres::ota_vuc_service::VucServiceTopic::kUpgradeModeCtrl:
      {
      const auto &prop = &(get_type_props<::seres::ota_vuc_service::UpgradeModeCtrl>()[0]);
      if (!write(streamer, instance.upgrade_mode_ctrl(), prop))
        return false;
      }
      break;
    case ::seres::ota_vuc_service::VucServiceTopic::kRequestUpgradeCountdown:
      {
      const auto &prop = &(get_type_props<::seres::ota_vuc_service::UpgradeCountdown>()[0]);
      if (!write(streamer, instance.upgrade_countdown(), prop))
        return false;
      }
      break;
    case ::seres::ota_vuc_service::VucServiceTopic::kUpgradeNotify:
      {
      const auto &prop = &(get_type_props<::seres::ota_vuc_service::UpgradeNotify>()[0]);
      if (!write(streamer, instance.upgrade_notify(), prop))
        return false;
      }
      break;
    case ::seres::ota_vuc_service::VucServiceTopic::kReportUpgradeProgress:
      {
      const auto &prop = &(get_type_props<::seres::ota_vuc_service::UpgradeProgress>()[0]);
      if (!write(streamer, instance.upgrade_progress(), prop))
        return false;
      }
      break;
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::seres::ota_vuc_service::VucServiceDataUnion& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_vuc_service::VucServiceDataUnion>();
  str.set_mode(cdr_stream::stream_mode::write, key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_vuc_service::VucServiceDataUnion& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto d = instance._d();
  if (!read(streamer, d))
    return false;
  switch(d)
  {
    case ::seres::ota_vuc_service::VucServiceTopic::kReportInventoryInfo:
    {
      auto obj = decl_ref_type(instance.inventory_info_list())();
      const auto &prop = &(get_type_props<::seres::ota_vuc_service::InventoryInfoList>()[0]);
      if (!read(streamer, obj, prop))
        return false;
      instance.inventory_info_list(obj);
    }
    break;
    case ::seres::ota_vuc_service::VucServiceTopic::kSeamlessUpgradeMode:
    {
      auto obj = decl_ref_type(instance.seamless_upgrade_mode())();
      const auto &prop = &(get_type_props<::seres::ota_vuc_service::SeamlessUpgradeMode>()[0]);
      if (!read(streamer, obj, prop))
        return false;
      instance.seamless_upgrade_mode(obj);
    }
    break;
    case ::seres::ota_vuc_service::VucServiceTopic::kUpgradeTaskNotify:
    {
      auto obj = decl_ref_type(instance.upgrade_task())();
      const auto &prop = &(get_type_props<::seres::ota_vuc_service::UpgradeTask>()[0]);
      if (!read(streamer, obj, prop))
        return false;
      instance.upgrade_task(obj);
    }
    break;
    case ::seres::ota_vuc_service::VucServiceTopic::kReportDownloadProgress:
    {
      auto obj = decl_ref_type(instance.downlaod_progress())();
      const auto &prop = &(get_type_props<::seres::ota_vuc_service::TotalDownloadProgress>()[0]);
      if (!read(streamer, obj, prop))
        return false;
      instance.downlaod_progress(obj);
    }
    break;
    case ::seres::ota_vuc_service::VucServiceTopic::kReportDownloadFailReason:
    {
      auto obj = decl_ref_type(instance.download_fail_reason())();
      if (!read(streamer, obj))
        return false;
      instance.download_fail_reason(obj);
    }
    break;
    case ::seres::ota_vuc_service::VucServiceTopic::kUpgradeModeCtrl:
    {
      auto obj = decl_ref_type(instance.upgrade_mode_ctrl())();
      const auto &prop = &(get_type_props<::seres::ota_vuc_service::UpgradeModeCtrl>()[0]);
      if (!read(streamer, obj, prop))
        return false;
      instance.upgrade_mode_ctrl(obj);
    }
    break;
    case ::seres::ota_vuc_service::VucServiceTopic::kRequestUpgradeCountdown:
    {
      auto obj = decl_ref_type(instance.upgrade_countdown())();
      const auto &prop = &(get_type_props<::seres::ota_vuc_service::UpgradeCountdown>()[0]);
      if (!read(streamer, obj, prop))
        return false;
      instance.upgrade_countdown(obj);
    }
    break;
    case ::seres::ota_vuc_service::VucServiceTopic::kUpgradeNotify:
    {
      auto obj = decl_ref_type(instance.upgrade_notify())();
      const auto &prop = &(get_type_props<::seres::ota_vuc_service::UpgradeNotify>()[0]);
      if (!read(streamer, obj, prop))
        return false;
      instance.upgrade_notify(obj);
    }
    break;
    case ::seres::ota_vuc_service::VucServiceTopic::kReportUpgradeProgress:
    {
      auto obj = decl_ref_type(instance.upgrade_progress())();
      const auto &prop = &(get_type_props<::seres::ota_vuc_service::UpgradeProgress>()[0]);
      if (!read(streamer, obj, prop))
        return false;
      instance.upgrade_progress(obj);
    }
    break;
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::seres::ota_vuc_service::VucServiceDataUnion& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_vuc_service::VucServiceDataUnion>();
  str.set_mode(cdr_stream::stream_mode::read, key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_vuc_service::VucServiceDataUnion& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  const auto d = instance._d();
  if (!move(streamer, d))
    return false;
  switch(d)
  {
    case ::seres::ota_vuc_service::VucServiceTopic::kReportInventoryInfo:
      {
      const auto &prop = &(get_type_props<::seres::ota_vuc_service::InventoryInfoList>()[0]);
      if (!move(streamer, instance.inventory_info_list(), prop))
        return false;
      }
      break;
    case ::seres::ota_vuc_service::VucServiceTopic::kSeamlessUpgradeMode:
      {
      const auto &prop = &(get_type_props<::seres::ota_vuc_service::SeamlessUpgradeMode>()[0]);
      if (!move(streamer, instance.seamless_upgrade_mode(), prop))
        return false;
      }
      break;
    case ::seres::ota_vuc_service::VucServiceTopic::kUpgradeTaskNotify:
      {
      const auto &prop = &(get_type_props<::seres::ota_vuc_service::UpgradeTask>()[0]);
      if (!move(streamer, instance.upgrade_task(), prop))
        return false;
      }
      break;
    case ::seres::ota_vuc_service::VucServiceTopic::kReportDownloadProgress:
      {
      const auto &prop = &(get_type_props<::seres::ota_vuc_service::TotalDownloadProgress>()[0]);
      if (!move(streamer, instance.downlaod_progress(), prop))
        return false;
      }
      break;
    case ::seres::ota_vuc_service::VucServiceTopic::kReportDownloadFailReason:
      {
      if (!move(streamer, instance.download_fail_reason()))
        return false;
      }
      break;
    case ::seres::ota_vuc_service::VucServiceTopic::kUpgradeModeCtrl:
      {
      const auto &prop = &(get_type_props<::seres::ota_vuc_service::UpgradeModeCtrl>()[0]);
      if (!move(streamer, instance.upgrade_mode_ctrl(), prop))
        return false;
      }
      break;
    case ::seres::ota_vuc_service::VucServiceTopic::kRequestUpgradeCountdown:
      {
      const auto &prop = &(get_type_props<::seres::ota_vuc_service::UpgradeCountdown>()[0]);
      if (!move(streamer, instance.upgrade_countdown(), prop))
        return false;
      }
      break;
    case ::seres::ota_vuc_service::VucServiceTopic::kUpgradeNotify:
      {
      const auto &prop = &(get_type_props<::seres::ota_vuc_service::UpgradeNotify>()[0]);
      if (!move(streamer, instance.upgrade_notify(), prop))
        return false;
      }
      break;
    case ::seres::ota_vuc_service::VucServiceTopic::kReportUpgradeProgress:
      {
      const auto &prop = &(get_type_props<::seres::ota_vuc_service::UpgradeProgress>()[0]);
      if (!move(streamer, instance.upgrade_progress(), prop))
        return false;
      }
      break;
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::seres::ota_vuc_service::VucServiceDataUnion& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_vuc_service::VucServiceDataUnion>();
  str.set_mode(cdr_stream::stream_mode::move, key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_vuc_service::VucServiceDataUnion& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  const auto d = instance._d();
  if (!max(streamer, d))
    return false;
  size_t union_max = streamer.position();
  size_t alignment_max = streamer.alignment();
  {
    size_t pos = streamer.position();
    size_t alignment = streamer.alignment();
      const auto &prop = &(get_type_props<::seres::ota_vuc_service::InventoryInfoList>()[0]);
      if (!max(streamer, instance.inventory_info_list(), prop))
        return false;
    if (union_max < streamer.position()) {
      union_max = streamer.position();
      alignment_max = streamer.alignment();
    }
    streamer.position(pos);
    streamer.alignment(alignment);
  }
  {
    size_t pos = streamer.position();
    size_t alignment = streamer.alignment();
      const auto &prop = &(get_type_props<::seres::ota_vuc_service::SeamlessUpgradeMode>()[0]);
      if (!max(streamer, instance.seamless_upgrade_mode(), prop))
        return false;
    if (union_max < streamer.position()) {
      union_max = streamer.position();
      alignment_max = streamer.alignment();
    }
    streamer.position(pos);
    streamer.alignment(alignment);
  }
  {
    size_t pos = streamer.position();
    size_t alignment = streamer.alignment();
      const auto &prop = &(get_type_props<::seres::ota_vuc_service::UpgradeTask>()[0]);
      if (!max(streamer, instance.upgrade_task(), prop))
        return false;
    if (union_max < streamer.position()) {
      union_max = streamer.position();
      alignment_max = streamer.alignment();
    }
    streamer.position(pos);
    streamer.alignment(alignment);
  }
  {
    size_t pos = streamer.position();
    size_t alignment = streamer.alignment();
      const auto &prop = &(get_type_props<::seres::ota_vuc_service::TotalDownloadProgress>()[0]);
      if (!max(streamer, instance.downlaod_progress(), prop))
        return false;
    if (union_max < streamer.position()) {
      union_max = streamer.position();
      alignment_max = streamer.alignment();
    }
    streamer.position(pos);
    streamer.alignment(alignment);
  }
  {
    size_t pos = streamer.position();
    size_t alignment = streamer.alignment();
      if (!max(streamer, instance.download_fail_reason()))
        return false;
    if (union_max < streamer.position()) {
      union_max = streamer.position();
      alignment_max = streamer.alignment();
    }
    streamer.position(pos);
    streamer.alignment(alignment);
  }
  {
    size_t pos = streamer.position();
    size_t alignment = streamer.alignment();
      const auto &prop = &(get_type_props<::seres::ota_vuc_service::UpgradeModeCtrl>()[0]);
      if (!max(streamer, instance.upgrade_mode_ctrl(), prop))
        return false;
    if (union_max < streamer.position()) {
      union_max = streamer.position();
      alignment_max = streamer.alignment();
    }
    streamer.position(pos);
    streamer.alignment(alignment);
  }
  {
    size_t pos = streamer.position();
    size_t alignment = streamer.alignment();
      const auto &prop = &(get_type_props<::seres::ota_vuc_service::UpgradeCountdown>()[0]);
      if (!max(streamer, instance.upgrade_countdown(), prop))
        return false;
    if (union_max < streamer.position()) {
      union_max = streamer.position();
      alignment_max = streamer.alignment();
    }
    streamer.position(pos);
    streamer.alignment(alignment);
  }
  {
    size_t pos = streamer.position();
    size_t alignment = streamer.alignment();
      const auto &prop = &(get_type_props<::seres::ota_vuc_service::UpgradeNotify>()[0]);
      if (!max(streamer, instance.upgrade_notify(), prop))
        return false;
    if (union_max < streamer.position()) {
      union_max = streamer.position();
      alignment_max = streamer.alignment();
    }
    streamer.position(pos);
    streamer.alignment(alignment);
  }
  {
    size_t pos = streamer.position();
    size_t alignment = streamer.alignment();
      const auto &prop = &(get_type_props<::seres::ota_vuc_service::UpgradeProgress>()[0]);
      if (!max(streamer, instance.upgrade_progress(), prop))
        return false;
    if (union_max < streamer.position()) {
      union_max = streamer.position();
      alignment_max = streamer.alignment();
    }
    streamer.position(pos);
    streamer.alignment(alignment);
  }
  streamer.position(union_max);
  streamer.alignment(alignment_max);
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::seres::ota_vuc_service::VucServiceDataUnion& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_vuc_service::VucServiceDataUnion>();
  str.set_mode(cdr_stream::stream_mode::max, key);
  return max(str, instance, props.data()); 
}

} //namespace cdr
} //namespace core
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

#endif // DDSCXX_OTA_VUCSERVICEDATA_HPP_DF4ED10AC7951DF940198A5264CFBE5A
