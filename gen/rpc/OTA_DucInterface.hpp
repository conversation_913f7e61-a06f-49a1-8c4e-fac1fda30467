/****************************************************************

  Generated by Eclipse Cyclone DDS IDL to CXX Translator
  File name: /home/<USER>/dds/scom-idl/OTA/OTA_DucInterface.idl
  Source: OTA_DucInterface.hpp
  Cyclone DDS: v0.11.0

*****************************************************************/
#ifndef DDSCXX_OTA_DUCINTERFACE_HPP_46FD7474B46E62D7C8A5F4776E293F0F
#define DDSCXX_OTA_DUCINTERFACE_HPP_46FD7474B46E62D7C8A5F4776E293F0F

#include "rpcCommon.hpp"

#include "OTA_DucData.hpp"

#include <utility>
#include <ostream>
#include <cstdint>
#include <variant>
#include <dds/core/Exception.hpp>


namespace seres
{
namespace ota_duc_service
{
enum class ReturnCode
{
  OK,
  ERROR,
  REFUSED};

std::ostream& operator<<(std::ostream& os, ReturnCode const& rhs);

const int32_t DucServiceInterface_inventoryCollection_HASH = 202679978;

const int32_t DucServiceInterface_stopInventoryCollection_HASH = 23721455;

const int32_t DucServiceInterface_getInventoryResult_HASH = 207766764;

const int32_t DucServiceInterface_checkDownloadCondition_HASH = 7343798;

const int32_t DucServiceInterface_startDownload_HASH = 38715577;

const int32_t DucServiceInterface_downloadCtrl_HASH = 33876474;

const int32_t DucServiceInterface_getDownloadProgress_HASH = 173346514;

const int32_t DucServiceInterface_uzipPackages_HASH = 113763844;

const int32_t DucServiceInterface_getuzipPackagesResult_HASH = 256795369;

const int32_t DucServiceInterface_startPackagesVerify_HASH = 194734422;

const int32_t DucServiceInterface_getPackagesVerifyResult_HASH = 4313302;

const int32_t DucServiceInterface_checkUpdateCondition_HASH = 178527735;

const int32_t DucServiceInterface_getCheckUpdateConditionResult_HASH = 200329384;

const int32_t DucServiceInterface_startUpdate_HASH = 136901098;

const int32_t DucServiceInterface_resumeUpdate_HASH = 29478526;

const int32_t DucServiceInterface_pauseUpdate_HASH = 251480810;

const int32_t DucServiceInterface_getUpdateProgress_HASH = 66921313;

const int32_t DucServiceInterface_activate_HASH = 146417871;

const int32_t DucServiceInterface_rollback_HASH = 170403593;

const int32_t DucServiceInterface_getRollbackProgress_HASH = 31250426;

const int32_t DucServiceInterface_uploadLog_HASH = 126211184;

class DucServiceInterface_inventoryCollection_In
{
private:
 ::seres::ota_duc_service::SelectedInventoryList inventory_list_;

public:
  DucServiceInterface_inventoryCollection_In() = default;

  explicit DucServiceInterface_inventoryCollection_In(
    const ::seres::ota_duc_service::SelectedInventoryList& inventory_list) :
    inventory_list_(inventory_list) { }

  const ::seres::ota_duc_service::SelectedInventoryList& inventory_list() const { return this->inventory_list_; }
  ::seres::ota_duc_service::SelectedInventoryList& inventory_list() { return this->inventory_list_; }
  void inventory_list(const ::seres::ota_duc_service::SelectedInventoryList& _val_) { this->inventory_list_ = _val_; }
  void inventory_list(::seres::ota_duc_service::SelectedInventoryList&& _val_) { this->inventory_list_ = std::move(_val_); }

  bool operator==(const DucServiceInterface_inventoryCollection_In& _other) const
  {
    (void) _other;
    return inventory_list_ == _other.inventory_list_;
  }

  bool operator!=(const DucServiceInterface_inventoryCollection_In& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_inventoryCollection_In const& rhs);

class DucServiceInterface_stopInventoryCollection_In
{
private:
 int8_t _default_ = 0;

public:
  DucServiceInterface_stopInventoryCollection_In() = default;

  explicit DucServiceInterface_stopInventoryCollection_In(
    int8_t _default) :
    _default_(_default) { }

  int8_t _default() const { return this->_default_; }
  int8_t& _default() { return this->_default_; }
  void _default(int8_t _val_) { this->_default_ = _val_; }

  bool operator==(const DucServiceInterface_stopInventoryCollection_In& _other) const
  {
    (void) _other;
    return _default_ == _other._default_;
  }

  bool operator!=(const DucServiceInterface_stopInventoryCollection_In& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_stopInventoryCollection_In const& rhs);

class DucServiceInterface_getInventoryResult_In
{
private:
 int8_t _default_ = 0;

public:
  DucServiceInterface_getInventoryResult_In() = default;

  explicit DucServiceInterface_getInventoryResult_In(
    int8_t _default) :
    _default_(_default) { }

  int8_t _default() const { return this->_default_; }
  int8_t& _default() { return this->_default_; }
  void _default(int8_t _val_) { this->_default_ = _val_; }

  bool operator==(const DucServiceInterface_getInventoryResult_In& _other) const
  {
    (void) _other;
    return _default_ == _other._default_;
  }

  bool operator!=(const DucServiceInterface_getInventoryResult_In& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_getInventoryResult_In const& rhs);

class DucServiceInterface_checkDownloadCondition_In
{
private:
 ::seres::ota_duc_service::DownloadConditionLists conditions_;

public:
  DucServiceInterface_checkDownloadCondition_In() = default;

  explicit DucServiceInterface_checkDownloadCondition_In(
    const ::seres::ota_duc_service::DownloadConditionLists& conditions) :
    conditions_(conditions) { }

  const ::seres::ota_duc_service::DownloadConditionLists& conditions() const { return this->conditions_; }
  ::seres::ota_duc_service::DownloadConditionLists& conditions() { return this->conditions_; }
  void conditions(const ::seres::ota_duc_service::DownloadConditionLists& _val_) { this->conditions_ = _val_; }
  void conditions(::seres::ota_duc_service::DownloadConditionLists&& _val_) { this->conditions_ = std::move(_val_); }

  bool operator==(const DucServiceInterface_checkDownloadCondition_In& _other) const
  {
    (void) _other;
    return conditions_ == _other.conditions_;
  }

  bool operator!=(const DucServiceInterface_checkDownloadCondition_In& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_checkDownloadCondition_In const& rhs);

class DucServiceInterface_startDownload_In
{
private:
 ::seres::ota_duc_service::DownloadTaskLists task_list_;

public:
  DucServiceInterface_startDownload_In() = default;

  explicit DucServiceInterface_startDownload_In(
    const ::seres::ota_duc_service::DownloadTaskLists& task_list) :
    task_list_(task_list) { }

  const ::seres::ota_duc_service::DownloadTaskLists& task_list() const { return this->task_list_; }
  ::seres::ota_duc_service::DownloadTaskLists& task_list() { return this->task_list_; }
  void task_list(const ::seres::ota_duc_service::DownloadTaskLists& _val_) { this->task_list_ = _val_; }
  void task_list(::seres::ota_duc_service::DownloadTaskLists&& _val_) { this->task_list_ = std::move(_val_); }

  bool operator==(const DucServiceInterface_startDownload_In& _other) const
  {
    (void) _other;
    return task_list_ == _other.task_list_;
  }

  bool operator!=(const DucServiceInterface_startDownload_In& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_startDownload_In const& rhs);

class DucServiceInterface_downloadCtrl_In
{
private:
 ::seres::ota_duc_service::DownloadCtrl download_command_ = ::seres::ota_duc_service::DownloadCtrl::PAUSE;

public:
  DucServiceInterface_downloadCtrl_In() = default;

  explicit DucServiceInterface_downloadCtrl_In(
    ::seres::ota_duc_service::DownloadCtrl download_command) :
    download_command_(download_command) { }

  ::seres::ota_duc_service::DownloadCtrl download_command() const { return this->download_command_; }
  ::seres::ota_duc_service::DownloadCtrl& download_command() { return this->download_command_; }
  void download_command(::seres::ota_duc_service::DownloadCtrl _val_) { this->download_command_ = _val_; }

  bool operator==(const DucServiceInterface_downloadCtrl_In& _other) const
  {
    (void) _other;
    return download_command_ == _other.download_command_;
  }

  bool operator!=(const DucServiceInterface_downloadCtrl_In& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_downloadCtrl_In const& rhs);

class DucServiceInterface_getDownloadProgress_In
{
private:
 int8_t _default_ = 0;

public:
  DucServiceInterface_getDownloadProgress_In() = default;

  explicit DucServiceInterface_getDownloadProgress_In(
    int8_t _default) :
    _default_(_default) { }

  int8_t _default() const { return this->_default_; }
  int8_t& _default() { return this->_default_; }
  void _default(int8_t _val_) { this->_default_ = _val_; }

  bool operator==(const DucServiceInterface_getDownloadProgress_In& _other) const
  {
    (void) _other;
    return _default_ == _other._default_;
  }

  bool operator!=(const DucServiceInterface_getDownloadProgress_In& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_getDownloadProgress_In const& rhs);

class DucServiceInterface_uzipPackages_In
{
private:
 int8_t _default_ = 0;

public:
  DucServiceInterface_uzipPackages_In() = default;

  explicit DucServiceInterface_uzipPackages_In(
    int8_t _default) :
    _default_(_default) { }

  int8_t _default() const { return this->_default_; }
  int8_t& _default() { return this->_default_; }
  void _default(int8_t _val_) { this->_default_ = _val_; }

  bool operator==(const DucServiceInterface_uzipPackages_In& _other) const
  {
    (void) _other;
    return _default_ == _other._default_;
  }

  bool operator!=(const DucServiceInterface_uzipPackages_In& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_uzipPackages_In const& rhs);

class DucServiceInterface_getuzipPackagesResult_In
{
private:
 int8_t _default_ = 0;

public:
  DucServiceInterface_getuzipPackagesResult_In() = default;

  explicit DucServiceInterface_getuzipPackagesResult_In(
    int8_t _default) :
    _default_(_default) { }

  int8_t _default() const { return this->_default_; }
  int8_t& _default() { return this->_default_; }
  void _default(int8_t _val_) { this->_default_ = _val_; }

  bool operator==(const DucServiceInterface_getuzipPackagesResult_In& _other) const
  {
    (void) _other;
    return _default_ == _other._default_;
  }

  bool operator!=(const DucServiceInterface_getuzipPackagesResult_In& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_getuzipPackagesResult_In const& rhs);

class DucServiceInterface_startPackagesVerify_In
{
private:
 int8_t _default_ = 0;

public:
  DucServiceInterface_startPackagesVerify_In() = default;

  explicit DucServiceInterface_startPackagesVerify_In(
    int8_t _default) :
    _default_(_default) { }

  int8_t _default() const { return this->_default_; }
  int8_t& _default() { return this->_default_; }
  void _default(int8_t _val_) { this->_default_ = _val_; }

  bool operator==(const DucServiceInterface_startPackagesVerify_In& _other) const
  {
    (void) _other;
    return _default_ == _other._default_;
  }

  bool operator!=(const DucServiceInterface_startPackagesVerify_In& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_startPackagesVerify_In const& rhs);

class DucServiceInterface_getPackagesVerifyResult_In
{
private:
 int8_t _default_ = 0;

public:
  DucServiceInterface_getPackagesVerifyResult_In() = default;

  explicit DucServiceInterface_getPackagesVerifyResult_In(
    int8_t _default) :
    _default_(_default) { }

  int8_t _default() const { return this->_default_; }
  int8_t& _default() { return this->_default_; }
  void _default(int8_t _val_) { this->_default_ = _val_; }

  bool operator==(const DucServiceInterface_getPackagesVerifyResult_In& _other) const
  {
    (void) _other;
    return _default_ == _other._default_;
  }

  bool operator!=(const DucServiceInterface_getPackagesVerifyResult_In& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_getPackagesVerifyResult_In const& rhs);

class DucServiceInterface_checkUpdateCondition_In
{
private:
 int8_t _default_ = 0;

public:
  DucServiceInterface_checkUpdateCondition_In() = default;

  explicit DucServiceInterface_checkUpdateCondition_In(
    int8_t _default) :
    _default_(_default) { }

  int8_t _default() const { return this->_default_; }
  int8_t& _default() { return this->_default_; }
  void _default(int8_t _val_) { this->_default_ = _val_; }

  bool operator==(const DucServiceInterface_checkUpdateCondition_In& _other) const
  {
    (void) _other;
    return _default_ == _other._default_;
  }

  bool operator!=(const DucServiceInterface_checkUpdateCondition_In& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_checkUpdateCondition_In const& rhs);

class DucServiceInterface_getCheckUpdateConditionResult_In
{
private:
 int8_t _default_ = 0;

public:
  DucServiceInterface_getCheckUpdateConditionResult_In() = default;

  explicit DucServiceInterface_getCheckUpdateConditionResult_In(
    int8_t _default) :
    _default_(_default) { }

  int8_t _default() const { return this->_default_; }
  int8_t& _default() { return this->_default_; }
  void _default(int8_t _val_) { this->_default_ = _val_; }

  bool operator==(const DucServiceInterface_getCheckUpdateConditionResult_In& _other) const
  {
    (void) _other;
    return _default_ == _other._default_;
  }

  bool operator!=(const DucServiceInterface_getCheckUpdateConditionResult_In& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_getCheckUpdateConditionResult_In const& rhs);

class DucServiceInterface_startUpdate_In
{
private:
 ::seres::ota_duc_service::UpdateMode mode_ = ::seres::ota_duc_service::UpdateMode::SeamlessMode;
 ::seres::ota_duc_service::UpdateDeviceList update_list_;

public:
  DucServiceInterface_startUpdate_In() = default;

  explicit DucServiceInterface_startUpdate_In(
    ::seres::ota_duc_service::UpdateMode mode,
    const ::seres::ota_duc_service::UpdateDeviceList& update_list) :
    mode_(mode),
    update_list_(update_list) { }

  ::seres::ota_duc_service::UpdateMode mode() const { return this->mode_; }
  ::seres::ota_duc_service::UpdateMode& mode() { return this->mode_; }
  void mode(::seres::ota_duc_service::UpdateMode _val_) { this->mode_ = _val_; }
  const ::seres::ota_duc_service::UpdateDeviceList& update_list() const { return this->update_list_; }
  ::seres::ota_duc_service::UpdateDeviceList& update_list() { return this->update_list_; }
  void update_list(const ::seres::ota_duc_service::UpdateDeviceList& _val_) { this->update_list_ = _val_; }
  void update_list(::seres::ota_duc_service::UpdateDeviceList&& _val_) { this->update_list_ = std::move(_val_); }

  bool operator==(const DucServiceInterface_startUpdate_In& _other) const
  {
    (void) _other;
    return mode_ == _other.mode_ &&
      update_list_ == _other.update_list_;
  }

  bool operator!=(const DucServiceInterface_startUpdate_In& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_startUpdate_In const& rhs);

class DucServiceInterface_resumeUpdate_In
{
private:
 int8_t _default_ = 0;

public:
  DucServiceInterface_resumeUpdate_In() = default;

  explicit DucServiceInterface_resumeUpdate_In(
    int8_t _default) :
    _default_(_default) { }

  int8_t _default() const { return this->_default_; }
  int8_t& _default() { return this->_default_; }
  void _default(int8_t _val_) { this->_default_ = _val_; }

  bool operator==(const DucServiceInterface_resumeUpdate_In& _other) const
  {
    (void) _other;
    return _default_ == _other._default_;
  }

  bool operator!=(const DucServiceInterface_resumeUpdate_In& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_resumeUpdate_In const& rhs);

class DucServiceInterface_pauseUpdate_In
{
private:
 int8_t _default_ = 0;

public:
  DucServiceInterface_pauseUpdate_In() = default;

  explicit DucServiceInterface_pauseUpdate_In(
    int8_t _default) :
    _default_(_default) { }

  int8_t _default() const { return this->_default_; }
  int8_t& _default() { return this->_default_; }
  void _default(int8_t _val_) { this->_default_ = _val_; }

  bool operator==(const DucServiceInterface_pauseUpdate_In& _other) const
  {
    (void) _other;
    return _default_ == _other._default_;
  }

  bool operator!=(const DucServiceInterface_pauseUpdate_In& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_pauseUpdate_In const& rhs);

class DucServiceInterface_getUpdateProgress_In
{
private:
 int8_t _default_ = 0;

public:
  DucServiceInterface_getUpdateProgress_In() = default;

  explicit DucServiceInterface_getUpdateProgress_In(
    int8_t _default) :
    _default_(_default) { }

  int8_t _default() const { return this->_default_; }
  int8_t& _default() { return this->_default_; }
  void _default(int8_t _val_) { this->_default_ = _val_; }

  bool operator==(const DucServiceInterface_getUpdateProgress_In& _other) const
  {
    (void) _other;
    return _default_ == _other._default_;
  }

  bool operator!=(const DucServiceInterface_getUpdateProgress_In& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_getUpdateProgress_In const& rhs);

class DucServiceInterface_activate_In
{
private:
 int8_t _default_ = 0;

public:
  DucServiceInterface_activate_In() = default;

  explicit DucServiceInterface_activate_In(
    int8_t _default) :
    _default_(_default) { }

  int8_t _default() const { return this->_default_; }
  int8_t& _default() { return this->_default_; }
  void _default(int8_t _val_) { this->_default_ = _val_; }

  bool operator==(const DucServiceInterface_activate_In& _other) const
  {
    (void) _other;
    return _default_ == _other._default_;
  }

  bool operator!=(const DucServiceInterface_activate_In& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_activate_In const& rhs);

class DucServiceInterface_rollback_In
{
private:
 ::seres::ota_duc_service::RollbackComponentList component_list_;

public:
  DucServiceInterface_rollback_In() = default;

  explicit DucServiceInterface_rollback_In(
    const ::seres::ota_duc_service::RollbackComponentList& component_list) :
    component_list_(component_list) { }

  const ::seres::ota_duc_service::RollbackComponentList& component_list() const { return this->component_list_; }
  ::seres::ota_duc_service::RollbackComponentList& component_list() { return this->component_list_; }
  void component_list(const ::seres::ota_duc_service::RollbackComponentList& _val_) { this->component_list_ = _val_; }
  void component_list(::seres::ota_duc_service::RollbackComponentList&& _val_) { this->component_list_ = std::move(_val_); }

  bool operator==(const DucServiceInterface_rollback_In& _other) const
  {
    (void) _other;
    return component_list_ == _other.component_list_;
  }

  bool operator!=(const DucServiceInterface_rollback_In& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_rollback_In const& rhs);

class DucServiceInterface_getRollbackProgress_In
{
private:
 int8_t _default_ = 0;

public:
  DucServiceInterface_getRollbackProgress_In() = default;

  explicit DucServiceInterface_getRollbackProgress_In(
    int8_t _default) :
    _default_(_default) { }

  int8_t _default() const { return this->_default_; }
  int8_t& _default() { return this->_default_; }
  void _default(int8_t _val_) { this->_default_ = _val_; }

  bool operator==(const DucServiceInterface_getRollbackProgress_In& _other) const
  {
    (void) _other;
    return _default_ == _other._default_;
  }

  bool operator!=(const DucServiceInterface_getRollbackProgress_In& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_getRollbackProgress_In const& rhs);

class DucServiceInterface_uploadLog_In
{
private:
 int8_t _default_ = 0;

public:
  DucServiceInterface_uploadLog_In() = default;

  explicit DucServiceInterface_uploadLog_In(
    int8_t _default) :
    _default_(_default) { }

  int8_t _default() const { return this->_default_; }
  int8_t& _default() { return this->_default_; }
  void _default(int8_t _val_) { this->_default_ = _val_; }

  bool operator==(const DucServiceInterface_uploadLog_In& _other) const
  {
    (void) _other;
    return _default_ == _other._default_;
  }

  bool operator!=(const DucServiceInterface_uploadLog_In& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_uploadLog_In const& rhs);

class DucServiceInterface_inventoryCollection_Out
{
private:
 int8_t _default_ = 0;

public:
  DucServiceInterface_inventoryCollection_Out() = default;

  explicit DucServiceInterface_inventoryCollection_Out(
    int8_t _default) :
    _default_(_default) { }

  int8_t _default() const { return this->_default_; }
  int8_t& _default() { return this->_default_; }
  void _default(int8_t _val_) { this->_default_ = _val_; }

  bool operator==(const DucServiceInterface_inventoryCollection_Out& _other) const
  {
    (void) _other;
    return _default_ == _other._default_;
  }

  bool operator!=(const DucServiceInterface_inventoryCollection_Out& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_inventoryCollection_Out const& rhs);

class DucServiceInterface_inventoryCollection_Result
{
private:
 ::seres::ota_duc_service::DucServiceInterface_inventoryCollection_Out inventoryCollectionOut_;
 ::seres::ota_duc_service::ReturnCode _return_ = ::seres::ota_duc_service::ReturnCode::OK;

public:
  DucServiceInterface_inventoryCollection_Result() = default;

  explicit DucServiceInterface_inventoryCollection_Result(
    const ::seres::ota_duc_service::DucServiceInterface_inventoryCollection_Out& inventoryCollectionOut,
    ::seres::ota_duc_service::ReturnCode _return) :
    inventoryCollectionOut_(inventoryCollectionOut),
    _return_(_return) { }

  const ::seres::ota_duc_service::DucServiceInterface_inventoryCollection_Out& inventoryCollectionOut() const { return this->inventoryCollectionOut_; }
  ::seres::ota_duc_service::DucServiceInterface_inventoryCollection_Out& inventoryCollectionOut() { return this->inventoryCollectionOut_; }
  void inventoryCollectionOut(const ::seres::ota_duc_service::DucServiceInterface_inventoryCollection_Out& _val_) { this->inventoryCollectionOut_ = _val_; }
  void inventoryCollectionOut(::seres::ota_duc_service::DucServiceInterface_inventoryCollection_Out&& _val_) { this->inventoryCollectionOut_ = std::move(_val_); }
  ::seres::ota_duc_service::ReturnCode _return() const { return this->_return_; }
  ::seres::ota_duc_service::ReturnCode& _return() { return this->_return_; }
  void _return(::seres::ota_duc_service::ReturnCode _val_) { this->_return_ = _val_; }

  bool operator==(const DucServiceInterface_inventoryCollection_Result& _other) const
  {
    (void) _other;
    return inventoryCollectionOut_ == _other.inventoryCollectionOut_ &&
      _return_ == _other._return_;
  }

  bool operator!=(const DucServiceInterface_inventoryCollection_Result& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_inventoryCollection_Result const& rhs);

class DucServiceInterface_stopInventoryCollection_Out
{
private:
 int8_t _default_ = 0;

public:
  DucServiceInterface_stopInventoryCollection_Out() = default;

  explicit DucServiceInterface_stopInventoryCollection_Out(
    int8_t _default) :
    _default_(_default) { }

  int8_t _default() const { return this->_default_; }
  int8_t& _default() { return this->_default_; }
  void _default(int8_t _val_) { this->_default_ = _val_; }

  bool operator==(const DucServiceInterface_stopInventoryCollection_Out& _other) const
  {
    (void) _other;
    return _default_ == _other._default_;
  }

  bool operator!=(const DucServiceInterface_stopInventoryCollection_Out& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_stopInventoryCollection_Out const& rhs);

class DucServiceInterface_stopInventoryCollection_Result
{
private:
 ::seres::ota_duc_service::DucServiceInterface_stopInventoryCollection_Out stopInventoryCollectionOut_;
 ::seres::ota_duc_service::ReturnCode _return_ = ::seres::ota_duc_service::ReturnCode::OK;

public:
  DucServiceInterface_stopInventoryCollection_Result() = default;

  explicit DucServiceInterface_stopInventoryCollection_Result(
    const ::seres::ota_duc_service::DucServiceInterface_stopInventoryCollection_Out& stopInventoryCollectionOut,
    ::seres::ota_duc_service::ReturnCode _return) :
    stopInventoryCollectionOut_(stopInventoryCollectionOut),
    _return_(_return) { }

  const ::seres::ota_duc_service::DucServiceInterface_stopInventoryCollection_Out& stopInventoryCollectionOut() const { return this->stopInventoryCollectionOut_; }
  ::seres::ota_duc_service::DucServiceInterface_stopInventoryCollection_Out& stopInventoryCollectionOut() { return this->stopInventoryCollectionOut_; }
  void stopInventoryCollectionOut(const ::seres::ota_duc_service::DucServiceInterface_stopInventoryCollection_Out& _val_) { this->stopInventoryCollectionOut_ = _val_; }
  void stopInventoryCollectionOut(::seres::ota_duc_service::DucServiceInterface_stopInventoryCollection_Out&& _val_) { this->stopInventoryCollectionOut_ = std::move(_val_); }
  ::seres::ota_duc_service::ReturnCode _return() const { return this->_return_; }
  ::seres::ota_duc_service::ReturnCode& _return() { return this->_return_; }
  void _return(::seres::ota_duc_service::ReturnCode _val_) { this->_return_ = _val_; }

  bool operator==(const DucServiceInterface_stopInventoryCollection_Result& _other) const
  {
    (void) _other;
    return stopInventoryCollectionOut_ == _other.stopInventoryCollectionOut_ &&
      _return_ == _other._return_;
  }

  bool operator!=(const DucServiceInterface_stopInventoryCollection_Result& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_stopInventoryCollection_Result const& rhs);

class DucServiceInterface_getInventoryResult_Out
{
private:
 ::seres::ota_duc_service::InventoryResult inventory_list_;

public:
  DucServiceInterface_getInventoryResult_Out() = default;

  explicit DucServiceInterface_getInventoryResult_Out(
    const ::seres::ota_duc_service::InventoryResult& inventory_list) :
    inventory_list_(inventory_list) { }

  const ::seres::ota_duc_service::InventoryResult& inventory_list() const { return this->inventory_list_; }
  ::seres::ota_duc_service::InventoryResult& inventory_list() { return this->inventory_list_; }
  void inventory_list(const ::seres::ota_duc_service::InventoryResult& _val_) { this->inventory_list_ = _val_; }
  void inventory_list(::seres::ota_duc_service::InventoryResult&& _val_) { this->inventory_list_ = std::move(_val_); }

  bool operator==(const DucServiceInterface_getInventoryResult_Out& _other) const
  {
    (void) _other;
    return inventory_list_ == _other.inventory_list_;
  }

  bool operator!=(const DucServiceInterface_getInventoryResult_Out& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_getInventoryResult_Out const& rhs);

class DucServiceInterface_getInventoryResult_Result
{
private:
 ::seres::ota_duc_service::DucServiceInterface_getInventoryResult_Out getInventoryResultOut_;
 ::seres::ota_duc_service::ReturnCode _return_ = ::seres::ota_duc_service::ReturnCode::OK;

public:
  DucServiceInterface_getInventoryResult_Result() = default;

  explicit DucServiceInterface_getInventoryResult_Result(
    const ::seres::ota_duc_service::DucServiceInterface_getInventoryResult_Out& getInventoryResultOut,
    ::seres::ota_duc_service::ReturnCode _return) :
    getInventoryResultOut_(getInventoryResultOut),
    _return_(_return) { }

  const ::seres::ota_duc_service::DucServiceInterface_getInventoryResult_Out& getInventoryResultOut() const { return this->getInventoryResultOut_; }
  ::seres::ota_duc_service::DucServiceInterface_getInventoryResult_Out& getInventoryResultOut() { return this->getInventoryResultOut_; }
  void getInventoryResultOut(const ::seres::ota_duc_service::DucServiceInterface_getInventoryResult_Out& _val_) { this->getInventoryResultOut_ = _val_; }
  void getInventoryResultOut(::seres::ota_duc_service::DucServiceInterface_getInventoryResult_Out&& _val_) { this->getInventoryResultOut_ = std::move(_val_); }
  ::seres::ota_duc_service::ReturnCode _return() const { return this->_return_; }
  ::seres::ota_duc_service::ReturnCode& _return() { return this->_return_; }
  void _return(::seres::ota_duc_service::ReturnCode _val_) { this->_return_ = _val_; }

  bool operator==(const DucServiceInterface_getInventoryResult_Result& _other) const
  {
    (void) _other;
    return getInventoryResultOut_ == _other.getInventoryResultOut_ &&
      _return_ == _other._return_;
  }

  bool operator!=(const DucServiceInterface_getInventoryResult_Result& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_getInventoryResult_Result const& rhs);

class DucServiceInterface_checkDownloadCondition_Out
{
private:
 ::seres::ota_duc_service::DownloadConditionResult condition_result_ = ::seres::ota_duc_service::DownloadConditionResult::NOEXCEPTION;

public:
  DucServiceInterface_checkDownloadCondition_Out() = default;

  explicit DucServiceInterface_checkDownloadCondition_Out(
    ::seres::ota_duc_service::DownloadConditionResult condition_result) :
    condition_result_(condition_result) { }

  ::seres::ota_duc_service::DownloadConditionResult condition_result() const { return this->condition_result_; }
  ::seres::ota_duc_service::DownloadConditionResult& condition_result() { return this->condition_result_; }
  void condition_result(::seres::ota_duc_service::DownloadConditionResult _val_) { this->condition_result_ = _val_; }

  bool operator==(const DucServiceInterface_checkDownloadCondition_Out& _other) const
  {
    (void) _other;
    return condition_result_ == _other.condition_result_;
  }

  bool operator!=(const DucServiceInterface_checkDownloadCondition_Out& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_checkDownloadCondition_Out const& rhs);

class DucServiceInterface_checkDownloadCondition_Result
{
private:
 ::seres::ota_duc_service::DucServiceInterface_checkDownloadCondition_Out checkDownloadConditionOut_;
 ::seres::ota_duc_service::ReturnCode _return_ = ::seres::ota_duc_service::ReturnCode::OK;

public:
  DucServiceInterface_checkDownloadCondition_Result() = default;

  explicit DucServiceInterface_checkDownloadCondition_Result(
    const ::seres::ota_duc_service::DucServiceInterface_checkDownloadCondition_Out& checkDownloadConditionOut,
    ::seres::ota_duc_service::ReturnCode _return) :
    checkDownloadConditionOut_(checkDownloadConditionOut),
    _return_(_return) { }

  const ::seres::ota_duc_service::DucServiceInterface_checkDownloadCondition_Out& checkDownloadConditionOut() const { return this->checkDownloadConditionOut_; }
  ::seres::ota_duc_service::DucServiceInterface_checkDownloadCondition_Out& checkDownloadConditionOut() { return this->checkDownloadConditionOut_; }
  void checkDownloadConditionOut(const ::seres::ota_duc_service::DucServiceInterface_checkDownloadCondition_Out& _val_) { this->checkDownloadConditionOut_ = _val_; }
  void checkDownloadConditionOut(::seres::ota_duc_service::DucServiceInterface_checkDownloadCondition_Out&& _val_) { this->checkDownloadConditionOut_ = std::move(_val_); }
  ::seres::ota_duc_service::ReturnCode _return() const { return this->_return_; }
  ::seres::ota_duc_service::ReturnCode& _return() { return this->_return_; }
  void _return(::seres::ota_duc_service::ReturnCode _val_) { this->_return_ = _val_; }

  bool operator==(const DucServiceInterface_checkDownloadCondition_Result& _other) const
  {
    (void) _other;
    return checkDownloadConditionOut_ == _other.checkDownloadConditionOut_ &&
      _return_ == _other._return_;
  }

  bool operator!=(const DucServiceInterface_checkDownloadCondition_Result& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_checkDownloadCondition_Result const& rhs);

class DucServiceInterface_startDownload_Out
{
private:
 int8_t _default_ = 0;

public:
  DucServiceInterface_startDownload_Out() = default;

  explicit DucServiceInterface_startDownload_Out(
    int8_t _default) :
    _default_(_default) { }

  int8_t _default() const { return this->_default_; }
  int8_t& _default() { return this->_default_; }
  void _default(int8_t _val_) { this->_default_ = _val_; }

  bool operator==(const DucServiceInterface_startDownload_Out& _other) const
  {
    (void) _other;
    return _default_ == _other._default_;
  }

  bool operator!=(const DucServiceInterface_startDownload_Out& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_startDownload_Out const& rhs);

class DucServiceInterface_startDownload_Result
{
private:
 ::seres::ota_duc_service::DucServiceInterface_startDownload_Out startDownloadOut_;
 ::seres::ota_duc_service::ReturnCode _return_ = ::seres::ota_duc_service::ReturnCode::OK;

public:
  DucServiceInterface_startDownload_Result() = default;

  explicit DucServiceInterface_startDownload_Result(
    const ::seres::ota_duc_service::DucServiceInterface_startDownload_Out& startDownloadOut,
    ::seres::ota_duc_service::ReturnCode _return) :
    startDownloadOut_(startDownloadOut),
    _return_(_return) { }

  const ::seres::ota_duc_service::DucServiceInterface_startDownload_Out& startDownloadOut() const { return this->startDownloadOut_; }
  ::seres::ota_duc_service::DucServiceInterface_startDownload_Out& startDownloadOut() { return this->startDownloadOut_; }
  void startDownloadOut(const ::seres::ota_duc_service::DucServiceInterface_startDownload_Out& _val_) { this->startDownloadOut_ = _val_; }
  void startDownloadOut(::seres::ota_duc_service::DucServiceInterface_startDownload_Out&& _val_) { this->startDownloadOut_ = std::move(_val_); }
  ::seres::ota_duc_service::ReturnCode _return() const { return this->_return_; }
  ::seres::ota_duc_service::ReturnCode& _return() { return this->_return_; }
  void _return(::seres::ota_duc_service::ReturnCode _val_) { this->_return_ = _val_; }

  bool operator==(const DucServiceInterface_startDownload_Result& _other) const
  {
    (void) _other;
    return startDownloadOut_ == _other.startDownloadOut_ &&
      _return_ == _other._return_;
  }

  bool operator!=(const DucServiceInterface_startDownload_Result& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_startDownload_Result const& rhs);

class DucServiceInterface_downloadCtrl_Out
{
private:
 int8_t _default_ = 0;

public:
  DucServiceInterface_downloadCtrl_Out() = default;

  explicit DucServiceInterface_downloadCtrl_Out(
    int8_t _default) :
    _default_(_default) { }

  int8_t _default() const { return this->_default_; }
  int8_t& _default() { return this->_default_; }
  void _default(int8_t _val_) { this->_default_ = _val_; }

  bool operator==(const DucServiceInterface_downloadCtrl_Out& _other) const
  {
    (void) _other;
    return _default_ == _other._default_;
  }

  bool operator!=(const DucServiceInterface_downloadCtrl_Out& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_downloadCtrl_Out const& rhs);

class DucServiceInterface_downloadCtrl_Result
{
private:
 ::seres::ota_duc_service::DucServiceInterface_downloadCtrl_Out downloadCtrlOut_;
 ::seres::ota_duc_service::ReturnCode _return_ = ::seres::ota_duc_service::ReturnCode::OK;

public:
  DucServiceInterface_downloadCtrl_Result() = default;

  explicit DucServiceInterface_downloadCtrl_Result(
    const ::seres::ota_duc_service::DucServiceInterface_downloadCtrl_Out& downloadCtrlOut,
    ::seres::ota_duc_service::ReturnCode _return) :
    downloadCtrlOut_(downloadCtrlOut),
    _return_(_return) { }

  const ::seres::ota_duc_service::DucServiceInterface_downloadCtrl_Out& downloadCtrlOut() const { return this->downloadCtrlOut_; }
  ::seres::ota_duc_service::DucServiceInterface_downloadCtrl_Out& downloadCtrlOut() { return this->downloadCtrlOut_; }
  void downloadCtrlOut(const ::seres::ota_duc_service::DucServiceInterface_downloadCtrl_Out& _val_) { this->downloadCtrlOut_ = _val_; }
  void downloadCtrlOut(::seres::ota_duc_service::DucServiceInterface_downloadCtrl_Out&& _val_) { this->downloadCtrlOut_ = std::move(_val_); }
  ::seres::ota_duc_service::ReturnCode _return() const { return this->_return_; }
  ::seres::ota_duc_service::ReturnCode& _return() { return this->_return_; }
  void _return(::seres::ota_duc_service::ReturnCode _val_) { this->_return_ = _val_; }

  bool operator==(const DucServiceInterface_downloadCtrl_Result& _other) const
  {
    (void) _other;
    return downloadCtrlOut_ == _other.downloadCtrlOut_ &&
      _return_ == _other._return_;
  }

  bool operator!=(const DucServiceInterface_downloadCtrl_Result& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_downloadCtrl_Result const& rhs);

class DucServiceInterface_getDownloadProgress_Out
{
private:
 ::seres::ota_duc_service::DownloadProgress download_progress_;

public:
  DucServiceInterface_getDownloadProgress_Out() = default;

  explicit DucServiceInterface_getDownloadProgress_Out(
    const ::seres::ota_duc_service::DownloadProgress& download_progress) :
    download_progress_(download_progress) { }

  const ::seres::ota_duc_service::DownloadProgress& download_progress() const { return this->download_progress_; }
  ::seres::ota_duc_service::DownloadProgress& download_progress() { return this->download_progress_; }
  void download_progress(const ::seres::ota_duc_service::DownloadProgress& _val_) { this->download_progress_ = _val_; }
  void download_progress(::seres::ota_duc_service::DownloadProgress&& _val_) { this->download_progress_ = std::move(_val_); }

  bool operator==(const DucServiceInterface_getDownloadProgress_Out& _other) const
  {
    (void) _other;
    return download_progress_ == _other.download_progress_;
  }

  bool operator!=(const DucServiceInterface_getDownloadProgress_Out& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_getDownloadProgress_Out const& rhs);

class DucServiceInterface_getDownloadProgress_Result
{
private:
 ::seres::ota_duc_service::DucServiceInterface_getDownloadProgress_Out getDownloadProgressOut_;
 ::seres::ota_duc_service::ReturnCode _return_ = ::seres::ota_duc_service::ReturnCode::OK;

public:
  DucServiceInterface_getDownloadProgress_Result() = default;

  explicit DucServiceInterface_getDownloadProgress_Result(
    const ::seres::ota_duc_service::DucServiceInterface_getDownloadProgress_Out& getDownloadProgressOut,
    ::seres::ota_duc_service::ReturnCode _return) :
    getDownloadProgressOut_(getDownloadProgressOut),
    _return_(_return) { }

  const ::seres::ota_duc_service::DucServiceInterface_getDownloadProgress_Out& getDownloadProgressOut() const { return this->getDownloadProgressOut_; }
  ::seres::ota_duc_service::DucServiceInterface_getDownloadProgress_Out& getDownloadProgressOut() { return this->getDownloadProgressOut_; }
  void getDownloadProgressOut(const ::seres::ota_duc_service::DucServiceInterface_getDownloadProgress_Out& _val_) { this->getDownloadProgressOut_ = _val_; }
  void getDownloadProgressOut(::seres::ota_duc_service::DucServiceInterface_getDownloadProgress_Out&& _val_) { this->getDownloadProgressOut_ = std::move(_val_); }
  ::seres::ota_duc_service::ReturnCode _return() const { return this->_return_; }
  ::seres::ota_duc_service::ReturnCode& _return() { return this->_return_; }
  void _return(::seres::ota_duc_service::ReturnCode _val_) { this->_return_ = _val_; }

  bool operator==(const DucServiceInterface_getDownloadProgress_Result& _other) const
  {
    (void) _other;
    return getDownloadProgressOut_ == _other.getDownloadProgressOut_ &&
      _return_ == _other._return_;
  }

  bool operator!=(const DucServiceInterface_getDownloadProgress_Result& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_getDownloadProgress_Result const& rhs);

class DucServiceInterface_uzipPackages_Out
{
private:
 int8_t _default_ = 0;

public:
  DucServiceInterface_uzipPackages_Out() = default;

  explicit DucServiceInterface_uzipPackages_Out(
    int8_t _default) :
    _default_(_default) { }

  int8_t _default() const { return this->_default_; }
  int8_t& _default() { return this->_default_; }
  void _default(int8_t _val_) { this->_default_ = _val_; }

  bool operator==(const DucServiceInterface_uzipPackages_Out& _other) const
  {
    (void) _other;
    return _default_ == _other._default_;
  }

  bool operator!=(const DucServiceInterface_uzipPackages_Out& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_uzipPackages_Out const& rhs);

class DucServiceInterface_uzipPackages_Result
{
private:
 ::seres::ota_duc_service::DucServiceInterface_uzipPackages_Out uzipPackagesOut_;
 ::seres::ota_duc_service::ReturnCode _return_ = ::seres::ota_duc_service::ReturnCode::OK;

public:
  DucServiceInterface_uzipPackages_Result() = default;

  explicit DucServiceInterface_uzipPackages_Result(
    const ::seres::ota_duc_service::DucServiceInterface_uzipPackages_Out& uzipPackagesOut,
    ::seres::ota_duc_service::ReturnCode _return) :
    uzipPackagesOut_(uzipPackagesOut),
    _return_(_return) { }

  const ::seres::ota_duc_service::DucServiceInterface_uzipPackages_Out& uzipPackagesOut() const { return this->uzipPackagesOut_; }
  ::seres::ota_duc_service::DucServiceInterface_uzipPackages_Out& uzipPackagesOut() { return this->uzipPackagesOut_; }
  void uzipPackagesOut(const ::seres::ota_duc_service::DucServiceInterface_uzipPackages_Out& _val_) { this->uzipPackagesOut_ = _val_; }
  void uzipPackagesOut(::seres::ota_duc_service::DucServiceInterface_uzipPackages_Out&& _val_) { this->uzipPackagesOut_ = std::move(_val_); }
  ::seres::ota_duc_service::ReturnCode _return() const { return this->_return_; }
  ::seres::ota_duc_service::ReturnCode& _return() { return this->_return_; }
  void _return(::seres::ota_duc_service::ReturnCode _val_) { this->_return_ = _val_; }

  bool operator==(const DucServiceInterface_uzipPackages_Result& _other) const
  {
    (void) _other;
    return uzipPackagesOut_ == _other.uzipPackagesOut_ &&
      _return_ == _other._return_;
  }

  bool operator!=(const DucServiceInterface_uzipPackages_Result& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_uzipPackages_Result const& rhs);

class DucServiceInterface_getuzipPackagesResult_Out
{
private:
 ::seres::ota_duc_service::UzipPackagesResult uzip_Result_;

public:
  DucServiceInterface_getuzipPackagesResult_Out() = default;

  explicit DucServiceInterface_getuzipPackagesResult_Out(
    const ::seres::ota_duc_service::UzipPackagesResult& uzip_Result) :
    uzip_Result_(uzip_Result) { }

  const ::seres::ota_duc_service::UzipPackagesResult& uzip_Result() const { return this->uzip_Result_; }
  ::seres::ota_duc_service::UzipPackagesResult& uzip_Result() { return this->uzip_Result_; }
  void uzip_Result(const ::seres::ota_duc_service::UzipPackagesResult& _val_) { this->uzip_Result_ = _val_; }
  void uzip_Result(::seres::ota_duc_service::UzipPackagesResult&& _val_) { this->uzip_Result_ = std::move(_val_); }

  bool operator==(const DucServiceInterface_getuzipPackagesResult_Out& _other) const
  {
    (void) _other;
    return uzip_Result_ == _other.uzip_Result_;
  }

  bool operator!=(const DucServiceInterface_getuzipPackagesResult_Out& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_getuzipPackagesResult_Out const& rhs);

class DucServiceInterface_getuzipPackagesResult_Result
{
private:
 ::seres::ota_duc_service::DucServiceInterface_getuzipPackagesResult_Out getuzipPackagesResultOut_;
 ::seres::ota_duc_service::ReturnCode _return_ = ::seres::ota_duc_service::ReturnCode::OK;

public:
  DucServiceInterface_getuzipPackagesResult_Result() = default;

  explicit DucServiceInterface_getuzipPackagesResult_Result(
    const ::seres::ota_duc_service::DucServiceInterface_getuzipPackagesResult_Out& getuzipPackagesResultOut,
    ::seres::ota_duc_service::ReturnCode _return) :
    getuzipPackagesResultOut_(getuzipPackagesResultOut),
    _return_(_return) { }

  const ::seres::ota_duc_service::DucServiceInterface_getuzipPackagesResult_Out& getuzipPackagesResultOut() const { return this->getuzipPackagesResultOut_; }
  ::seres::ota_duc_service::DucServiceInterface_getuzipPackagesResult_Out& getuzipPackagesResultOut() { return this->getuzipPackagesResultOut_; }
  void getuzipPackagesResultOut(const ::seres::ota_duc_service::DucServiceInterface_getuzipPackagesResult_Out& _val_) { this->getuzipPackagesResultOut_ = _val_; }
  void getuzipPackagesResultOut(::seres::ota_duc_service::DucServiceInterface_getuzipPackagesResult_Out&& _val_) { this->getuzipPackagesResultOut_ = std::move(_val_); }
  ::seres::ota_duc_service::ReturnCode _return() const { return this->_return_; }
  ::seres::ota_duc_service::ReturnCode& _return() { return this->_return_; }
  void _return(::seres::ota_duc_service::ReturnCode _val_) { this->_return_ = _val_; }

  bool operator==(const DucServiceInterface_getuzipPackagesResult_Result& _other) const
  {
    (void) _other;
    return getuzipPackagesResultOut_ == _other.getuzipPackagesResultOut_ &&
      _return_ == _other._return_;
  }

  bool operator!=(const DucServiceInterface_getuzipPackagesResult_Result& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_getuzipPackagesResult_Result const& rhs);

class DucServiceInterface_startPackagesVerify_Out
{
private:
 int8_t _default_ = 0;

public:
  DucServiceInterface_startPackagesVerify_Out() = default;

  explicit DucServiceInterface_startPackagesVerify_Out(
    int8_t _default) :
    _default_(_default) { }

  int8_t _default() const { return this->_default_; }
  int8_t& _default() { return this->_default_; }
  void _default(int8_t _val_) { this->_default_ = _val_; }

  bool operator==(const DucServiceInterface_startPackagesVerify_Out& _other) const
  {
    (void) _other;
    return _default_ == _other._default_;
  }

  bool operator!=(const DucServiceInterface_startPackagesVerify_Out& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_startPackagesVerify_Out const& rhs);

class DucServiceInterface_startPackagesVerify_Result
{
private:
 ::seres::ota_duc_service::DucServiceInterface_startPackagesVerify_Out startPackagesVerifyOut_;
 ::seres::ota_duc_service::ReturnCode _return_ = ::seres::ota_duc_service::ReturnCode::OK;

public:
  DucServiceInterface_startPackagesVerify_Result() = default;

  explicit DucServiceInterface_startPackagesVerify_Result(
    const ::seres::ota_duc_service::DucServiceInterface_startPackagesVerify_Out& startPackagesVerifyOut,
    ::seres::ota_duc_service::ReturnCode _return) :
    startPackagesVerifyOut_(startPackagesVerifyOut),
    _return_(_return) { }

  const ::seres::ota_duc_service::DucServiceInterface_startPackagesVerify_Out& startPackagesVerifyOut() const { return this->startPackagesVerifyOut_; }
  ::seres::ota_duc_service::DucServiceInterface_startPackagesVerify_Out& startPackagesVerifyOut() { return this->startPackagesVerifyOut_; }
  void startPackagesVerifyOut(const ::seres::ota_duc_service::DucServiceInterface_startPackagesVerify_Out& _val_) { this->startPackagesVerifyOut_ = _val_; }
  void startPackagesVerifyOut(::seres::ota_duc_service::DucServiceInterface_startPackagesVerify_Out&& _val_) { this->startPackagesVerifyOut_ = std::move(_val_); }
  ::seres::ota_duc_service::ReturnCode _return() const { return this->_return_; }
  ::seres::ota_duc_service::ReturnCode& _return() { return this->_return_; }
  void _return(::seres::ota_duc_service::ReturnCode _val_) { this->_return_ = _val_; }

  bool operator==(const DucServiceInterface_startPackagesVerify_Result& _other) const
  {
    (void) _other;
    return startPackagesVerifyOut_ == _other.startPackagesVerifyOut_ &&
      _return_ == _other._return_;
  }

  bool operator!=(const DucServiceInterface_startPackagesVerify_Result& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_startPackagesVerify_Result const& rhs);

class DucServiceInterface_getPackagesVerifyResult_Out
{
private:
 ::seres::ota_duc_service::PackagesVerifyResult verify_Result_;

public:
  DucServiceInterface_getPackagesVerifyResult_Out() = default;

  explicit DucServiceInterface_getPackagesVerifyResult_Out(
    const ::seres::ota_duc_service::PackagesVerifyResult& verify_Result) :
    verify_Result_(verify_Result) { }

  const ::seres::ota_duc_service::PackagesVerifyResult& verify_Result() const { return this->verify_Result_; }
  ::seres::ota_duc_service::PackagesVerifyResult& verify_Result() { return this->verify_Result_; }
  void verify_Result(const ::seres::ota_duc_service::PackagesVerifyResult& _val_) { this->verify_Result_ = _val_; }
  void verify_Result(::seres::ota_duc_service::PackagesVerifyResult&& _val_) { this->verify_Result_ = std::move(_val_); }

  bool operator==(const DucServiceInterface_getPackagesVerifyResult_Out& _other) const
  {
    (void) _other;
    return verify_Result_ == _other.verify_Result_;
  }

  bool operator!=(const DucServiceInterface_getPackagesVerifyResult_Out& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_getPackagesVerifyResult_Out const& rhs);

class DucServiceInterface_getPackagesVerifyResult_Result
{
private:
 ::seres::ota_duc_service::DucServiceInterface_getPackagesVerifyResult_Out getPackagesVerifyResultOut_;
 ::seres::ota_duc_service::ReturnCode _return_ = ::seres::ota_duc_service::ReturnCode::OK;

public:
  DucServiceInterface_getPackagesVerifyResult_Result() = default;

  explicit DucServiceInterface_getPackagesVerifyResult_Result(
    const ::seres::ota_duc_service::DucServiceInterface_getPackagesVerifyResult_Out& getPackagesVerifyResultOut,
    ::seres::ota_duc_service::ReturnCode _return) :
    getPackagesVerifyResultOut_(getPackagesVerifyResultOut),
    _return_(_return) { }

  const ::seres::ota_duc_service::DucServiceInterface_getPackagesVerifyResult_Out& getPackagesVerifyResultOut() const { return this->getPackagesVerifyResultOut_; }
  ::seres::ota_duc_service::DucServiceInterface_getPackagesVerifyResult_Out& getPackagesVerifyResultOut() { return this->getPackagesVerifyResultOut_; }
  void getPackagesVerifyResultOut(const ::seres::ota_duc_service::DucServiceInterface_getPackagesVerifyResult_Out& _val_) { this->getPackagesVerifyResultOut_ = _val_; }
  void getPackagesVerifyResultOut(::seres::ota_duc_service::DucServiceInterface_getPackagesVerifyResult_Out&& _val_) { this->getPackagesVerifyResultOut_ = std::move(_val_); }
  ::seres::ota_duc_service::ReturnCode _return() const { return this->_return_; }
  ::seres::ota_duc_service::ReturnCode& _return() { return this->_return_; }
  void _return(::seres::ota_duc_service::ReturnCode _val_) { this->_return_ = _val_; }

  bool operator==(const DucServiceInterface_getPackagesVerifyResult_Result& _other) const
  {
    (void) _other;
    return getPackagesVerifyResultOut_ == _other.getPackagesVerifyResultOut_ &&
      _return_ == _other._return_;
  }

  bool operator!=(const DucServiceInterface_getPackagesVerifyResult_Result& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_getPackagesVerifyResult_Result const& rhs);

class DucServiceInterface_checkUpdateCondition_Out
{
private:
 int8_t _default_ = 0;

public:
  DucServiceInterface_checkUpdateCondition_Out() = default;

  explicit DucServiceInterface_checkUpdateCondition_Out(
    int8_t _default) :
    _default_(_default) { }

  int8_t _default() const { return this->_default_; }
  int8_t& _default() { return this->_default_; }
  void _default(int8_t _val_) { this->_default_ = _val_; }

  bool operator==(const DucServiceInterface_checkUpdateCondition_Out& _other) const
  {
    (void) _other;
    return _default_ == _other._default_;
  }

  bool operator!=(const DucServiceInterface_checkUpdateCondition_Out& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_checkUpdateCondition_Out const& rhs);

class DucServiceInterface_checkUpdateCondition_Result
{
private:
 ::seres::ota_duc_service::DucServiceInterface_checkUpdateCondition_Out checkUpdateConditionOut_;
 ::seres::ota_duc_service::ReturnCode _return_ = ::seres::ota_duc_service::ReturnCode::OK;

public:
  DucServiceInterface_checkUpdateCondition_Result() = default;

  explicit DucServiceInterface_checkUpdateCondition_Result(
    const ::seres::ota_duc_service::DucServiceInterface_checkUpdateCondition_Out& checkUpdateConditionOut,
    ::seres::ota_duc_service::ReturnCode _return) :
    checkUpdateConditionOut_(checkUpdateConditionOut),
    _return_(_return) { }

  const ::seres::ota_duc_service::DucServiceInterface_checkUpdateCondition_Out& checkUpdateConditionOut() const { return this->checkUpdateConditionOut_; }
  ::seres::ota_duc_service::DucServiceInterface_checkUpdateCondition_Out& checkUpdateConditionOut() { return this->checkUpdateConditionOut_; }
  void checkUpdateConditionOut(const ::seres::ota_duc_service::DucServiceInterface_checkUpdateCondition_Out& _val_) { this->checkUpdateConditionOut_ = _val_; }
  void checkUpdateConditionOut(::seres::ota_duc_service::DucServiceInterface_checkUpdateCondition_Out&& _val_) { this->checkUpdateConditionOut_ = std::move(_val_); }
  ::seres::ota_duc_service::ReturnCode _return() const { return this->_return_; }
  ::seres::ota_duc_service::ReturnCode& _return() { return this->_return_; }
  void _return(::seres::ota_duc_service::ReturnCode _val_) { this->_return_ = _val_; }

  bool operator==(const DucServiceInterface_checkUpdateCondition_Result& _other) const
  {
    (void) _other;
    return checkUpdateConditionOut_ == _other.checkUpdateConditionOut_ &&
      _return_ == _other._return_;
  }

  bool operator!=(const DucServiceInterface_checkUpdateCondition_Result& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_checkUpdateCondition_Result const& rhs);

class DucServiceInterface_getCheckUpdateConditionResult_Out
{
private:
 ::seres::ota_duc_service::CheckUpdateConditionResult checkcondition_Result_;

public:
  DucServiceInterface_getCheckUpdateConditionResult_Out() = default;

  explicit DucServiceInterface_getCheckUpdateConditionResult_Out(
    const ::seres::ota_duc_service::CheckUpdateConditionResult& checkcondition_Result) :
    checkcondition_Result_(checkcondition_Result) { }

  const ::seres::ota_duc_service::CheckUpdateConditionResult& checkcondition_Result() const { return this->checkcondition_Result_; }
  ::seres::ota_duc_service::CheckUpdateConditionResult& checkcondition_Result() { return this->checkcondition_Result_; }
  void checkcondition_Result(const ::seres::ota_duc_service::CheckUpdateConditionResult& _val_) { this->checkcondition_Result_ = _val_; }
  void checkcondition_Result(::seres::ota_duc_service::CheckUpdateConditionResult&& _val_) { this->checkcondition_Result_ = std::move(_val_); }

  bool operator==(const DucServiceInterface_getCheckUpdateConditionResult_Out& _other) const
  {
    (void) _other;
    return checkcondition_Result_ == _other.checkcondition_Result_;
  }

  bool operator!=(const DucServiceInterface_getCheckUpdateConditionResult_Out& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_getCheckUpdateConditionResult_Out const& rhs);

class DucServiceInterface_getCheckUpdateConditionResult_Result
{
private:
 ::seres::ota_duc_service::DucServiceInterface_getCheckUpdateConditionResult_Out getCheckUpdateConditionResultOut_;
 ::seres::ota_duc_service::ReturnCode _return_ = ::seres::ota_duc_service::ReturnCode::OK;

public:
  DucServiceInterface_getCheckUpdateConditionResult_Result() = default;

  explicit DucServiceInterface_getCheckUpdateConditionResult_Result(
    const ::seres::ota_duc_service::DucServiceInterface_getCheckUpdateConditionResult_Out& getCheckUpdateConditionResultOut,
    ::seres::ota_duc_service::ReturnCode _return) :
    getCheckUpdateConditionResultOut_(getCheckUpdateConditionResultOut),
    _return_(_return) { }

  const ::seres::ota_duc_service::DucServiceInterface_getCheckUpdateConditionResult_Out& getCheckUpdateConditionResultOut() const { return this->getCheckUpdateConditionResultOut_; }
  ::seres::ota_duc_service::DucServiceInterface_getCheckUpdateConditionResult_Out& getCheckUpdateConditionResultOut() { return this->getCheckUpdateConditionResultOut_; }
  void getCheckUpdateConditionResultOut(const ::seres::ota_duc_service::DucServiceInterface_getCheckUpdateConditionResult_Out& _val_) { this->getCheckUpdateConditionResultOut_ = _val_; }
  void getCheckUpdateConditionResultOut(::seres::ota_duc_service::DucServiceInterface_getCheckUpdateConditionResult_Out&& _val_) { this->getCheckUpdateConditionResultOut_ = std::move(_val_); }
  ::seres::ota_duc_service::ReturnCode _return() const { return this->_return_; }
  ::seres::ota_duc_service::ReturnCode& _return() { return this->_return_; }
  void _return(::seres::ota_duc_service::ReturnCode _val_) { this->_return_ = _val_; }

  bool operator==(const DucServiceInterface_getCheckUpdateConditionResult_Result& _other) const
  {
    (void) _other;
    return getCheckUpdateConditionResultOut_ == _other.getCheckUpdateConditionResultOut_ &&
      _return_ == _other._return_;
  }

  bool operator!=(const DucServiceInterface_getCheckUpdateConditionResult_Result& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_getCheckUpdateConditionResult_Result const& rhs);

class DucServiceInterface_startUpdate_Out
{
private:
 int8_t _default_ = 0;

public:
  DucServiceInterface_startUpdate_Out() = default;

  explicit DucServiceInterface_startUpdate_Out(
    int8_t _default) :
    _default_(_default) { }

  int8_t _default() const { return this->_default_; }
  int8_t& _default() { return this->_default_; }
  void _default(int8_t _val_) { this->_default_ = _val_; }

  bool operator==(const DucServiceInterface_startUpdate_Out& _other) const
  {
    (void) _other;
    return _default_ == _other._default_;
  }

  bool operator!=(const DucServiceInterface_startUpdate_Out& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_startUpdate_Out const& rhs);

class DucServiceInterface_startUpdate_Result
{
private:
 ::seres::ota_duc_service::DucServiceInterface_startUpdate_Out startUpdateOut_;
 ::seres::ota_duc_service::ReturnCode _return_ = ::seres::ota_duc_service::ReturnCode::OK;

public:
  DucServiceInterface_startUpdate_Result() = default;

  explicit DucServiceInterface_startUpdate_Result(
    const ::seres::ota_duc_service::DucServiceInterface_startUpdate_Out& startUpdateOut,
    ::seres::ota_duc_service::ReturnCode _return) :
    startUpdateOut_(startUpdateOut),
    _return_(_return) { }

  const ::seres::ota_duc_service::DucServiceInterface_startUpdate_Out& startUpdateOut() const { return this->startUpdateOut_; }
  ::seres::ota_duc_service::DucServiceInterface_startUpdate_Out& startUpdateOut() { return this->startUpdateOut_; }
  void startUpdateOut(const ::seres::ota_duc_service::DucServiceInterface_startUpdate_Out& _val_) { this->startUpdateOut_ = _val_; }
  void startUpdateOut(::seres::ota_duc_service::DucServiceInterface_startUpdate_Out&& _val_) { this->startUpdateOut_ = std::move(_val_); }
  ::seres::ota_duc_service::ReturnCode _return() const { return this->_return_; }
  ::seres::ota_duc_service::ReturnCode& _return() { return this->_return_; }
  void _return(::seres::ota_duc_service::ReturnCode _val_) { this->_return_ = _val_; }

  bool operator==(const DucServiceInterface_startUpdate_Result& _other) const
  {
    (void) _other;
    return startUpdateOut_ == _other.startUpdateOut_ &&
      _return_ == _other._return_;
  }

  bool operator!=(const DucServiceInterface_startUpdate_Result& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_startUpdate_Result const& rhs);

class DucServiceInterface_resumeUpdate_Out
{
private:
 int8_t _default_ = 0;

public:
  DucServiceInterface_resumeUpdate_Out() = default;

  explicit DucServiceInterface_resumeUpdate_Out(
    int8_t _default) :
    _default_(_default) { }

  int8_t _default() const { return this->_default_; }
  int8_t& _default() { return this->_default_; }
  void _default(int8_t _val_) { this->_default_ = _val_; }

  bool operator==(const DucServiceInterface_resumeUpdate_Out& _other) const
  {
    (void) _other;
    return _default_ == _other._default_;
  }

  bool operator!=(const DucServiceInterface_resumeUpdate_Out& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_resumeUpdate_Out const& rhs);

class DucServiceInterface_resumeUpdate_Result
{
private:
 ::seres::ota_duc_service::DucServiceInterface_resumeUpdate_Out resumeUpdateOut_;
 ::seres::ota_duc_service::ReturnCode _return_ = ::seres::ota_duc_service::ReturnCode::OK;

public:
  DucServiceInterface_resumeUpdate_Result() = default;

  explicit DucServiceInterface_resumeUpdate_Result(
    const ::seres::ota_duc_service::DucServiceInterface_resumeUpdate_Out& resumeUpdateOut,
    ::seres::ota_duc_service::ReturnCode _return) :
    resumeUpdateOut_(resumeUpdateOut),
    _return_(_return) { }

  const ::seres::ota_duc_service::DucServiceInterface_resumeUpdate_Out& resumeUpdateOut() const { return this->resumeUpdateOut_; }
  ::seres::ota_duc_service::DucServiceInterface_resumeUpdate_Out& resumeUpdateOut() { return this->resumeUpdateOut_; }
  void resumeUpdateOut(const ::seres::ota_duc_service::DucServiceInterface_resumeUpdate_Out& _val_) { this->resumeUpdateOut_ = _val_; }
  void resumeUpdateOut(::seres::ota_duc_service::DucServiceInterface_resumeUpdate_Out&& _val_) { this->resumeUpdateOut_ = std::move(_val_); }
  ::seres::ota_duc_service::ReturnCode _return() const { return this->_return_; }
  ::seres::ota_duc_service::ReturnCode& _return() { return this->_return_; }
  void _return(::seres::ota_duc_service::ReturnCode _val_) { this->_return_ = _val_; }

  bool operator==(const DucServiceInterface_resumeUpdate_Result& _other) const
  {
    (void) _other;
    return resumeUpdateOut_ == _other.resumeUpdateOut_ &&
      _return_ == _other._return_;
  }

  bool operator!=(const DucServiceInterface_resumeUpdate_Result& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_resumeUpdate_Result const& rhs);

class DucServiceInterface_pauseUpdate_Out
{
private:
 int8_t _default_ = 0;

public:
  DucServiceInterface_pauseUpdate_Out() = default;

  explicit DucServiceInterface_pauseUpdate_Out(
    int8_t _default) :
    _default_(_default) { }

  int8_t _default() const { return this->_default_; }
  int8_t& _default() { return this->_default_; }
  void _default(int8_t _val_) { this->_default_ = _val_; }

  bool operator==(const DucServiceInterface_pauseUpdate_Out& _other) const
  {
    (void) _other;
    return _default_ == _other._default_;
  }

  bool operator!=(const DucServiceInterface_pauseUpdate_Out& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_pauseUpdate_Out const& rhs);

class DucServiceInterface_pauseUpdate_Result
{
private:
 ::seres::ota_duc_service::DucServiceInterface_pauseUpdate_Out pauseUpdateOut_;
 ::seres::ota_duc_service::ReturnCode _return_ = ::seres::ota_duc_service::ReturnCode::OK;

public:
  DucServiceInterface_pauseUpdate_Result() = default;

  explicit DucServiceInterface_pauseUpdate_Result(
    const ::seres::ota_duc_service::DucServiceInterface_pauseUpdate_Out& pauseUpdateOut,
    ::seres::ota_duc_service::ReturnCode _return) :
    pauseUpdateOut_(pauseUpdateOut),
    _return_(_return) { }

  const ::seres::ota_duc_service::DucServiceInterface_pauseUpdate_Out& pauseUpdateOut() const { return this->pauseUpdateOut_; }
  ::seres::ota_duc_service::DucServiceInterface_pauseUpdate_Out& pauseUpdateOut() { return this->pauseUpdateOut_; }
  void pauseUpdateOut(const ::seres::ota_duc_service::DucServiceInterface_pauseUpdate_Out& _val_) { this->pauseUpdateOut_ = _val_; }
  void pauseUpdateOut(::seres::ota_duc_service::DucServiceInterface_pauseUpdate_Out&& _val_) { this->pauseUpdateOut_ = std::move(_val_); }
  ::seres::ota_duc_service::ReturnCode _return() const { return this->_return_; }
  ::seres::ota_duc_service::ReturnCode& _return() { return this->_return_; }
  void _return(::seres::ota_duc_service::ReturnCode _val_) { this->_return_ = _val_; }

  bool operator==(const DucServiceInterface_pauseUpdate_Result& _other) const
  {
    (void) _other;
    return pauseUpdateOut_ == _other.pauseUpdateOut_ &&
      _return_ == _other._return_;
  }

  bool operator!=(const DucServiceInterface_pauseUpdate_Result& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_pauseUpdate_Result const& rhs);

class DucServiceInterface_getUpdateProgress_Out
{
private:
 ::seres::ota_duc_service::UpdateProgress update_progress_;

public:
  DucServiceInterface_getUpdateProgress_Out() = default;

  explicit DucServiceInterface_getUpdateProgress_Out(
    const ::seres::ota_duc_service::UpdateProgress& update_progress) :
    update_progress_(update_progress) { }

  const ::seres::ota_duc_service::UpdateProgress& update_progress() const { return this->update_progress_; }
  ::seres::ota_duc_service::UpdateProgress& update_progress() { return this->update_progress_; }
  void update_progress(const ::seres::ota_duc_service::UpdateProgress& _val_) { this->update_progress_ = _val_; }
  void update_progress(::seres::ota_duc_service::UpdateProgress&& _val_) { this->update_progress_ = std::move(_val_); }

  bool operator==(const DucServiceInterface_getUpdateProgress_Out& _other) const
  {
    (void) _other;
    return update_progress_ == _other.update_progress_;
  }

  bool operator!=(const DucServiceInterface_getUpdateProgress_Out& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_getUpdateProgress_Out const& rhs);

class DucServiceInterface_getUpdateProgress_Result
{
private:
 ::seres::ota_duc_service::DucServiceInterface_getUpdateProgress_Out getUpdateProgressOut_;
 ::seres::ota_duc_service::ReturnCode _return_ = ::seres::ota_duc_service::ReturnCode::OK;

public:
  DucServiceInterface_getUpdateProgress_Result() = default;

  explicit DucServiceInterface_getUpdateProgress_Result(
    const ::seres::ota_duc_service::DucServiceInterface_getUpdateProgress_Out& getUpdateProgressOut,
    ::seres::ota_duc_service::ReturnCode _return) :
    getUpdateProgressOut_(getUpdateProgressOut),
    _return_(_return) { }

  const ::seres::ota_duc_service::DucServiceInterface_getUpdateProgress_Out& getUpdateProgressOut() const { return this->getUpdateProgressOut_; }
  ::seres::ota_duc_service::DucServiceInterface_getUpdateProgress_Out& getUpdateProgressOut() { return this->getUpdateProgressOut_; }
  void getUpdateProgressOut(const ::seres::ota_duc_service::DucServiceInterface_getUpdateProgress_Out& _val_) { this->getUpdateProgressOut_ = _val_; }
  void getUpdateProgressOut(::seres::ota_duc_service::DucServiceInterface_getUpdateProgress_Out&& _val_) { this->getUpdateProgressOut_ = std::move(_val_); }
  ::seres::ota_duc_service::ReturnCode _return() const { return this->_return_; }
  ::seres::ota_duc_service::ReturnCode& _return() { return this->_return_; }
  void _return(::seres::ota_duc_service::ReturnCode _val_) { this->_return_ = _val_; }

  bool operator==(const DucServiceInterface_getUpdateProgress_Result& _other) const
  {
    (void) _other;
    return getUpdateProgressOut_ == _other.getUpdateProgressOut_ &&
      _return_ == _other._return_;
  }

  bool operator!=(const DucServiceInterface_getUpdateProgress_Result& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_getUpdateProgress_Result const& rhs);

class DucServiceInterface_activate_Out
{
private:
 int8_t _default_ = 0;

public:
  DucServiceInterface_activate_Out() = default;

  explicit DucServiceInterface_activate_Out(
    int8_t _default) :
    _default_(_default) { }

  int8_t _default() const { return this->_default_; }
  int8_t& _default() { return this->_default_; }
  void _default(int8_t _val_) { this->_default_ = _val_; }

  bool operator==(const DucServiceInterface_activate_Out& _other) const
  {
    (void) _other;
    return _default_ == _other._default_;
  }

  bool operator!=(const DucServiceInterface_activate_Out& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_activate_Out const& rhs);

class DucServiceInterface_activate_Result
{
private:
 ::seres::ota_duc_service::DucServiceInterface_activate_Out activateOut_;
 ::seres::ota_duc_service::ReturnCode _return_ = ::seres::ota_duc_service::ReturnCode::OK;

public:
  DucServiceInterface_activate_Result() = default;

  explicit DucServiceInterface_activate_Result(
    const ::seres::ota_duc_service::DucServiceInterface_activate_Out& activateOut,
    ::seres::ota_duc_service::ReturnCode _return) :
    activateOut_(activateOut),
    _return_(_return) { }

  const ::seres::ota_duc_service::DucServiceInterface_activate_Out& activateOut() const { return this->activateOut_; }
  ::seres::ota_duc_service::DucServiceInterface_activate_Out& activateOut() { return this->activateOut_; }
  void activateOut(const ::seres::ota_duc_service::DucServiceInterface_activate_Out& _val_) { this->activateOut_ = _val_; }
  void activateOut(::seres::ota_duc_service::DucServiceInterface_activate_Out&& _val_) { this->activateOut_ = std::move(_val_); }
  ::seres::ota_duc_service::ReturnCode _return() const { return this->_return_; }
  ::seres::ota_duc_service::ReturnCode& _return() { return this->_return_; }
  void _return(::seres::ota_duc_service::ReturnCode _val_) { this->_return_ = _val_; }

  bool operator==(const DucServiceInterface_activate_Result& _other) const
  {
    (void) _other;
    return activateOut_ == _other.activateOut_ &&
      _return_ == _other._return_;
  }

  bool operator!=(const DucServiceInterface_activate_Result& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_activate_Result const& rhs);

class DucServiceInterface_rollback_Out
{
private:
 int8_t _default_ = 0;

public:
  DucServiceInterface_rollback_Out() = default;

  explicit DucServiceInterface_rollback_Out(
    int8_t _default) :
    _default_(_default) { }

  int8_t _default() const { return this->_default_; }
  int8_t& _default() { return this->_default_; }
  void _default(int8_t _val_) { this->_default_ = _val_; }

  bool operator==(const DucServiceInterface_rollback_Out& _other) const
  {
    (void) _other;
    return _default_ == _other._default_;
  }

  bool operator!=(const DucServiceInterface_rollback_Out& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_rollback_Out const& rhs);

class DucServiceInterface_rollback_Result
{
private:
 ::seres::ota_duc_service::DucServiceInterface_rollback_Out rollbackOut_;
 ::seres::ota_duc_service::ReturnCode _return_ = ::seres::ota_duc_service::ReturnCode::OK;

public:
  DucServiceInterface_rollback_Result() = default;

  explicit DucServiceInterface_rollback_Result(
    const ::seres::ota_duc_service::DucServiceInterface_rollback_Out& rollbackOut,
    ::seres::ota_duc_service::ReturnCode _return) :
    rollbackOut_(rollbackOut),
    _return_(_return) { }

  const ::seres::ota_duc_service::DucServiceInterface_rollback_Out& rollbackOut() const { return this->rollbackOut_; }
  ::seres::ota_duc_service::DucServiceInterface_rollback_Out& rollbackOut() { return this->rollbackOut_; }
  void rollbackOut(const ::seres::ota_duc_service::DucServiceInterface_rollback_Out& _val_) { this->rollbackOut_ = _val_; }
  void rollbackOut(::seres::ota_duc_service::DucServiceInterface_rollback_Out&& _val_) { this->rollbackOut_ = std::move(_val_); }
  ::seres::ota_duc_service::ReturnCode _return() const { return this->_return_; }
  ::seres::ota_duc_service::ReturnCode& _return() { return this->_return_; }
  void _return(::seres::ota_duc_service::ReturnCode _val_) { this->_return_ = _val_; }

  bool operator==(const DucServiceInterface_rollback_Result& _other) const
  {
    (void) _other;
    return rollbackOut_ == _other.rollbackOut_ &&
      _return_ == _other._return_;
  }

  bool operator!=(const DucServiceInterface_rollback_Result& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_rollback_Result const& rhs);

class DucServiceInterface_getRollbackProgress_Out
{
private:
 ::seres::ota_duc_service::UpdateProgress update_progress_;

public:
  DucServiceInterface_getRollbackProgress_Out() = default;

  explicit DucServiceInterface_getRollbackProgress_Out(
    const ::seres::ota_duc_service::UpdateProgress& update_progress) :
    update_progress_(update_progress) { }

  const ::seres::ota_duc_service::UpdateProgress& update_progress() const { return this->update_progress_; }
  ::seres::ota_duc_service::UpdateProgress& update_progress() { return this->update_progress_; }
  void update_progress(const ::seres::ota_duc_service::UpdateProgress& _val_) { this->update_progress_ = _val_; }
  void update_progress(::seres::ota_duc_service::UpdateProgress&& _val_) { this->update_progress_ = std::move(_val_); }

  bool operator==(const DucServiceInterface_getRollbackProgress_Out& _other) const
  {
    (void) _other;
    return update_progress_ == _other.update_progress_;
  }

  bool operator!=(const DucServiceInterface_getRollbackProgress_Out& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_getRollbackProgress_Out const& rhs);

class DucServiceInterface_getRollbackProgress_Result
{
private:
 ::seres::ota_duc_service::DucServiceInterface_getRollbackProgress_Out getRollbackProgressOut_;
 ::seres::ota_duc_service::ReturnCode _return_ = ::seres::ota_duc_service::ReturnCode::OK;

public:
  DucServiceInterface_getRollbackProgress_Result() = default;

  explicit DucServiceInterface_getRollbackProgress_Result(
    const ::seres::ota_duc_service::DucServiceInterface_getRollbackProgress_Out& getRollbackProgressOut,
    ::seres::ota_duc_service::ReturnCode _return) :
    getRollbackProgressOut_(getRollbackProgressOut),
    _return_(_return) { }

  const ::seres::ota_duc_service::DucServiceInterface_getRollbackProgress_Out& getRollbackProgressOut() const { return this->getRollbackProgressOut_; }
  ::seres::ota_duc_service::DucServiceInterface_getRollbackProgress_Out& getRollbackProgressOut() { return this->getRollbackProgressOut_; }
  void getRollbackProgressOut(const ::seres::ota_duc_service::DucServiceInterface_getRollbackProgress_Out& _val_) { this->getRollbackProgressOut_ = _val_; }
  void getRollbackProgressOut(::seres::ota_duc_service::DucServiceInterface_getRollbackProgress_Out&& _val_) { this->getRollbackProgressOut_ = std::move(_val_); }
  ::seres::ota_duc_service::ReturnCode _return() const { return this->_return_; }
  ::seres::ota_duc_service::ReturnCode& _return() { return this->_return_; }
  void _return(::seres::ota_duc_service::ReturnCode _val_) { this->_return_ = _val_; }

  bool operator==(const DucServiceInterface_getRollbackProgress_Result& _other) const
  {
    (void) _other;
    return getRollbackProgressOut_ == _other.getRollbackProgressOut_ &&
      _return_ == _other._return_;
  }

  bool operator!=(const DucServiceInterface_getRollbackProgress_Result& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_getRollbackProgress_Result const& rhs);

class DucServiceInterface_uploadLog_Out
{
private:
 int8_t _default_ = 0;

public:
  DucServiceInterface_uploadLog_Out() = default;

  explicit DucServiceInterface_uploadLog_Out(
    int8_t _default) :
    _default_(_default) { }

  int8_t _default() const { return this->_default_; }
  int8_t& _default() { return this->_default_; }
  void _default(int8_t _val_) { this->_default_ = _val_; }

  bool operator==(const DucServiceInterface_uploadLog_Out& _other) const
  {
    (void) _other;
    return _default_ == _other._default_;
  }

  bool operator!=(const DucServiceInterface_uploadLog_Out& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_uploadLog_Out const& rhs);

class DucServiceInterface_uploadLog_Result
{
private:
 ::seres::ota_duc_service::DucServiceInterface_uploadLog_Out uploadLogOut_;
 ::seres::ota_duc_service::ReturnCode _return_ = ::seres::ota_duc_service::ReturnCode::OK;

public:
  DucServiceInterface_uploadLog_Result() = default;

  explicit DucServiceInterface_uploadLog_Result(
    const ::seres::ota_duc_service::DucServiceInterface_uploadLog_Out& uploadLogOut,
    ::seres::ota_duc_service::ReturnCode _return) :
    uploadLogOut_(uploadLogOut),
    _return_(_return) { }

  const ::seres::ota_duc_service::DucServiceInterface_uploadLog_Out& uploadLogOut() const { return this->uploadLogOut_; }
  ::seres::ota_duc_service::DucServiceInterface_uploadLog_Out& uploadLogOut() { return this->uploadLogOut_; }
  void uploadLogOut(const ::seres::ota_duc_service::DucServiceInterface_uploadLog_Out& _val_) { this->uploadLogOut_ = _val_; }
  void uploadLogOut(::seres::ota_duc_service::DucServiceInterface_uploadLog_Out&& _val_) { this->uploadLogOut_ = std::move(_val_); }
  ::seres::ota_duc_service::ReturnCode _return() const { return this->_return_; }
  ::seres::ota_duc_service::ReturnCode& _return() { return this->_return_; }
  void _return(::seres::ota_duc_service::ReturnCode _val_) { this->_return_ = _val_; }

  bool operator==(const DucServiceInterface_uploadLog_Result& _other) const
  {
    (void) _other;
    return uploadLogOut_ == _other.uploadLogOut_ &&
      _return_ == _other._return_;
  }

  bool operator!=(const DucServiceInterface_uploadLog_Result& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_uploadLog_Result const& rhs);

class DucServiceInterface_Call
{
private:
  int32_t m__d;

  std::variant<::seres::ota_duc_service::DucServiceInterface_inventoryCollection_In, ::seres::ota_duc_service::DucServiceInterface_stopInventoryCollection_In, ::seres::ota_duc_service::DucServiceInterface_getInventoryResult_In, ::seres::ota_duc_service::DucServiceInterface_checkDownloadCondition_In, ::seres::ota_duc_service::DucServiceInterface_startDownload_In, ::seres::ota_duc_service::DucServiceInterface_downloadCtrl_In, ::seres::ota_duc_service::DucServiceInterface_getDownloadProgress_In, ::seres::ota_duc_service::DucServiceInterface_uzipPackages_In, ::seres::ota_duc_service::DucServiceInterface_getuzipPackagesResult_In, ::seres::ota_duc_service::DucServiceInterface_startPackagesVerify_In, ::seres::ota_duc_service::DucServiceInterface_getPackagesVerifyResult_In, ::seres::ota_duc_service::DucServiceInterface_checkUpdateCondition_In, ::seres::ota_duc_service::DucServiceInterface_getCheckUpdateConditionResult_In, ::seres::ota_duc_service::DucServiceInterface_startUpdate_In, ::seres::ota_duc_service::DucServiceInterface_resumeUpdate_In, ::seres::ota_duc_service::DucServiceInterface_pauseUpdate_In, ::seres::ota_duc_service::DucServiceInterface_getUpdateProgress_In, ::seres::ota_duc_service::DucServiceInterface_activate_In, ::seres::ota_duc_service::DucServiceInterface_rollback_In, ::seres::ota_duc_service::DucServiceInterface_getRollbackProgress_In, ::seres::ota_duc_service::DucServiceInterface_uploadLog_In> m__u;

  static const int32_t _default_discriminator = 0;

  static int32_t _is_discriminator(const int32_t d)
  {
    switch (d) {
      case 202679978:
        return 202679978;
      case 23721455:
        return 23721455;
      case 207766764:
        return 207766764;
      case 7343798:
        return 7343798;
      case 38715577:
        return 38715577;
      case 33876474:
        return 33876474;
      case 173346514:
        return 173346514;
      case 113763844:
        return 113763844;
      case 256795369:
        return 256795369;
      case 194734422:
        return 194734422;
      case 4313302:
        return 4313302;
      case 178527735:
        return 178527735;
      case 200329384:
        return 200329384;
      case 136901098:
        return 136901098;
      case 29478526:
        return 29478526;
      case 251480810:
        return 251480810;
      case 66921313:
        return 66921313;
      case 146417871:
        return 146417871;
      case 170403593:
        return 170403593;
      case 31250426:
        return 31250426;
      case 126211184:
        return 126211184;
      default:
        break;
    }
    return _default_discriminator;
  }

  static bool _is_compatible_discriminator(const int32_t d1, const int32_t d2)
  {
    return _is_discriminator(d1) == _is_discriminator(d2);
  }

public:
  DucServiceInterface_Call() :
      m__d(_default_discriminator),
      m__u()
 { }

  int32_t _d() const
  {
    return m__d;
  }

  void _d(int32_t d)
  {
    if (!_is_compatible_discriminator(m__d, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator value does not match current discriminator");
    }
    m__d = d;
  }

  const ::seres::ota_duc_service::DucServiceInterface_inventoryCollection_In &inventoryCollectionIn() const
  {
    if (!_is_compatible_discriminator(m__d, 202679978)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_inventoryCollection_In>(m__u);
  }

  ::seres::ota_duc_service::DucServiceInterface_inventoryCollection_In& inventoryCollectionIn()
  {
    if (!_is_compatible_discriminator(m__d, 202679978)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_inventoryCollection_In>(m__u);
  }

  void inventoryCollectionIn(const ::seres::ota_duc_service::DucServiceInterface_inventoryCollection_In& u, int32_t d = 202679978)
  {
    if (!_is_compatible_discriminator(202679978, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  void inventoryCollectionIn(::seres::ota_duc_service::DucServiceInterface_inventoryCollection_In&& u, int32_t d = 202679978)
  {
    if (!_is_compatible_discriminator(202679978, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  const ::seres::ota_duc_service::DucServiceInterface_stopInventoryCollection_In &stopInventoryCollectionIn() const
  {
    if (!_is_compatible_discriminator(m__d, 23721455)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_stopInventoryCollection_In>(m__u);
  }

  ::seres::ota_duc_service::DucServiceInterface_stopInventoryCollection_In& stopInventoryCollectionIn()
  {
    if (!_is_compatible_discriminator(m__d, 23721455)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_stopInventoryCollection_In>(m__u);
  }

  void stopInventoryCollectionIn(const ::seres::ota_duc_service::DucServiceInterface_stopInventoryCollection_In& u, int32_t d = 23721455)
  {
    if (!_is_compatible_discriminator(23721455, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  void stopInventoryCollectionIn(::seres::ota_duc_service::DucServiceInterface_stopInventoryCollection_In&& u, int32_t d = 23721455)
  {
    if (!_is_compatible_discriminator(23721455, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  const ::seres::ota_duc_service::DucServiceInterface_getInventoryResult_In &getInventoryResultIn() const
  {
    if (!_is_compatible_discriminator(m__d, 207766764)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_getInventoryResult_In>(m__u);
  }

  ::seres::ota_duc_service::DucServiceInterface_getInventoryResult_In& getInventoryResultIn()
  {
    if (!_is_compatible_discriminator(m__d, 207766764)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_getInventoryResult_In>(m__u);
  }

  void getInventoryResultIn(const ::seres::ota_duc_service::DucServiceInterface_getInventoryResult_In& u, int32_t d = 207766764)
  {
    if (!_is_compatible_discriminator(207766764, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  void getInventoryResultIn(::seres::ota_duc_service::DucServiceInterface_getInventoryResult_In&& u, int32_t d = 207766764)
  {
    if (!_is_compatible_discriminator(207766764, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  const ::seres::ota_duc_service::DucServiceInterface_checkDownloadCondition_In &checkDownloadConditionIn() const
  {
    if (!_is_compatible_discriminator(m__d, 7343798)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_checkDownloadCondition_In>(m__u);
  }

  ::seres::ota_duc_service::DucServiceInterface_checkDownloadCondition_In& checkDownloadConditionIn()
  {
    if (!_is_compatible_discriminator(m__d, 7343798)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_checkDownloadCondition_In>(m__u);
  }

  void checkDownloadConditionIn(const ::seres::ota_duc_service::DucServiceInterface_checkDownloadCondition_In& u, int32_t d = 7343798)
  {
    if (!_is_compatible_discriminator(7343798, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  void checkDownloadConditionIn(::seres::ota_duc_service::DucServiceInterface_checkDownloadCondition_In&& u, int32_t d = 7343798)
  {
    if (!_is_compatible_discriminator(7343798, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  const ::seres::ota_duc_service::DucServiceInterface_startDownload_In &startDownloadIn() const
  {
    if (!_is_compatible_discriminator(m__d, 38715577)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_startDownload_In>(m__u);
  }

  ::seres::ota_duc_service::DucServiceInterface_startDownload_In& startDownloadIn()
  {
    if (!_is_compatible_discriminator(m__d, 38715577)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_startDownload_In>(m__u);
  }

  void startDownloadIn(const ::seres::ota_duc_service::DucServiceInterface_startDownload_In& u, int32_t d = 38715577)
  {
    if (!_is_compatible_discriminator(38715577, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  void startDownloadIn(::seres::ota_duc_service::DucServiceInterface_startDownload_In&& u, int32_t d = 38715577)
  {
    if (!_is_compatible_discriminator(38715577, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  const ::seres::ota_duc_service::DucServiceInterface_downloadCtrl_In &downloadCtrlIn() const
  {
    if (!_is_compatible_discriminator(m__d, 33876474)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_downloadCtrl_In>(m__u);
  }

  ::seres::ota_duc_service::DucServiceInterface_downloadCtrl_In& downloadCtrlIn()
  {
    if (!_is_compatible_discriminator(m__d, 33876474)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_downloadCtrl_In>(m__u);
  }

  void downloadCtrlIn(const ::seres::ota_duc_service::DucServiceInterface_downloadCtrl_In& u, int32_t d = 33876474)
  {
    if (!_is_compatible_discriminator(33876474, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  void downloadCtrlIn(::seres::ota_duc_service::DucServiceInterface_downloadCtrl_In&& u, int32_t d = 33876474)
  {
    if (!_is_compatible_discriminator(33876474, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  const ::seres::ota_duc_service::DucServiceInterface_getDownloadProgress_In &getDownloadProgressIn() const
  {
    if (!_is_compatible_discriminator(m__d, 173346514)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_getDownloadProgress_In>(m__u);
  }

  ::seres::ota_duc_service::DucServiceInterface_getDownloadProgress_In& getDownloadProgressIn()
  {
    if (!_is_compatible_discriminator(m__d, 173346514)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_getDownloadProgress_In>(m__u);
  }

  void getDownloadProgressIn(const ::seres::ota_duc_service::DucServiceInterface_getDownloadProgress_In& u, int32_t d = 173346514)
  {
    if (!_is_compatible_discriminator(173346514, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  void getDownloadProgressIn(::seres::ota_duc_service::DucServiceInterface_getDownloadProgress_In&& u, int32_t d = 173346514)
  {
    if (!_is_compatible_discriminator(173346514, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  const ::seres::ota_duc_service::DucServiceInterface_uzipPackages_In &uzipPackagesIn() const
  {
    if (!_is_compatible_discriminator(m__d, 113763844)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_uzipPackages_In>(m__u);
  }

  ::seres::ota_duc_service::DucServiceInterface_uzipPackages_In& uzipPackagesIn()
  {
    if (!_is_compatible_discriminator(m__d, 113763844)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_uzipPackages_In>(m__u);
  }

  void uzipPackagesIn(const ::seres::ota_duc_service::DucServiceInterface_uzipPackages_In& u, int32_t d = 113763844)
  {
    if (!_is_compatible_discriminator(113763844, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  void uzipPackagesIn(::seres::ota_duc_service::DucServiceInterface_uzipPackages_In&& u, int32_t d = 113763844)
  {
    if (!_is_compatible_discriminator(113763844, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  const ::seres::ota_duc_service::DucServiceInterface_getuzipPackagesResult_In &getuzipPackagesResultIn() const
  {
    if (!_is_compatible_discriminator(m__d, 256795369)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_getuzipPackagesResult_In>(m__u);
  }

  ::seres::ota_duc_service::DucServiceInterface_getuzipPackagesResult_In& getuzipPackagesResultIn()
  {
    if (!_is_compatible_discriminator(m__d, 256795369)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_getuzipPackagesResult_In>(m__u);
  }

  void getuzipPackagesResultIn(const ::seres::ota_duc_service::DucServiceInterface_getuzipPackagesResult_In& u, int32_t d = 256795369)
  {
    if (!_is_compatible_discriminator(256795369, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  void getuzipPackagesResultIn(::seres::ota_duc_service::DucServiceInterface_getuzipPackagesResult_In&& u, int32_t d = 256795369)
  {
    if (!_is_compatible_discriminator(256795369, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  const ::seres::ota_duc_service::DucServiceInterface_startPackagesVerify_In &startPackagesVerifyIn() const
  {
    if (!_is_compatible_discriminator(m__d, 194734422)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_startPackagesVerify_In>(m__u);
  }

  ::seres::ota_duc_service::DucServiceInterface_startPackagesVerify_In& startPackagesVerifyIn()
  {
    if (!_is_compatible_discriminator(m__d, 194734422)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_startPackagesVerify_In>(m__u);
  }

  void startPackagesVerifyIn(const ::seres::ota_duc_service::DucServiceInterface_startPackagesVerify_In& u, int32_t d = 194734422)
  {
    if (!_is_compatible_discriminator(194734422, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  void startPackagesVerifyIn(::seres::ota_duc_service::DucServiceInterface_startPackagesVerify_In&& u, int32_t d = 194734422)
  {
    if (!_is_compatible_discriminator(194734422, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  const ::seres::ota_duc_service::DucServiceInterface_getPackagesVerifyResult_In &getPackagesVerifyResultIn() const
  {
    if (!_is_compatible_discriminator(m__d, 4313302)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_getPackagesVerifyResult_In>(m__u);
  }

  ::seres::ota_duc_service::DucServiceInterface_getPackagesVerifyResult_In& getPackagesVerifyResultIn()
  {
    if (!_is_compatible_discriminator(m__d, 4313302)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_getPackagesVerifyResult_In>(m__u);
  }

  void getPackagesVerifyResultIn(const ::seres::ota_duc_service::DucServiceInterface_getPackagesVerifyResult_In& u, int32_t d = 4313302)
  {
    if (!_is_compatible_discriminator(4313302, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  void getPackagesVerifyResultIn(::seres::ota_duc_service::DucServiceInterface_getPackagesVerifyResult_In&& u, int32_t d = 4313302)
  {
    if (!_is_compatible_discriminator(4313302, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  const ::seres::ota_duc_service::DucServiceInterface_checkUpdateCondition_In &checkUpdateConditionIn() const
  {
    if (!_is_compatible_discriminator(m__d, 178527735)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_checkUpdateCondition_In>(m__u);
  }

  ::seres::ota_duc_service::DucServiceInterface_checkUpdateCondition_In& checkUpdateConditionIn()
  {
    if (!_is_compatible_discriminator(m__d, 178527735)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_checkUpdateCondition_In>(m__u);
  }

  void checkUpdateConditionIn(const ::seres::ota_duc_service::DucServiceInterface_checkUpdateCondition_In& u, int32_t d = 178527735)
  {
    if (!_is_compatible_discriminator(178527735, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  void checkUpdateConditionIn(::seres::ota_duc_service::DucServiceInterface_checkUpdateCondition_In&& u, int32_t d = 178527735)
  {
    if (!_is_compatible_discriminator(178527735, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  const ::seres::ota_duc_service::DucServiceInterface_getCheckUpdateConditionResult_In &getCheckUpdateConditionResultIn() const
  {
    if (!_is_compatible_discriminator(m__d, 200329384)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_getCheckUpdateConditionResult_In>(m__u);
  }

  ::seres::ota_duc_service::DucServiceInterface_getCheckUpdateConditionResult_In& getCheckUpdateConditionResultIn()
  {
    if (!_is_compatible_discriminator(m__d, 200329384)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_getCheckUpdateConditionResult_In>(m__u);
  }

  void getCheckUpdateConditionResultIn(const ::seres::ota_duc_service::DucServiceInterface_getCheckUpdateConditionResult_In& u, int32_t d = 200329384)
  {
    if (!_is_compatible_discriminator(200329384, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  void getCheckUpdateConditionResultIn(::seres::ota_duc_service::DucServiceInterface_getCheckUpdateConditionResult_In&& u, int32_t d = 200329384)
  {
    if (!_is_compatible_discriminator(200329384, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  const ::seres::ota_duc_service::DucServiceInterface_startUpdate_In &startUpdateIn() const
  {
    if (!_is_compatible_discriminator(m__d, 136901098)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_startUpdate_In>(m__u);
  }

  ::seres::ota_duc_service::DucServiceInterface_startUpdate_In& startUpdateIn()
  {
    if (!_is_compatible_discriminator(m__d, 136901098)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_startUpdate_In>(m__u);
  }

  void startUpdateIn(const ::seres::ota_duc_service::DucServiceInterface_startUpdate_In& u, int32_t d = 136901098)
  {
    if (!_is_compatible_discriminator(136901098, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  void startUpdateIn(::seres::ota_duc_service::DucServiceInterface_startUpdate_In&& u, int32_t d = 136901098)
  {
    if (!_is_compatible_discriminator(136901098, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  const ::seres::ota_duc_service::DucServiceInterface_resumeUpdate_In &resumeUpdateIn() const
  {
    if (!_is_compatible_discriminator(m__d, 29478526)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_resumeUpdate_In>(m__u);
  }

  ::seres::ota_duc_service::DucServiceInterface_resumeUpdate_In& resumeUpdateIn()
  {
    if (!_is_compatible_discriminator(m__d, 29478526)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_resumeUpdate_In>(m__u);
  }

  void resumeUpdateIn(const ::seres::ota_duc_service::DucServiceInterface_resumeUpdate_In& u, int32_t d = 29478526)
  {
    if (!_is_compatible_discriminator(29478526, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  void resumeUpdateIn(::seres::ota_duc_service::DucServiceInterface_resumeUpdate_In&& u, int32_t d = 29478526)
  {
    if (!_is_compatible_discriminator(29478526, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  const ::seres::ota_duc_service::DucServiceInterface_pauseUpdate_In &pauseUpdateIn() const
  {
    if (!_is_compatible_discriminator(m__d, 251480810)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_pauseUpdate_In>(m__u);
  }

  ::seres::ota_duc_service::DucServiceInterface_pauseUpdate_In& pauseUpdateIn()
  {
    if (!_is_compatible_discriminator(m__d, 251480810)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_pauseUpdate_In>(m__u);
  }

  void pauseUpdateIn(const ::seres::ota_duc_service::DucServiceInterface_pauseUpdate_In& u, int32_t d = 251480810)
  {
    if (!_is_compatible_discriminator(251480810, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  void pauseUpdateIn(::seres::ota_duc_service::DucServiceInterface_pauseUpdate_In&& u, int32_t d = 251480810)
  {
    if (!_is_compatible_discriminator(251480810, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  const ::seres::ota_duc_service::DucServiceInterface_getUpdateProgress_In &getUpdateProgressIn() const
  {
    if (!_is_compatible_discriminator(m__d, 66921313)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_getUpdateProgress_In>(m__u);
  }

  ::seres::ota_duc_service::DucServiceInterface_getUpdateProgress_In& getUpdateProgressIn()
  {
    if (!_is_compatible_discriminator(m__d, 66921313)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_getUpdateProgress_In>(m__u);
  }

  void getUpdateProgressIn(const ::seres::ota_duc_service::DucServiceInterface_getUpdateProgress_In& u, int32_t d = 66921313)
  {
    if (!_is_compatible_discriminator(66921313, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  void getUpdateProgressIn(::seres::ota_duc_service::DucServiceInterface_getUpdateProgress_In&& u, int32_t d = 66921313)
  {
    if (!_is_compatible_discriminator(66921313, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  const ::seres::ota_duc_service::DucServiceInterface_activate_In &activateIn() const
  {
    if (!_is_compatible_discriminator(m__d, 146417871)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_activate_In>(m__u);
  }

  ::seres::ota_duc_service::DucServiceInterface_activate_In& activateIn()
  {
    if (!_is_compatible_discriminator(m__d, 146417871)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_activate_In>(m__u);
  }

  void activateIn(const ::seres::ota_duc_service::DucServiceInterface_activate_In& u, int32_t d = 146417871)
  {
    if (!_is_compatible_discriminator(146417871, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  void activateIn(::seres::ota_duc_service::DucServiceInterface_activate_In&& u, int32_t d = 146417871)
  {
    if (!_is_compatible_discriminator(146417871, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  const ::seres::ota_duc_service::DucServiceInterface_rollback_In &rollbackIn() const
  {
    if (!_is_compatible_discriminator(m__d, 170403593)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_rollback_In>(m__u);
  }

  ::seres::ota_duc_service::DucServiceInterface_rollback_In& rollbackIn()
  {
    if (!_is_compatible_discriminator(m__d, 170403593)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_rollback_In>(m__u);
  }

  void rollbackIn(const ::seres::ota_duc_service::DucServiceInterface_rollback_In& u, int32_t d = 170403593)
  {
    if (!_is_compatible_discriminator(170403593, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  void rollbackIn(::seres::ota_duc_service::DucServiceInterface_rollback_In&& u, int32_t d = 170403593)
  {
    if (!_is_compatible_discriminator(170403593, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  const ::seres::ota_duc_service::DucServiceInterface_getRollbackProgress_In &getRollbackProgressIn() const
  {
    if (!_is_compatible_discriminator(m__d, 31250426)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_getRollbackProgress_In>(m__u);
  }

  ::seres::ota_duc_service::DucServiceInterface_getRollbackProgress_In& getRollbackProgressIn()
  {
    if (!_is_compatible_discriminator(m__d, 31250426)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_getRollbackProgress_In>(m__u);
  }

  void getRollbackProgressIn(const ::seres::ota_duc_service::DucServiceInterface_getRollbackProgress_In& u, int32_t d = 31250426)
  {
    if (!_is_compatible_discriminator(31250426, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  void getRollbackProgressIn(::seres::ota_duc_service::DucServiceInterface_getRollbackProgress_In&& u, int32_t d = 31250426)
  {
    if (!_is_compatible_discriminator(31250426, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  const ::seres::ota_duc_service::DucServiceInterface_uploadLog_In &uploadLogIn() const
  {
    if (!_is_compatible_discriminator(m__d, 126211184)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_uploadLog_In>(m__u);
  }

  ::seres::ota_duc_service::DucServiceInterface_uploadLog_In& uploadLogIn()
  {
    if (!_is_compatible_discriminator(m__d, 126211184)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_uploadLog_In>(m__u);
  }

  void uploadLogIn(const ::seres::ota_duc_service::DucServiceInterface_uploadLog_In& u, int32_t d = 126211184)
  {
    if (!_is_compatible_discriminator(126211184, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  void uploadLogIn(::seres::ota_duc_service::DucServiceInterface_uploadLog_In&& u, int32_t d = 126211184)
  {
    if (!_is_compatible_discriminator(126211184, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  bool operator==(const DucServiceInterface_Call& _other) const
  {
    if (_d() != _other._d()) return false;
    switch (_d()) {
      case 202679978:
        return inventoryCollectionIn() == _other.inventoryCollectionIn();
      case 23721455:
        return stopInventoryCollectionIn() == _other.stopInventoryCollectionIn();
      case 207766764:
        return getInventoryResultIn() == _other.getInventoryResultIn();
      case 7343798:
        return checkDownloadConditionIn() == _other.checkDownloadConditionIn();
      case 38715577:
        return startDownloadIn() == _other.startDownloadIn();
      case 33876474:
        return downloadCtrlIn() == _other.downloadCtrlIn();
      case 173346514:
        return getDownloadProgressIn() == _other.getDownloadProgressIn();
      case 113763844:
        return uzipPackagesIn() == _other.uzipPackagesIn();
      case 256795369:
        return getuzipPackagesResultIn() == _other.getuzipPackagesResultIn();
      case 194734422:
        return startPackagesVerifyIn() == _other.startPackagesVerifyIn();
      case 4313302:
        return getPackagesVerifyResultIn() == _other.getPackagesVerifyResultIn();
      case 178527735:
        return checkUpdateConditionIn() == _other.checkUpdateConditionIn();
      case 200329384:
        return getCheckUpdateConditionResultIn() == _other.getCheckUpdateConditionResultIn();
      case 136901098:
        return startUpdateIn() == _other.startUpdateIn();
      case 29478526:
        return resumeUpdateIn() == _other.resumeUpdateIn();
      case 251480810:
        return pauseUpdateIn() == _other.pauseUpdateIn();
      case 66921313:
        return getUpdateProgressIn() == _other.getUpdateProgressIn();
      case 146417871:
        return activateIn() == _other.activateIn();
      case 170403593:
        return rollbackIn() == _other.rollbackIn();
      case 31250426:
        return getRollbackProgressIn() == _other.getRollbackProgressIn();
      case 126211184:
        return uploadLogIn() == _other.uploadLogIn();
      default:
        return true;
    }
    return true;
  }

  bool operator!=(const DucServiceInterface_Call& _other) const
  {
    return !(*this == _other);
  }

  void _default(int32_t d = 0)
  {
    if (!_is_compatible_discriminator(d, 0))
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match default branch");
    m__d = d;
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_Call const& rhs);
class DucServiceInterface_Request
{
private:
 ::dds::rpc::RequestHeader header_;
 ::seres::ota_duc_service::DucServiceInterface_Call data_;

public:
  DucServiceInterface_Request() = default;

  explicit DucServiceInterface_Request(
    const ::dds::rpc::RequestHeader& header,
    const ::seres::ota_duc_service::DucServiceInterface_Call& data) :
    header_(header),
    data_(data) { }

  const ::dds::rpc::RequestHeader& header() const { return this->header_; }
  ::dds::rpc::RequestHeader& header() { return this->header_; }
  void header(const ::dds::rpc::RequestHeader& _val_) { this->header_ = _val_; }
  void header(::dds::rpc::RequestHeader&& _val_) { this->header_ = std::move(_val_); }
  const ::seres::ota_duc_service::DucServiceInterface_Call& data() const { return this->data_; }
  ::seres::ota_duc_service::DucServiceInterface_Call& data() { return this->data_; }
  void data(const ::seres::ota_duc_service::DucServiceInterface_Call& _val_) { this->data_ = _val_; }
  void data(::seres::ota_duc_service::DucServiceInterface_Call&& _val_) { this->data_ = std::move(_val_); }

  bool operator==(const DucServiceInterface_Request& _other) const
  {
    (void) _other;
    return header_ == _other.header_ &&
      data_ == _other.data_;
  }

  bool operator!=(const DucServiceInterface_Request& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_Request const& rhs);

class DucServiceInterface_Return
{
private:
  int32_t m__d;

  std::variant<::seres::ota_duc_service::DucServiceInterface_inventoryCollection_Result, ::seres::ota_duc_service::DucServiceInterface_stopInventoryCollection_Result, ::seres::ota_duc_service::DucServiceInterface_getInventoryResult_Result, ::seres::ota_duc_service::DucServiceInterface_checkDownloadCondition_Result, ::seres::ota_duc_service::DucServiceInterface_startDownload_Result, ::seres::ota_duc_service::DucServiceInterface_downloadCtrl_Result, ::seres::ota_duc_service::DucServiceInterface_getDownloadProgress_Result, ::seres::ota_duc_service::DucServiceInterface_uzipPackages_Result, ::seres::ota_duc_service::DucServiceInterface_getuzipPackagesResult_Result, ::seres::ota_duc_service::DucServiceInterface_startPackagesVerify_Result, ::seres::ota_duc_service::DucServiceInterface_getPackagesVerifyResult_Result, ::seres::ota_duc_service::DucServiceInterface_checkUpdateCondition_Result, ::seres::ota_duc_service::DucServiceInterface_getCheckUpdateConditionResult_Result, ::seres::ota_duc_service::DucServiceInterface_startUpdate_Result, ::seres::ota_duc_service::DucServiceInterface_resumeUpdate_Result, ::seres::ota_duc_service::DucServiceInterface_pauseUpdate_Result, ::seres::ota_duc_service::DucServiceInterface_getUpdateProgress_Result, ::seres::ota_duc_service::DucServiceInterface_activate_Result, ::seres::ota_duc_service::DucServiceInterface_rollback_Result, ::seres::ota_duc_service::DucServiceInterface_getRollbackProgress_Result, ::seres::ota_duc_service::DucServiceInterface_uploadLog_Result> m__u;

  static const int32_t _default_discriminator = 0;

  static int32_t _is_discriminator(const int32_t d)
  {
    switch (d) {
      case 202679978:
        return 202679978;
      case 23721455:
        return 23721455;
      case 207766764:
        return 207766764;
      case 7343798:
        return 7343798;
      case 38715577:
        return 38715577;
      case 33876474:
        return 33876474;
      case 173346514:
        return 173346514;
      case 113763844:
        return 113763844;
      case 256795369:
        return 256795369;
      case 194734422:
        return 194734422;
      case 4313302:
        return 4313302;
      case 178527735:
        return 178527735;
      case 200329384:
        return 200329384;
      case 136901098:
        return 136901098;
      case 29478526:
        return 29478526;
      case 251480810:
        return 251480810;
      case 66921313:
        return 66921313;
      case 146417871:
        return 146417871;
      case 170403593:
        return 170403593;
      case 31250426:
        return 31250426;
      case 126211184:
        return 126211184;
      default:
        break;
    }
    return _default_discriminator;
  }

  static bool _is_compatible_discriminator(const int32_t d1, const int32_t d2)
  {
    return _is_discriminator(d1) == _is_discriminator(d2);
  }

public:
  DucServiceInterface_Return() :
      m__d(_default_discriminator),
      m__u()
 { }

  int32_t _d() const
  {
    return m__d;
  }

  void _d(int32_t d)
  {
    if (!_is_compatible_discriminator(m__d, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator value does not match current discriminator");
    }
    m__d = d;
  }

  const ::seres::ota_duc_service::DucServiceInterface_inventoryCollection_Result &inventoryCollectionResult() const
  {
    if (!_is_compatible_discriminator(m__d, 202679978)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_inventoryCollection_Result>(m__u);
  }

  ::seres::ota_duc_service::DucServiceInterface_inventoryCollection_Result& inventoryCollectionResult()
  {
    if (!_is_compatible_discriminator(m__d, 202679978)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_inventoryCollection_Result>(m__u);
  }

  void inventoryCollectionResult(const ::seres::ota_duc_service::DucServiceInterface_inventoryCollection_Result& u, int32_t d = 202679978)
  {
    if (!_is_compatible_discriminator(202679978, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  void inventoryCollectionResult(::seres::ota_duc_service::DucServiceInterface_inventoryCollection_Result&& u, int32_t d = 202679978)
  {
    if (!_is_compatible_discriminator(202679978, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  const ::seres::ota_duc_service::DucServiceInterface_stopInventoryCollection_Result &stopInventoryCollectionResult() const
  {
    if (!_is_compatible_discriminator(m__d, 23721455)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_stopInventoryCollection_Result>(m__u);
  }

  ::seres::ota_duc_service::DucServiceInterface_stopInventoryCollection_Result& stopInventoryCollectionResult()
  {
    if (!_is_compatible_discriminator(m__d, 23721455)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_stopInventoryCollection_Result>(m__u);
  }

  void stopInventoryCollectionResult(const ::seres::ota_duc_service::DucServiceInterface_stopInventoryCollection_Result& u, int32_t d = 23721455)
  {
    if (!_is_compatible_discriminator(23721455, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  void stopInventoryCollectionResult(::seres::ota_duc_service::DucServiceInterface_stopInventoryCollection_Result&& u, int32_t d = 23721455)
  {
    if (!_is_compatible_discriminator(23721455, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  const ::seres::ota_duc_service::DucServiceInterface_getInventoryResult_Result &getInventoryResultResult() const
  {
    if (!_is_compatible_discriminator(m__d, 207766764)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_getInventoryResult_Result>(m__u);
  }

  ::seres::ota_duc_service::DucServiceInterface_getInventoryResult_Result& getInventoryResultResult()
  {
    if (!_is_compatible_discriminator(m__d, 207766764)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_getInventoryResult_Result>(m__u);
  }

  void getInventoryResultResult(const ::seres::ota_duc_service::DucServiceInterface_getInventoryResult_Result& u, int32_t d = 207766764)
  {
    if (!_is_compatible_discriminator(207766764, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  void getInventoryResultResult(::seres::ota_duc_service::DucServiceInterface_getInventoryResult_Result&& u, int32_t d = 207766764)
  {
    if (!_is_compatible_discriminator(207766764, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  const ::seres::ota_duc_service::DucServiceInterface_checkDownloadCondition_Result &checkDownloadConditionResult() const
  {
    if (!_is_compatible_discriminator(m__d, 7343798)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_checkDownloadCondition_Result>(m__u);
  }

  ::seres::ota_duc_service::DucServiceInterface_checkDownloadCondition_Result& checkDownloadConditionResult()
  {
    if (!_is_compatible_discriminator(m__d, 7343798)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_checkDownloadCondition_Result>(m__u);
  }

  void checkDownloadConditionResult(const ::seres::ota_duc_service::DucServiceInterface_checkDownloadCondition_Result& u, int32_t d = 7343798)
  {
    if (!_is_compatible_discriminator(7343798, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  void checkDownloadConditionResult(::seres::ota_duc_service::DucServiceInterface_checkDownloadCondition_Result&& u, int32_t d = 7343798)
  {
    if (!_is_compatible_discriminator(7343798, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  const ::seres::ota_duc_service::DucServiceInterface_startDownload_Result &startDownloadResult() const
  {
    if (!_is_compatible_discriminator(m__d, 38715577)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_startDownload_Result>(m__u);
  }

  ::seres::ota_duc_service::DucServiceInterface_startDownload_Result& startDownloadResult()
  {
    if (!_is_compatible_discriminator(m__d, 38715577)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_startDownload_Result>(m__u);
  }

  void startDownloadResult(const ::seres::ota_duc_service::DucServiceInterface_startDownload_Result& u, int32_t d = 38715577)
  {
    if (!_is_compatible_discriminator(38715577, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  void startDownloadResult(::seres::ota_duc_service::DucServiceInterface_startDownload_Result&& u, int32_t d = 38715577)
  {
    if (!_is_compatible_discriminator(38715577, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  const ::seres::ota_duc_service::DucServiceInterface_downloadCtrl_Result &downloadCtrlResult() const
  {
    if (!_is_compatible_discriminator(m__d, 33876474)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_downloadCtrl_Result>(m__u);
  }

  ::seres::ota_duc_service::DucServiceInterface_downloadCtrl_Result& downloadCtrlResult()
  {
    if (!_is_compatible_discriminator(m__d, 33876474)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_downloadCtrl_Result>(m__u);
  }

  void downloadCtrlResult(const ::seres::ota_duc_service::DucServiceInterface_downloadCtrl_Result& u, int32_t d = 33876474)
  {
    if (!_is_compatible_discriminator(33876474, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  void downloadCtrlResult(::seres::ota_duc_service::DucServiceInterface_downloadCtrl_Result&& u, int32_t d = 33876474)
  {
    if (!_is_compatible_discriminator(33876474, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  const ::seres::ota_duc_service::DucServiceInterface_getDownloadProgress_Result &getDownloadProgressResult() const
  {
    if (!_is_compatible_discriminator(m__d, 173346514)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_getDownloadProgress_Result>(m__u);
  }

  ::seres::ota_duc_service::DucServiceInterface_getDownloadProgress_Result& getDownloadProgressResult()
  {
    if (!_is_compatible_discriminator(m__d, 173346514)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_getDownloadProgress_Result>(m__u);
  }

  void getDownloadProgressResult(const ::seres::ota_duc_service::DucServiceInterface_getDownloadProgress_Result& u, int32_t d = 173346514)
  {
    if (!_is_compatible_discriminator(173346514, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  void getDownloadProgressResult(::seres::ota_duc_service::DucServiceInterface_getDownloadProgress_Result&& u, int32_t d = 173346514)
  {
    if (!_is_compatible_discriminator(173346514, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  const ::seres::ota_duc_service::DucServiceInterface_uzipPackages_Result &uzipPackagesResult() const
  {
    if (!_is_compatible_discriminator(m__d, 113763844)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_uzipPackages_Result>(m__u);
  }

  ::seres::ota_duc_service::DucServiceInterface_uzipPackages_Result& uzipPackagesResult()
  {
    if (!_is_compatible_discriminator(m__d, 113763844)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_uzipPackages_Result>(m__u);
  }

  void uzipPackagesResult(const ::seres::ota_duc_service::DucServiceInterface_uzipPackages_Result& u, int32_t d = 113763844)
  {
    if (!_is_compatible_discriminator(113763844, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  void uzipPackagesResult(::seres::ota_duc_service::DucServiceInterface_uzipPackages_Result&& u, int32_t d = 113763844)
  {
    if (!_is_compatible_discriminator(113763844, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  const ::seres::ota_duc_service::DucServiceInterface_getuzipPackagesResult_Result &getuzipPackagesResultResult() const
  {
    if (!_is_compatible_discriminator(m__d, 256795369)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_getuzipPackagesResult_Result>(m__u);
  }

  ::seres::ota_duc_service::DucServiceInterface_getuzipPackagesResult_Result& getuzipPackagesResultResult()
  {
    if (!_is_compatible_discriminator(m__d, 256795369)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_getuzipPackagesResult_Result>(m__u);
  }

  void getuzipPackagesResultResult(const ::seres::ota_duc_service::DucServiceInterface_getuzipPackagesResult_Result& u, int32_t d = 256795369)
  {
    if (!_is_compatible_discriminator(256795369, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  void getuzipPackagesResultResult(::seres::ota_duc_service::DucServiceInterface_getuzipPackagesResult_Result&& u, int32_t d = 256795369)
  {
    if (!_is_compatible_discriminator(256795369, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  const ::seres::ota_duc_service::DucServiceInterface_startPackagesVerify_Result &startPackagesVerifyResult() const
  {
    if (!_is_compatible_discriminator(m__d, 194734422)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_startPackagesVerify_Result>(m__u);
  }

  ::seres::ota_duc_service::DucServiceInterface_startPackagesVerify_Result& startPackagesVerifyResult()
  {
    if (!_is_compatible_discriminator(m__d, 194734422)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_startPackagesVerify_Result>(m__u);
  }

  void startPackagesVerifyResult(const ::seres::ota_duc_service::DucServiceInterface_startPackagesVerify_Result& u, int32_t d = 194734422)
  {
    if (!_is_compatible_discriminator(194734422, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  void startPackagesVerifyResult(::seres::ota_duc_service::DucServiceInterface_startPackagesVerify_Result&& u, int32_t d = 194734422)
  {
    if (!_is_compatible_discriminator(194734422, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  const ::seres::ota_duc_service::DucServiceInterface_getPackagesVerifyResult_Result &getPackagesVerifyResultResult() const
  {
    if (!_is_compatible_discriminator(m__d, 4313302)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_getPackagesVerifyResult_Result>(m__u);
  }

  ::seres::ota_duc_service::DucServiceInterface_getPackagesVerifyResult_Result& getPackagesVerifyResultResult()
  {
    if (!_is_compatible_discriminator(m__d, 4313302)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_getPackagesVerifyResult_Result>(m__u);
  }

  void getPackagesVerifyResultResult(const ::seres::ota_duc_service::DucServiceInterface_getPackagesVerifyResult_Result& u, int32_t d = 4313302)
  {
    if (!_is_compatible_discriminator(4313302, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  void getPackagesVerifyResultResult(::seres::ota_duc_service::DucServiceInterface_getPackagesVerifyResult_Result&& u, int32_t d = 4313302)
  {
    if (!_is_compatible_discriminator(4313302, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  const ::seres::ota_duc_service::DucServiceInterface_checkUpdateCondition_Result &checkUpdateConditionResult() const
  {
    if (!_is_compatible_discriminator(m__d, 178527735)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_checkUpdateCondition_Result>(m__u);
  }

  ::seres::ota_duc_service::DucServiceInterface_checkUpdateCondition_Result& checkUpdateConditionResult()
  {
    if (!_is_compatible_discriminator(m__d, 178527735)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_checkUpdateCondition_Result>(m__u);
  }

  void checkUpdateConditionResult(const ::seres::ota_duc_service::DucServiceInterface_checkUpdateCondition_Result& u, int32_t d = 178527735)
  {
    if (!_is_compatible_discriminator(178527735, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  void checkUpdateConditionResult(::seres::ota_duc_service::DucServiceInterface_checkUpdateCondition_Result&& u, int32_t d = 178527735)
  {
    if (!_is_compatible_discriminator(178527735, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  const ::seres::ota_duc_service::DucServiceInterface_getCheckUpdateConditionResult_Result &getCheckUpdateConditionResultResult() const
  {
    if (!_is_compatible_discriminator(m__d, 200329384)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_getCheckUpdateConditionResult_Result>(m__u);
  }

  ::seres::ota_duc_service::DucServiceInterface_getCheckUpdateConditionResult_Result& getCheckUpdateConditionResultResult()
  {
    if (!_is_compatible_discriminator(m__d, 200329384)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_getCheckUpdateConditionResult_Result>(m__u);
  }

  void getCheckUpdateConditionResultResult(const ::seres::ota_duc_service::DucServiceInterface_getCheckUpdateConditionResult_Result& u, int32_t d = 200329384)
  {
    if (!_is_compatible_discriminator(200329384, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  void getCheckUpdateConditionResultResult(::seres::ota_duc_service::DucServiceInterface_getCheckUpdateConditionResult_Result&& u, int32_t d = 200329384)
  {
    if (!_is_compatible_discriminator(200329384, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  const ::seres::ota_duc_service::DucServiceInterface_startUpdate_Result &startUpdateResult() const
  {
    if (!_is_compatible_discriminator(m__d, 136901098)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_startUpdate_Result>(m__u);
  }

  ::seres::ota_duc_service::DucServiceInterface_startUpdate_Result& startUpdateResult()
  {
    if (!_is_compatible_discriminator(m__d, 136901098)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_startUpdate_Result>(m__u);
  }

  void startUpdateResult(const ::seres::ota_duc_service::DucServiceInterface_startUpdate_Result& u, int32_t d = 136901098)
  {
    if (!_is_compatible_discriminator(136901098, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  void startUpdateResult(::seres::ota_duc_service::DucServiceInterface_startUpdate_Result&& u, int32_t d = 136901098)
  {
    if (!_is_compatible_discriminator(136901098, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  const ::seres::ota_duc_service::DucServiceInterface_resumeUpdate_Result &resumeUpdateResult() const
  {
    if (!_is_compatible_discriminator(m__d, 29478526)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_resumeUpdate_Result>(m__u);
  }

  ::seres::ota_duc_service::DucServiceInterface_resumeUpdate_Result& resumeUpdateResult()
  {
    if (!_is_compatible_discriminator(m__d, 29478526)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_resumeUpdate_Result>(m__u);
  }

  void resumeUpdateResult(const ::seres::ota_duc_service::DucServiceInterface_resumeUpdate_Result& u, int32_t d = 29478526)
  {
    if (!_is_compatible_discriminator(29478526, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  void resumeUpdateResult(::seres::ota_duc_service::DucServiceInterface_resumeUpdate_Result&& u, int32_t d = 29478526)
  {
    if (!_is_compatible_discriminator(29478526, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  const ::seres::ota_duc_service::DucServiceInterface_pauseUpdate_Result &pauseUpdateResult() const
  {
    if (!_is_compatible_discriminator(m__d, 251480810)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_pauseUpdate_Result>(m__u);
  }

  ::seres::ota_duc_service::DucServiceInterface_pauseUpdate_Result& pauseUpdateResult()
  {
    if (!_is_compatible_discriminator(m__d, 251480810)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_pauseUpdate_Result>(m__u);
  }

  void pauseUpdateResult(const ::seres::ota_duc_service::DucServiceInterface_pauseUpdate_Result& u, int32_t d = 251480810)
  {
    if (!_is_compatible_discriminator(251480810, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  void pauseUpdateResult(::seres::ota_duc_service::DucServiceInterface_pauseUpdate_Result&& u, int32_t d = 251480810)
  {
    if (!_is_compatible_discriminator(251480810, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  const ::seres::ota_duc_service::DucServiceInterface_getUpdateProgress_Result &getUpdateProgressResult() const
  {
    if (!_is_compatible_discriminator(m__d, 66921313)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_getUpdateProgress_Result>(m__u);
  }

  ::seres::ota_duc_service::DucServiceInterface_getUpdateProgress_Result& getUpdateProgressResult()
  {
    if (!_is_compatible_discriminator(m__d, 66921313)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_getUpdateProgress_Result>(m__u);
  }

  void getUpdateProgressResult(const ::seres::ota_duc_service::DucServiceInterface_getUpdateProgress_Result& u, int32_t d = 66921313)
  {
    if (!_is_compatible_discriminator(66921313, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  void getUpdateProgressResult(::seres::ota_duc_service::DucServiceInterface_getUpdateProgress_Result&& u, int32_t d = 66921313)
  {
    if (!_is_compatible_discriminator(66921313, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  const ::seres::ota_duc_service::DucServiceInterface_activate_Result &activateResult() const
  {
    if (!_is_compatible_discriminator(m__d, 146417871)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_activate_Result>(m__u);
  }

  ::seres::ota_duc_service::DucServiceInterface_activate_Result& activateResult()
  {
    if (!_is_compatible_discriminator(m__d, 146417871)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_activate_Result>(m__u);
  }

  void activateResult(const ::seres::ota_duc_service::DucServiceInterface_activate_Result& u, int32_t d = 146417871)
  {
    if (!_is_compatible_discriminator(146417871, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  void activateResult(::seres::ota_duc_service::DucServiceInterface_activate_Result&& u, int32_t d = 146417871)
  {
    if (!_is_compatible_discriminator(146417871, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  const ::seres::ota_duc_service::DucServiceInterface_rollback_Result &rollbackResult() const
  {
    if (!_is_compatible_discriminator(m__d, 170403593)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_rollback_Result>(m__u);
  }

  ::seres::ota_duc_service::DucServiceInterface_rollback_Result& rollbackResult()
  {
    if (!_is_compatible_discriminator(m__d, 170403593)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_rollback_Result>(m__u);
  }

  void rollbackResult(const ::seres::ota_duc_service::DucServiceInterface_rollback_Result& u, int32_t d = 170403593)
  {
    if (!_is_compatible_discriminator(170403593, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  void rollbackResult(::seres::ota_duc_service::DucServiceInterface_rollback_Result&& u, int32_t d = 170403593)
  {
    if (!_is_compatible_discriminator(170403593, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  const ::seres::ota_duc_service::DucServiceInterface_getRollbackProgress_Result &getRollbackProgressResult() const
  {
    if (!_is_compatible_discriminator(m__d, 31250426)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_getRollbackProgress_Result>(m__u);
  }

  ::seres::ota_duc_service::DucServiceInterface_getRollbackProgress_Result& getRollbackProgressResult()
  {
    if (!_is_compatible_discriminator(m__d, 31250426)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_getRollbackProgress_Result>(m__u);
  }

  void getRollbackProgressResult(const ::seres::ota_duc_service::DucServiceInterface_getRollbackProgress_Result& u, int32_t d = 31250426)
  {
    if (!_is_compatible_discriminator(31250426, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  void getRollbackProgressResult(::seres::ota_duc_service::DucServiceInterface_getRollbackProgress_Result&& u, int32_t d = 31250426)
  {
    if (!_is_compatible_discriminator(31250426, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  const ::seres::ota_duc_service::DucServiceInterface_uploadLog_Result &uploadLogResult() const
  {
    if (!_is_compatible_discriminator(m__d, 126211184)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_uploadLog_Result>(m__u);
  }

  ::seres::ota_duc_service::DucServiceInterface_uploadLog_Result& uploadLogResult()
  {
    if (!_is_compatible_discriminator(m__d, 126211184)) {
      throw dds::core::InvalidArgumentError(
        "Requested branch does not match current discriminator");
    }
    return std::get<::seres::ota_duc_service::DucServiceInterface_uploadLog_Result>(m__u);
  }

  void uploadLogResult(const ::seres::ota_duc_service::DucServiceInterface_uploadLog_Result& u, int32_t d = 126211184)
  {
    if (!_is_compatible_discriminator(126211184, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  void uploadLogResult(::seres::ota_duc_service::DucServiceInterface_uploadLog_Result&& u, int32_t d = 126211184)
  {
    if (!_is_compatible_discriminator(126211184, d)) {
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match current discriminator");
    }
    m__d = d;
    m__u = u;
  }

  bool operator==(const DucServiceInterface_Return& _other) const
  {
    if (_d() != _other._d()) return false;
    switch (_d()) {
      case 202679978:
        return inventoryCollectionResult() == _other.inventoryCollectionResult();
      case 23721455:
        return stopInventoryCollectionResult() == _other.stopInventoryCollectionResult();
      case 207766764:
        return getInventoryResultResult() == _other.getInventoryResultResult();
      case 7343798:
        return checkDownloadConditionResult() == _other.checkDownloadConditionResult();
      case 38715577:
        return startDownloadResult() == _other.startDownloadResult();
      case 33876474:
        return downloadCtrlResult() == _other.downloadCtrlResult();
      case 173346514:
        return getDownloadProgressResult() == _other.getDownloadProgressResult();
      case 113763844:
        return uzipPackagesResult() == _other.uzipPackagesResult();
      case 256795369:
        return getuzipPackagesResultResult() == _other.getuzipPackagesResultResult();
      case 194734422:
        return startPackagesVerifyResult() == _other.startPackagesVerifyResult();
      case 4313302:
        return getPackagesVerifyResultResult() == _other.getPackagesVerifyResultResult();
      case 178527735:
        return checkUpdateConditionResult() == _other.checkUpdateConditionResult();
      case 200329384:
        return getCheckUpdateConditionResultResult() == _other.getCheckUpdateConditionResultResult();
      case 136901098:
        return startUpdateResult() == _other.startUpdateResult();
      case 29478526:
        return resumeUpdateResult() == _other.resumeUpdateResult();
      case 251480810:
        return pauseUpdateResult() == _other.pauseUpdateResult();
      case 66921313:
        return getUpdateProgressResult() == _other.getUpdateProgressResult();
      case 146417871:
        return activateResult() == _other.activateResult();
      case 170403593:
        return rollbackResult() == _other.rollbackResult();
      case 31250426:
        return getRollbackProgressResult() == _other.getRollbackProgressResult();
      case 126211184:
        return uploadLogResult() == _other.uploadLogResult();
      default:
        return true;
    }
    return true;
  }

  bool operator!=(const DucServiceInterface_Return& _other) const
  {
    return !(*this == _other);
  }

  void _default(int32_t d = 0)
  {
    if (!_is_compatible_discriminator(d, 0))
      throw dds::core::InvalidArgumentError(
        "Discriminator does not match default branch");
    m__d = d;
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_Return const& rhs);
class DucServiceInterface_Reply
{
private:
 ::dds::rpc::ReplyHeader header_;
 ::seres::ota_duc_service::DucServiceInterface_Return data_;

public:
  DucServiceInterface_Reply() = default;

  explicit DucServiceInterface_Reply(
    const ::dds::rpc::ReplyHeader& header,
    const ::seres::ota_duc_service::DucServiceInterface_Return& data) :
    header_(header),
    data_(data) { }

  const ::dds::rpc::ReplyHeader& header() const { return this->header_; }
  ::dds::rpc::ReplyHeader& header() { return this->header_; }
  void header(const ::dds::rpc::ReplyHeader& _val_) { this->header_ = _val_; }
  void header(::dds::rpc::ReplyHeader&& _val_) { this->header_ = std::move(_val_); }
  const ::seres::ota_duc_service::DucServiceInterface_Return& data() const { return this->data_; }
  ::seres::ota_duc_service::DucServiceInterface_Return& data() { return this->data_; }
  void data(const ::seres::ota_duc_service::DucServiceInterface_Return& _val_) { this->data_ = _val_; }
  void data(::seres::ota_duc_service::DucServiceInterface_Return&& _val_) { this->data_ = std::move(_val_); }

  bool operator==(const DucServiceInterface_Reply& _other) const
  {
    (void) _other;
    return header_ == _other.header_ &&
      data_ == _other.data_;
  }

  bool operator!=(const DucServiceInterface_Reply& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, DucServiceInterface_Reply const& rhs);

} //namespace ota_duc_service

} //namespace seres

#include "dds/topic/TopicTraits.hpp"
#include "org/eclipse/cyclonedds/topic/datatopic.hpp"

namespace org {
namespace eclipse {
namespace cyclonedds {
namespace topic {

template <> constexpr const char* TopicTraits<::seres::ota_duc_service::DucServiceInterface_Request>::getTypeName()
{
  return "seres::ota_duc_service::DucServiceInterface_Request";
}

template <> constexpr bool TopicTraits<::seres::ota_duc_service::DucServiceInterface_Request>::isSelfContained()
{
  return false;
}

template <> constexpr bool TopicTraits<::seres::ota_duc_service::DucServiceInterface_Request>::isKeyless()
{
  return true;
}

template <> constexpr const char* TopicTraits<::seres::ota_duc_service::DucServiceInterface_Reply>::getTypeName()
{
  return "seres::ota_duc_service::DucServiceInterface_Reply";
}

template <> constexpr bool TopicTraits<::seres::ota_duc_service::DucServiceInterface_Reply>::isSelfContained()
{
  return false;
}

template <> constexpr bool TopicTraits<::seres::ota_duc_service::DucServiceInterface_Reply>::isKeyless()
{
  return true;
}

} //namespace topic
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

namespace dds {
namespace topic {

template <>
struct topic_type_name<::seres::ota_duc_service::DucServiceInterface_Request>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::seres::ota_duc_service::DucServiceInterface_Request>::getTypeName();
    }
};

template <>
struct topic_type_name<::seres::ota_duc_service::DucServiceInterface_Reply>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::seres::ota_duc_service::DucServiceInterface_Reply>::getTypeName();
    }
};

}
}

REGISTER_TOPIC_TYPE(::seres::ota_duc_service::DucServiceInterface_Request)
REGISTER_TOPIC_TYPE(::seres::ota_duc_service::DucServiceInterface_Reply)

namespace org{
namespace eclipse{
namespace cyclonedds{
namespace core{
namespace cdr{

template<>
::seres::ota_duc_service::ReturnCode enum_conversion<::seres::ota_duc_service::ReturnCode>(uint32_t in);

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_inventoryCollection_In>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_inventoryCollection_In = get_type_props<::seres::ota_duc_service::DucServiceInterface_inventoryCollection_In>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_inventoryCollection_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.inventory_list(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_inventoryCollection_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.inventory_list(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_inventoryCollection_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.inventory_list(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_inventoryCollection_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.inventory_list(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_stopInventoryCollection_In>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_stopInventoryCollection_In = get_type_props<::seres::ota_duc_service::DucServiceInterface_stopInventoryCollection_In>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_stopInventoryCollection_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_stopInventoryCollection_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_stopInventoryCollection_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_stopInventoryCollection_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_getInventoryResult_In>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_getInventoryResult_In = get_type_props<::seres::ota_duc_service::DucServiceInterface_getInventoryResult_In>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getInventoryResult_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_getInventoryResult_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getInventoryResult_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getInventoryResult_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_checkDownloadCondition_In>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_checkDownloadCondition_In = get_type_props<::seres::ota_duc_service::DucServiceInterface_checkDownloadCondition_In>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_checkDownloadCondition_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.conditions(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_checkDownloadCondition_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.conditions(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_checkDownloadCondition_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.conditions(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_checkDownloadCondition_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.conditions(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_startDownload_In>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_startDownload_In = get_type_props<::seres::ota_duc_service::DucServiceInterface_startDownload_In>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_startDownload_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.task_list(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_startDownload_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.task_list(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_startDownload_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.task_list(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_startDownload_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.task_list(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_downloadCtrl_In>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_downloadCtrl_In = get_type_props<::seres::ota_duc_service::DucServiceInterface_downloadCtrl_In>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_downloadCtrl_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.download_command()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_downloadCtrl_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.download_command()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_downloadCtrl_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.download_command()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_downloadCtrl_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.download_command()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_getDownloadProgress_In>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_getDownloadProgress_In = get_type_props<::seres::ota_duc_service::DucServiceInterface_getDownloadProgress_In>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getDownloadProgress_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_getDownloadProgress_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getDownloadProgress_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getDownloadProgress_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_uzipPackages_In>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_uzipPackages_In = get_type_props<::seres::ota_duc_service::DucServiceInterface_uzipPackages_In>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_uzipPackages_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_uzipPackages_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_uzipPackages_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_uzipPackages_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_getuzipPackagesResult_In>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_getuzipPackagesResult_In = get_type_props<::seres::ota_duc_service::DucServiceInterface_getuzipPackagesResult_In>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getuzipPackagesResult_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_getuzipPackagesResult_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getuzipPackagesResult_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getuzipPackagesResult_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_startPackagesVerify_In>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_startPackagesVerify_In = get_type_props<::seres::ota_duc_service::DucServiceInterface_startPackagesVerify_In>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_startPackagesVerify_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_startPackagesVerify_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_startPackagesVerify_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_startPackagesVerify_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_getPackagesVerifyResult_In>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_getPackagesVerifyResult_In = get_type_props<::seres::ota_duc_service::DucServiceInterface_getPackagesVerifyResult_In>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getPackagesVerifyResult_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_getPackagesVerifyResult_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getPackagesVerifyResult_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getPackagesVerifyResult_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_checkUpdateCondition_In>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_checkUpdateCondition_In = get_type_props<::seres::ota_duc_service::DucServiceInterface_checkUpdateCondition_In>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_checkUpdateCondition_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_checkUpdateCondition_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_checkUpdateCondition_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_checkUpdateCondition_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_getCheckUpdateConditionResult_In>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_getCheckUpdateConditionResult_In = get_type_props<::seres::ota_duc_service::DucServiceInterface_getCheckUpdateConditionResult_In>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getCheckUpdateConditionResult_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_getCheckUpdateConditionResult_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getCheckUpdateConditionResult_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getCheckUpdateConditionResult_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_startUpdate_In>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_startUpdate_In = get_type_props<::seres::ota_duc_service::DucServiceInterface_startUpdate_In>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_startUpdate_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.mode()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.update_list(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_startUpdate_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.mode()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.update_list(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_startUpdate_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.mode()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.update_list(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_startUpdate_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.mode()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.update_list(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_resumeUpdate_In>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_resumeUpdate_In = get_type_props<::seres::ota_duc_service::DucServiceInterface_resumeUpdate_In>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_resumeUpdate_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_resumeUpdate_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_resumeUpdate_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_resumeUpdate_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_pauseUpdate_In>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_pauseUpdate_In = get_type_props<::seres::ota_duc_service::DucServiceInterface_pauseUpdate_In>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_pauseUpdate_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_pauseUpdate_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_pauseUpdate_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_pauseUpdate_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_getUpdateProgress_In>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_getUpdateProgress_In = get_type_props<::seres::ota_duc_service::DucServiceInterface_getUpdateProgress_In>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getUpdateProgress_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_getUpdateProgress_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getUpdateProgress_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getUpdateProgress_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_activate_In>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_activate_In = get_type_props<::seres::ota_duc_service::DucServiceInterface_activate_In>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_activate_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_activate_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_activate_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_activate_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_rollback_In>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_rollback_In = get_type_props<::seres::ota_duc_service::DucServiceInterface_rollback_In>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_rollback_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.component_list(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_rollback_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.component_list(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_rollback_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.component_list(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_rollback_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.component_list(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_getRollbackProgress_In>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_getRollbackProgress_In = get_type_props<::seres::ota_duc_service::DucServiceInterface_getRollbackProgress_In>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getRollbackProgress_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_getRollbackProgress_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getRollbackProgress_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getRollbackProgress_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_uploadLog_In>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_uploadLog_In = get_type_props<::seres::ota_duc_service::DucServiceInterface_uploadLog_In>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_uploadLog_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_uploadLog_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_uploadLog_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_uploadLog_In& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_inventoryCollection_Out>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_inventoryCollection_Out = get_type_props<::seres::ota_duc_service::DucServiceInterface_inventoryCollection_Out>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_inventoryCollection_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_inventoryCollection_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_inventoryCollection_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_inventoryCollection_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_inventoryCollection_Result>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_inventoryCollection_Result = get_type_props<::seres::ota_duc_service::DucServiceInterface_inventoryCollection_Result>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_inventoryCollection_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.inventoryCollectionOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_inventoryCollection_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.inventoryCollectionOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_inventoryCollection_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.inventoryCollectionOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_inventoryCollection_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.inventoryCollectionOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_stopInventoryCollection_Out>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_stopInventoryCollection_Out = get_type_props<::seres::ota_duc_service::DucServiceInterface_stopInventoryCollection_Out>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_stopInventoryCollection_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_stopInventoryCollection_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_stopInventoryCollection_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_stopInventoryCollection_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_stopInventoryCollection_Result>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_stopInventoryCollection_Result = get_type_props<::seres::ota_duc_service::DucServiceInterface_stopInventoryCollection_Result>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_stopInventoryCollection_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.stopInventoryCollectionOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_stopInventoryCollection_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.stopInventoryCollectionOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_stopInventoryCollection_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.stopInventoryCollectionOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_stopInventoryCollection_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.stopInventoryCollectionOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_getInventoryResult_Out>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_getInventoryResult_Out = get_type_props<::seres::ota_duc_service::DucServiceInterface_getInventoryResult_Out>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getInventoryResult_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.inventory_list(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_getInventoryResult_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.inventory_list(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getInventoryResult_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.inventory_list(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getInventoryResult_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.inventory_list(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_getInventoryResult_Result>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_getInventoryResult_Result = get_type_props<::seres::ota_duc_service::DucServiceInterface_getInventoryResult_Result>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getInventoryResult_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.getInventoryResultOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_getInventoryResult_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.getInventoryResultOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getInventoryResult_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.getInventoryResultOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getInventoryResult_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.getInventoryResultOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_checkDownloadCondition_Out>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_checkDownloadCondition_Out = get_type_props<::seres::ota_duc_service::DucServiceInterface_checkDownloadCondition_Out>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_checkDownloadCondition_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.condition_result()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_checkDownloadCondition_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.condition_result()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_checkDownloadCondition_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.condition_result()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_checkDownloadCondition_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.condition_result()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_checkDownloadCondition_Result>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_checkDownloadCondition_Result = get_type_props<::seres::ota_duc_service::DucServiceInterface_checkDownloadCondition_Result>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_checkDownloadCondition_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.checkDownloadConditionOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_checkDownloadCondition_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.checkDownloadConditionOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_checkDownloadCondition_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.checkDownloadConditionOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_checkDownloadCondition_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.checkDownloadConditionOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_startDownload_Out>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_startDownload_Out = get_type_props<::seres::ota_duc_service::DucServiceInterface_startDownload_Out>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_startDownload_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_startDownload_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_startDownload_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_startDownload_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_startDownload_Result>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_startDownload_Result = get_type_props<::seres::ota_duc_service::DucServiceInterface_startDownload_Result>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_startDownload_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.startDownloadOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_startDownload_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.startDownloadOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_startDownload_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.startDownloadOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_startDownload_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.startDownloadOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_downloadCtrl_Out>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_downloadCtrl_Out = get_type_props<::seres::ota_duc_service::DucServiceInterface_downloadCtrl_Out>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_downloadCtrl_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_downloadCtrl_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_downloadCtrl_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_downloadCtrl_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_downloadCtrl_Result>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_downloadCtrl_Result = get_type_props<::seres::ota_duc_service::DucServiceInterface_downloadCtrl_Result>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_downloadCtrl_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.downloadCtrlOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_downloadCtrl_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.downloadCtrlOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_downloadCtrl_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.downloadCtrlOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_downloadCtrl_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.downloadCtrlOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_getDownloadProgress_Out>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_getDownloadProgress_Out = get_type_props<::seres::ota_duc_service::DucServiceInterface_getDownloadProgress_Out>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getDownloadProgress_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.download_progress(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_getDownloadProgress_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.download_progress(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getDownloadProgress_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.download_progress(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getDownloadProgress_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.download_progress(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_getDownloadProgress_Result>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_getDownloadProgress_Result = get_type_props<::seres::ota_duc_service::DucServiceInterface_getDownloadProgress_Result>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getDownloadProgress_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.getDownloadProgressOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_getDownloadProgress_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.getDownloadProgressOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getDownloadProgress_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.getDownloadProgressOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getDownloadProgress_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.getDownloadProgressOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_uzipPackages_Out>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_uzipPackages_Out = get_type_props<::seres::ota_duc_service::DucServiceInterface_uzipPackages_Out>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_uzipPackages_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_uzipPackages_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_uzipPackages_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_uzipPackages_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_uzipPackages_Result>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_uzipPackages_Result = get_type_props<::seres::ota_duc_service::DucServiceInterface_uzipPackages_Result>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_uzipPackages_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.uzipPackagesOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_uzipPackages_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.uzipPackagesOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_uzipPackages_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.uzipPackagesOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_uzipPackages_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.uzipPackagesOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_getuzipPackagesResult_Out>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_getuzipPackagesResult_Out = get_type_props<::seres::ota_duc_service::DucServiceInterface_getuzipPackagesResult_Out>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getuzipPackagesResult_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.uzip_Result(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_getuzipPackagesResult_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.uzip_Result(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getuzipPackagesResult_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.uzip_Result(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getuzipPackagesResult_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.uzip_Result(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_getuzipPackagesResult_Result>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_getuzipPackagesResult_Result = get_type_props<::seres::ota_duc_service::DucServiceInterface_getuzipPackagesResult_Result>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getuzipPackagesResult_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.getuzipPackagesResultOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_getuzipPackagesResult_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.getuzipPackagesResultOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getuzipPackagesResult_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.getuzipPackagesResultOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getuzipPackagesResult_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.getuzipPackagesResultOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_startPackagesVerify_Out>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_startPackagesVerify_Out = get_type_props<::seres::ota_duc_service::DucServiceInterface_startPackagesVerify_Out>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_startPackagesVerify_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_startPackagesVerify_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_startPackagesVerify_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_startPackagesVerify_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_startPackagesVerify_Result>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_startPackagesVerify_Result = get_type_props<::seres::ota_duc_service::DucServiceInterface_startPackagesVerify_Result>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_startPackagesVerify_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.startPackagesVerifyOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_startPackagesVerify_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.startPackagesVerifyOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_startPackagesVerify_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.startPackagesVerifyOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_startPackagesVerify_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.startPackagesVerifyOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_getPackagesVerifyResult_Out>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_getPackagesVerifyResult_Out = get_type_props<::seres::ota_duc_service::DucServiceInterface_getPackagesVerifyResult_Out>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getPackagesVerifyResult_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.verify_Result(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_getPackagesVerifyResult_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.verify_Result(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getPackagesVerifyResult_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.verify_Result(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getPackagesVerifyResult_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.verify_Result(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_getPackagesVerifyResult_Result>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_getPackagesVerifyResult_Result = get_type_props<::seres::ota_duc_service::DucServiceInterface_getPackagesVerifyResult_Result>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getPackagesVerifyResult_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.getPackagesVerifyResultOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_getPackagesVerifyResult_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.getPackagesVerifyResultOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getPackagesVerifyResult_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.getPackagesVerifyResultOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getPackagesVerifyResult_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.getPackagesVerifyResultOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_checkUpdateCondition_Out>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_checkUpdateCondition_Out = get_type_props<::seres::ota_duc_service::DucServiceInterface_checkUpdateCondition_Out>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_checkUpdateCondition_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_checkUpdateCondition_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_checkUpdateCondition_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_checkUpdateCondition_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_checkUpdateCondition_Result>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_checkUpdateCondition_Result = get_type_props<::seres::ota_duc_service::DucServiceInterface_checkUpdateCondition_Result>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_checkUpdateCondition_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.checkUpdateConditionOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_checkUpdateCondition_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.checkUpdateConditionOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_checkUpdateCondition_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.checkUpdateConditionOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_checkUpdateCondition_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.checkUpdateConditionOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_getCheckUpdateConditionResult_Out>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_getCheckUpdateConditionResult_Out = get_type_props<::seres::ota_duc_service::DucServiceInterface_getCheckUpdateConditionResult_Out>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getCheckUpdateConditionResult_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.checkcondition_Result(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_getCheckUpdateConditionResult_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.checkcondition_Result(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getCheckUpdateConditionResult_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.checkcondition_Result(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getCheckUpdateConditionResult_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.checkcondition_Result(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_getCheckUpdateConditionResult_Result>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_getCheckUpdateConditionResult_Result = get_type_props<::seres::ota_duc_service::DucServiceInterface_getCheckUpdateConditionResult_Result>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getCheckUpdateConditionResult_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.getCheckUpdateConditionResultOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_getCheckUpdateConditionResult_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.getCheckUpdateConditionResultOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getCheckUpdateConditionResult_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.getCheckUpdateConditionResultOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getCheckUpdateConditionResult_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.getCheckUpdateConditionResultOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_startUpdate_Out>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_startUpdate_Out = get_type_props<::seres::ota_duc_service::DucServiceInterface_startUpdate_Out>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_startUpdate_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_startUpdate_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_startUpdate_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_startUpdate_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_startUpdate_Result>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_startUpdate_Result = get_type_props<::seres::ota_duc_service::DucServiceInterface_startUpdate_Result>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_startUpdate_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.startUpdateOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_startUpdate_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.startUpdateOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_startUpdate_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.startUpdateOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_startUpdate_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.startUpdateOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_resumeUpdate_Out>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_resumeUpdate_Out = get_type_props<::seres::ota_duc_service::DucServiceInterface_resumeUpdate_Out>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_resumeUpdate_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_resumeUpdate_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_resumeUpdate_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_resumeUpdate_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_resumeUpdate_Result>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_resumeUpdate_Result = get_type_props<::seres::ota_duc_service::DucServiceInterface_resumeUpdate_Result>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_resumeUpdate_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.resumeUpdateOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_resumeUpdate_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.resumeUpdateOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_resumeUpdate_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.resumeUpdateOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_resumeUpdate_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.resumeUpdateOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_pauseUpdate_Out>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_pauseUpdate_Out = get_type_props<::seres::ota_duc_service::DucServiceInterface_pauseUpdate_Out>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_pauseUpdate_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_pauseUpdate_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_pauseUpdate_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_pauseUpdate_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_pauseUpdate_Result>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_pauseUpdate_Result = get_type_props<::seres::ota_duc_service::DucServiceInterface_pauseUpdate_Result>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_pauseUpdate_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.pauseUpdateOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_pauseUpdate_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.pauseUpdateOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_pauseUpdate_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.pauseUpdateOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_pauseUpdate_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.pauseUpdateOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_getUpdateProgress_Out>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_getUpdateProgress_Out = get_type_props<::seres::ota_duc_service::DucServiceInterface_getUpdateProgress_Out>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getUpdateProgress_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.update_progress(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_getUpdateProgress_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.update_progress(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getUpdateProgress_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.update_progress(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getUpdateProgress_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.update_progress(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_getUpdateProgress_Result>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_getUpdateProgress_Result = get_type_props<::seres::ota_duc_service::DucServiceInterface_getUpdateProgress_Result>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getUpdateProgress_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.getUpdateProgressOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_getUpdateProgress_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.getUpdateProgressOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getUpdateProgress_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.getUpdateProgressOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getUpdateProgress_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.getUpdateProgressOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_activate_Out>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_activate_Out = get_type_props<::seres::ota_duc_service::DucServiceInterface_activate_Out>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_activate_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_activate_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_activate_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_activate_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_activate_Result>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_activate_Result = get_type_props<::seres::ota_duc_service::DucServiceInterface_activate_Result>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_activate_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.activateOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_activate_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.activateOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_activate_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.activateOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_activate_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.activateOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_rollback_Out>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_rollback_Out = get_type_props<::seres::ota_duc_service::DucServiceInterface_rollback_Out>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_rollback_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_rollback_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_rollback_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_rollback_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_rollback_Result>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_rollback_Result = get_type_props<::seres::ota_duc_service::DucServiceInterface_rollback_Result>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_rollback_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.rollbackOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_rollback_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.rollbackOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_rollback_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.rollbackOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_rollback_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.rollbackOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_getRollbackProgress_Out>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_getRollbackProgress_Out = get_type_props<::seres::ota_duc_service::DucServiceInterface_getRollbackProgress_Out>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getRollbackProgress_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.update_progress(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_getRollbackProgress_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.update_progress(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getRollbackProgress_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.update_progress(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getRollbackProgress_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.update_progress(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_getRollbackProgress_Result>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_getRollbackProgress_Result = get_type_props<::seres::ota_duc_service::DucServiceInterface_getRollbackProgress_Result>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getRollbackProgress_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.getRollbackProgressOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_getRollbackProgress_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.getRollbackProgressOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getRollbackProgress_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.getRollbackProgressOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_getRollbackProgress_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.getRollbackProgressOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_uploadLog_Out>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_uploadLog_Out = get_type_props<::seres::ota_duc_service::DucServiceInterface_uploadLog_Out>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_uploadLog_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_uploadLog_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_uploadLog_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_uploadLog_Out& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance._default()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_uploadLog_Result>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_uploadLog_Result = get_type_props<::seres::ota_duc_service::DucServiceInterface_uploadLog_Result>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_uploadLog_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.uploadLogOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_uploadLog_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.uploadLogOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_uploadLog_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.uploadLogOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_uploadLog_Result& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.uploadLogOut(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance._return()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_Call>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_Call = get_type_props<::seres::ota_duc_service::DucServiceInterface_Call>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_Call& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  const auto d = instance._d();
  if (!write(streamer, d))
    return false;
  switch(d)
  {
    case 202679978:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_inventoryCollection_In>()[0]);
      if (!write(streamer, instance.inventoryCollectionIn(), prop))
        return false;
      }
      break;
    case 23721455:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_stopInventoryCollection_In>()[0]);
      if (!write(streamer, instance.stopInventoryCollectionIn(), prop))
        return false;
      }
      break;
    case 207766764:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_getInventoryResult_In>()[0]);
      if (!write(streamer, instance.getInventoryResultIn(), prop))
        return false;
      }
      break;
    case 7343798:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_checkDownloadCondition_In>()[0]);
      if (!write(streamer, instance.checkDownloadConditionIn(), prop))
        return false;
      }
      break;
    case 38715577:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_startDownload_In>()[0]);
      if (!write(streamer, instance.startDownloadIn(), prop))
        return false;
      }
      break;
    case 33876474:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_downloadCtrl_In>()[0]);
      if (!write(streamer, instance.downloadCtrlIn(), prop))
        return false;
      }
      break;
    case 173346514:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_getDownloadProgress_In>()[0]);
      if (!write(streamer, instance.getDownloadProgressIn(), prop))
        return false;
      }
      break;
    case 113763844:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_uzipPackages_In>()[0]);
      if (!write(streamer, instance.uzipPackagesIn(), prop))
        return false;
      }
      break;
    case 256795369:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_getuzipPackagesResult_In>()[0]);
      if (!write(streamer, instance.getuzipPackagesResultIn(), prop))
        return false;
      }
      break;
    case 194734422:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_startPackagesVerify_In>()[0]);
      if (!write(streamer, instance.startPackagesVerifyIn(), prop))
        return false;
      }
      break;
    case 4313302:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_getPackagesVerifyResult_In>()[0]);
      if (!write(streamer, instance.getPackagesVerifyResultIn(), prop))
        return false;
      }
      break;
    case 178527735:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_checkUpdateCondition_In>()[0]);
      if (!write(streamer, instance.checkUpdateConditionIn(), prop))
        return false;
      }
      break;
    case 200329384:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_getCheckUpdateConditionResult_In>()[0]);
      if (!write(streamer, instance.getCheckUpdateConditionResultIn(), prop))
        return false;
      }
      break;
    case 136901098:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_startUpdate_In>()[0]);
      if (!write(streamer, instance.startUpdateIn(), prop))
        return false;
      }
      break;
    case 29478526:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_resumeUpdate_In>()[0]);
      if (!write(streamer, instance.resumeUpdateIn(), prop))
        return false;
      }
      break;
    case 251480810:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_pauseUpdate_In>()[0]);
      if (!write(streamer, instance.pauseUpdateIn(), prop))
        return false;
      }
      break;
    case 66921313:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_getUpdateProgress_In>()[0]);
      if (!write(streamer, instance.getUpdateProgressIn(), prop))
        return false;
      }
      break;
    case 146417871:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_activate_In>()[0]);
      if (!write(streamer, instance.activateIn(), prop))
        return false;
      }
      break;
    case 170403593:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_rollback_In>()[0]);
      if (!write(streamer, instance.rollbackIn(), prop))
        return false;
      }
      break;
    case 31250426:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_getRollbackProgress_In>()[0]);
      if (!write(streamer, instance.getRollbackProgressIn(), prop))
        return false;
      }
      break;
    case 126211184:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_uploadLog_In>()[0]);
      if (!write(streamer, instance.uploadLogIn(), prop))
        return false;
      }
      break;
    default:
      break;
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_Call& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto d = instance._d();
  if (!read(streamer, d))
    return false;
  switch(d)
  {
    case 202679978:
    {
      auto obj = decl_ref_type(instance.inventoryCollectionIn())();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_inventoryCollection_In>()[0]);
      if (!read(streamer, obj, prop))
        return false;
      instance.inventoryCollectionIn(obj);
    }
    break;
    case 23721455:
    {
      auto obj = decl_ref_type(instance.stopInventoryCollectionIn())();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_stopInventoryCollection_In>()[0]);
      if (!read(streamer, obj, prop))
        return false;
      instance.stopInventoryCollectionIn(obj);
    }
    break;
    case 207766764:
    {
      auto obj = decl_ref_type(instance.getInventoryResultIn())();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_getInventoryResult_In>()[0]);
      if (!read(streamer, obj, prop))
        return false;
      instance.getInventoryResultIn(obj);
    }
    break;
    case 7343798:
    {
      auto obj = decl_ref_type(instance.checkDownloadConditionIn())();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_checkDownloadCondition_In>()[0]);
      if (!read(streamer, obj, prop))
        return false;
      instance.checkDownloadConditionIn(obj);
    }
    break;
    case 38715577:
    {
      auto obj = decl_ref_type(instance.startDownloadIn())();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_startDownload_In>()[0]);
      if (!read(streamer, obj, prop))
        return false;
      instance.startDownloadIn(obj);
    }
    break;
    case 33876474:
    {
      auto obj = decl_ref_type(instance.downloadCtrlIn())();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_downloadCtrl_In>()[0]);
      if (!read(streamer, obj, prop))
        return false;
      instance.downloadCtrlIn(obj);
    }
    break;
    case 173346514:
    {
      auto obj = decl_ref_type(instance.getDownloadProgressIn())();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_getDownloadProgress_In>()[0]);
      if (!read(streamer, obj, prop))
        return false;
      instance.getDownloadProgressIn(obj);
    }
    break;
    case 113763844:
    {
      auto obj = decl_ref_type(instance.uzipPackagesIn())();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_uzipPackages_In>()[0]);
      if (!read(streamer, obj, prop))
        return false;
      instance.uzipPackagesIn(obj);
    }
    break;
    case 256795369:
    {
      auto obj = decl_ref_type(instance.getuzipPackagesResultIn())();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_getuzipPackagesResult_In>()[0]);
      if (!read(streamer, obj, prop))
        return false;
      instance.getuzipPackagesResultIn(obj);
    }
    break;
    case 194734422:
    {
      auto obj = decl_ref_type(instance.startPackagesVerifyIn())();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_startPackagesVerify_In>()[0]);
      if (!read(streamer, obj, prop))
        return false;
      instance.startPackagesVerifyIn(obj);
    }
    break;
    case 4313302:
    {
      auto obj = decl_ref_type(instance.getPackagesVerifyResultIn())();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_getPackagesVerifyResult_In>()[0]);
      if (!read(streamer, obj, prop))
        return false;
      instance.getPackagesVerifyResultIn(obj);
    }
    break;
    case 178527735:
    {
      auto obj = decl_ref_type(instance.checkUpdateConditionIn())();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_checkUpdateCondition_In>()[0]);
      if (!read(streamer, obj, prop))
        return false;
      instance.checkUpdateConditionIn(obj);
    }
    break;
    case 200329384:
    {
      auto obj = decl_ref_type(instance.getCheckUpdateConditionResultIn())();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_getCheckUpdateConditionResult_In>()[0]);
      if (!read(streamer, obj, prop))
        return false;
      instance.getCheckUpdateConditionResultIn(obj);
    }
    break;
    case 136901098:
    {
      auto obj = decl_ref_type(instance.startUpdateIn())();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_startUpdate_In>()[0]);
      if (!read(streamer, obj, prop))
        return false;
      instance.startUpdateIn(obj);
    }
    break;
    case 29478526:
    {
      auto obj = decl_ref_type(instance.resumeUpdateIn())();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_resumeUpdate_In>()[0]);
      if (!read(streamer, obj, prop))
        return false;
      instance.resumeUpdateIn(obj);
    }
    break;
    case 251480810:
    {
      auto obj = decl_ref_type(instance.pauseUpdateIn())();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_pauseUpdate_In>()[0]);
      if (!read(streamer, obj, prop))
        return false;
      instance.pauseUpdateIn(obj);
    }
    break;
    case 66921313:
    {
      auto obj = decl_ref_type(instance.getUpdateProgressIn())();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_getUpdateProgress_In>()[0]);
      if (!read(streamer, obj, prop))
        return false;
      instance.getUpdateProgressIn(obj);
    }
    break;
    case 146417871:
    {
      auto obj = decl_ref_type(instance.activateIn())();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_activate_In>()[0]);
      if (!read(streamer, obj, prop))
        return false;
      instance.activateIn(obj);
    }
    break;
    case 170403593:
    {
      auto obj = decl_ref_type(instance.rollbackIn())();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_rollback_In>()[0]);
      if (!read(streamer, obj, prop))
        return false;
      instance.rollbackIn(obj);
    }
    break;
    case 31250426:
    {
      auto obj = decl_ref_type(instance.getRollbackProgressIn())();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_getRollbackProgress_In>()[0]);
      if (!read(streamer, obj, prop))
        return false;
      instance.getRollbackProgressIn(obj);
    }
    break;
    case 126211184:
    {
      auto obj = decl_ref_type(instance.uploadLogIn())();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_uploadLog_In>()[0]);
      if (!read(streamer, obj, prop))
        return false;
      instance.uploadLogIn(obj);
    }
    break;
    default:
      instance._d(d);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_Call& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  const auto d = instance._d();
  if (!move(streamer, d))
    return false;
  switch(d)
  {
    case 202679978:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_inventoryCollection_In>()[0]);
      if (!move(streamer, instance.inventoryCollectionIn(), prop))
        return false;
      }
      break;
    case 23721455:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_stopInventoryCollection_In>()[0]);
      if (!move(streamer, instance.stopInventoryCollectionIn(), prop))
        return false;
      }
      break;
    case 207766764:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_getInventoryResult_In>()[0]);
      if (!move(streamer, instance.getInventoryResultIn(), prop))
        return false;
      }
      break;
    case 7343798:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_checkDownloadCondition_In>()[0]);
      if (!move(streamer, instance.checkDownloadConditionIn(), prop))
        return false;
      }
      break;
    case 38715577:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_startDownload_In>()[0]);
      if (!move(streamer, instance.startDownloadIn(), prop))
        return false;
      }
      break;
    case 33876474:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_downloadCtrl_In>()[0]);
      if (!move(streamer, instance.downloadCtrlIn(), prop))
        return false;
      }
      break;
    case 173346514:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_getDownloadProgress_In>()[0]);
      if (!move(streamer, instance.getDownloadProgressIn(), prop))
        return false;
      }
      break;
    case 113763844:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_uzipPackages_In>()[0]);
      if (!move(streamer, instance.uzipPackagesIn(), prop))
        return false;
      }
      break;
    case 256795369:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_getuzipPackagesResult_In>()[0]);
      if (!move(streamer, instance.getuzipPackagesResultIn(), prop))
        return false;
      }
      break;
    case 194734422:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_startPackagesVerify_In>()[0]);
      if (!move(streamer, instance.startPackagesVerifyIn(), prop))
        return false;
      }
      break;
    case 4313302:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_getPackagesVerifyResult_In>()[0]);
      if (!move(streamer, instance.getPackagesVerifyResultIn(), prop))
        return false;
      }
      break;
    case 178527735:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_checkUpdateCondition_In>()[0]);
      if (!move(streamer, instance.checkUpdateConditionIn(), prop))
        return false;
      }
      break;
    case 200329384:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_getCheckUpdateConditionResult_In>()[0]);
      if (!move(streamer, instance.getCheckUpdateConditionResultIn(), prop))
        return false;
      }
      break;
    case 136901098:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_startUpdate_In>()[0]);
      if (!move(streamer, instance.startUpdateIn(), prop))
        return false;
      }
      break;
    case 29478526:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_resumeUpdate_In>()[0]);
      if (!move(streamer, instance.resumeUpdateIn(), prop))
        return false;
      }
      break;
    case 251480810:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_pauseUpdate_In>()[0]);
      if (!move(streamer, instance.pauseUpdateIn(), prop))
        return false;
      }
      break;
    case 66921313:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_getUpdateProgress_In>()[0]);
      if (!move(streamer, instance.getUpdateProgressIn(), prop))
        return false;
      }
      break;
    case 146417871:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_activate_In>()[0]);
      if (!move(streamer, instance.activateIn(), prop))
        return false;
      }
      break;
    case 170403593:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_rollback_In>()[0]);
      if (!move(streamer, instance.rollbackIn(), prop))
        return false;
      }
      break;
    case 31250426:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_getRollbackProgress_In>()[0]);
      if (!move(streamer, instance.getRollbackProgressIn(), prop))
        return false;
      }
      break;
    case 126211184:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_uploadLog_In>()[0]);
      if (!move(streamer, instance.uploadLogIn(), prop))
        return false;
      }
      break;
    default:
      break;
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_Call& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  const auto d = instance._d();
  if (!max(streamer, d))
    return false;
  size_t union_max = streamer.position();
  size_t alignment_max = streamer.alignment();
  {
    size_t pos = streamer.position();
    size_t alignment = streamer.alignment();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_inventoryCollection_In>()[0]);
      if (!max(streamer, instance.inventoryCollectionIn(), prop))
        return false;
    if (union_max < streamer.position()) {
      union_max = streamer.position();
      alignment_max = streamer.alignment();
    }
    streamer.position(pos);
    streamer.alignment(alignment);
  }
  {
    size_t pos = streamer.position();
    size_t alignment = streamer.alignment();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_stopInventoryCollection_In>()[0]);
      if (!max(streamer, instance.stopInventoryCollectionIn(), prop))
        return false;
    if (union_max < streamer.position()) {
      union_max = streamer.position();
      alignment_max = streamer.alignment();
    }
    streamer.position(pos);
    streamer.alignment(alignment);
  }
  {
    size_t pos = streamer.position();
    size_t alignment = streamer.alignment();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_getInventoryResult_In>()[0]);
      if (!max(streamer, instance.getInventoryResultIn(), prop))
        return false;
    if (union_max < streamer.position()) {
      union_max = streamer.position();
      alignment_max = streamer.alignment();
    }
    streamer.position(pos);
    streamer.alignment(alignment);
  }
  {
    size_t pos = streamer.position();
    size_t alignment = streamer.alignment();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_checkDownloadCondition_In>()[0]);
      if (!max(streamer, instance.checkDownloadConditionIn(), prop))
        return false;
    if (union_max < streamer.position()) {
      union_max = streamer.position();
      alignment_max = streamer.alignment();
    }
    streamer.position(pos);
    streamer.alignment(alignment);
  }
  {
    size_t pos = streamer.position();
    size_t alignment = streamer.alignment();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_startDownload_In>()[0]);
      if (!max(streamer, instance.startDownloadIn(), prop))
        return false;
    if (union_max < streamer.position()) {
      union_max = streamer.position();
      alignment_max = streamer.alignment();
    }
    streamer.position(pos);
    streamer.alignment(alignment);
  }
  {
    size_t pos = streamer.position();
    size_t alignment = streamer.alignment();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_downloadCtrl_In>()[0]);
      if (!max(streamer, instance.downloadCtrlIn(), prop))
        return false;
    if (union_max < streamer.position()) {
      union_max = streamer.position();
      alignment_max = streamer.alignment();
    }
    streamer.position(pos);
    streamer.alignment(alignment);
  }
  {
    size_t pos = streamer.position();
    size_t alignment = streamer.alignment();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_getDownloadProgress_In>()[0]);
      if (!max(streamer, instance.getDownloadProgressIn(), prop))
        return false;
    if (union_max < streamer.position()) {
      union_max = streamer.position();
      alignment_max = streamer.alignment();
    }
    streamer.position(pos);
    streamer.alignment(alignment);
  }
  {
    size_t pos = streamer.position();
    size_t alignment = streamer.alignment();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_uzipPackages_In>()[0]);
      if (!max(streamer, instance.uzipPackagesIn(), prop))
        return false;
    if (union_max < streamer.position()) {
      union_max = streamer.position();
      alignment_max = streamer.alignment();
    }
    streamer.position(pos);
    streamer.alignment(alignment);
  }
  {
    size_t pos = streamer.position();
    size_t alignment = streamer.alignment();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_getuzipPackagesResult_In>()[0]);
      if (!max(streamer, instance.getuzipPackagesResultIn(), prop))
        return false;
    if (union_max < streamer.position()) {
      union_max = streamer.position();
      alignment_max = streamer.alignment();
    }
    streamer.position(pos);
    streamer.alignment(alignment);
  }
  {
    size_t pos = streamer.position();
    size_t alignment = streamer.alignment();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_startPackagesVerify_In>()[0]);
      if (!max(streamer, instance.startPackagesVerifyIn(), prop))
        return false;
    if (union_max < streamer.position()) {
      union_max = streamer.position();
      alignment_max = streamer.alignment();
    }
    streamer.position(pos);
    streamer.alignment(alignment);
  }
  {
    size_t pos = streamer.position();
    size_t alignment = streamer.alignment();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_getPackagesVerifyResult_In>()[0]);
      if (!max(streamer, instance.getPackagesVerifyResultIn(), prop))
        return false;
    if (union_max < streamer.position()) {
      union_max = streamer.position();
      alignment_max = streamer.alignment();
    }
    streamer.position(pos);
    streamer.alignment(alignment);
  }
  {
    size_t pos = streamer.position();
    size_t alignment = streamer.alignment();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_checkUpdateCondition_In>()[0]);
      if (!max(streamer, instance.checkUpdateConditionIn(), prop))
        return false;
    if (union_max < streamer.position()) {
      union_max = streamer.position();
      alignment_max = streamer.alignment();
    }
    streamer.position(pos);
    streamer.alignment(alignment);
  }
  {
    size_t pos = streamer.position();
    size_t alignment = streamer.alignment();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_getCheckUpdateConditionResult_In>()[0]);
      if (!max(streamer, instance.getCheckUpdateConditionResultIn(), prop))
        return false;
    if (union_max < streamer.position()) {
      union_max = streamer.position();
      alignment_max = streamer.alignment();
    }
    streamer.position(pos);
    streamer.alignment(alignment);
  }
  {
    size_t pos = streamer.position();
    size_t alignment = streamer.alignment();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_startUpdate_In>()[0]);
      if (!max(streamer, instance.startUpdateIn(), prop))
        return false;
    if (union_max < streamer.position()) {
      union_max = streamer.position();
      alignment_max = streamer.alignment();
    }
    streamer.position(pos);
    streamer.alignment(alignment);
  }
  {
    size_t pos = streamer.position();
    size_t alignment = streamer.alignment();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_resumeUpdate_In>()[0]);
      if (!max(streamer, instance.resumeUpdateIn(), prop))
        return false;
    if (union_max < streamer.position()) {
      union_max = streamer.position();
      alignment_max = streamer.alignment();
    }
    streamer.position(pos);
    streamer.alignment(alignment);
  }
  {
    size_t pos = streamer.position();
    size_t alignment = streamer.alignment();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_pauseUpdate_In>()[0]);
      if (!max(streamer, instance.pauseUpdateIn(), prop))
        return false;
    if (union_max < streamer.position()) {
      union_max = streamer.position();
      alignment_max = streamer.alignment();
    }
    streamer.position(pos);
    streamer.alignment(alignment);
  }
  {
    size_t pos = streamer.position();
    size_t alignment = streamer.alignment();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_getUpdateProgress_In>()[0]);
      if (!max(streamer, instance.getUpdateProgressIn(), prop))
        return false;
    if (union_max < streamer.position()) {
      union_max = streamer.position();
      alignment_max = streamer.alignment();
    }
    streamer.position(pos);
    streamer.alignment(alignment);
  }
  {
    size_t pos = streamer.position();
    size_t alignment = streamer.alignment();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_activate_In>()[0]);
      if (!max(streamer, instance.activateIn(), prop))
        return false;
    if (union_max < streamer.position()) {
      union_max = streamer.position();
      alignment_max = streamer.alignment();
    }
    streamer.position(pos);
    streamer.alignment(alignment);
  }
  {
    size_t pos = streamer.position();
    size_t alignment = streamer.alignment();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_rollback_In>()[0]);
      if (!max(streamer, instance.rollbackIn(), prop))
        return false;
    if (union_max < streamer.position()) {
      union_max = streamer.position();
      alignment_max = streamer.alignment();
    }
    streamer.position(pos);
    streamer.alignment(alignment);
  }
  {
    size_t pos = streamer.position();
    size_t alignment = streamer.alignment();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_getRollbackProgress_In>()[0]);
      if (!max(streamer, instance.getRollbackProgressIn(), prop))
        return false;
    if (union_max < streamer.position()) {
      union_max = streamer.position();
      alignment_max = streamer.alignment();
    }
    streamer.position(pos);
    streamer.alignment(alignment);
  }
  {
    size_t pos = streamer.position();
    size_t alignment = streamer.alignment();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_uploadLog_In>()[0]);
      if (!max(streamer, instance.uploadLogIn(), prop))
        return false;
    if (union_max < streamer.position()) {
      union_max = streamer.position();
      alignment_max = streamer.alignment();
    }
    streamer.position(pos);
    streamer.alignment(alignment);
  }
  streamer.position(union_max);
  streamer.alignment(alignment_max);
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_Request>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_Request = get_type_props<::seres::ota_duc_service::DucServiceInterface_Request>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_Request& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.header(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.data(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::seres::ota_duc_service::DucServiceInterface_Request& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::DucServiceInterface_Request>();
  str.set_mode(cdr_stream::stream_mode::write, key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_Request& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.header(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.data(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::seres::ota_duc_service::DucServiceInterface_Request& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::DucServiceInterface_Request>();
  str.set_mode(cdr_stream::stream_mode::read, key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_Request& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.header(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.data(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::seres::ota_duc_service::DucServiceInterface_Request& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::DucServiceInterface_Request>();
  str.set_mode(cdr_stream::stream_mode::move, key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_Request& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.header(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.data(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::seres::ota_duc_service::DucServiceInterface_Request& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::DucServiceInterface_Request>();
  str.set_mode(cdr_stream::stream_mode::max, key);
  return max(str, instance, props.data()); 
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_Return>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_Return = get_type_props<::seres::ota_duc_service::DucServiceInterface_Return>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_Return& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  const auto d = instance._d();
  if (!write(streamer, d))
    return false;
  switch(d)
  {
    case 202679978:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_inventoryCollection_Result>()[0]);
      if (!write(streamer, instance.inventoryCollectionResult(), prop))
        return false;
      }
      break;
    case 23721455:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_stopInventoryCollection_Result>()[0]);
      if (!write(streamer, instance.stopInventoryCollectionResult(), prop))
        return false;
      }
      break;
    case 207766764:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_getInventoryResult_Result>()[0]);
      if (!write(streamer, instance.getInventoryResultResult(), prop))
        return false;
      }
      break;
    case 7343798:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_checkDownloadCondition_Result>()[0]);
      if (!write(streamer, instance.checkDownloadConditionResult(), prop))
        return false;
      }
      break;
    case 38715577:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_startDownload_Result>()[0]);
      if (!write(streamer, instance.startDownloadResult(), prop))
        return false;
      }
      break;
    case 33876474:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_downloadCtrl_Result>()[0]);
      if (!write(streamer, instance.downloadCtrlResult(), prop))
        return false;
      }
      break;
    case 173346514:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_getDownloadProgress_Result>()[0]);
      if (!write(streamer, instance.getDownloadProgressResult(), prop))
        return false;
      }
      break;
    case 113763844:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_uzipPackages_Result>()[0]);
      if (!write(streamer, instance.uzipPackagesResult(), prop))
        return false;
      }
      break;
    case 256795369:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_getuzipPackagesResult_Result>()[0]);
      if (!write(streamer, instance.getuzipPackagesResultResult(), prop))
        return false;
      }
      break;
    case 194734422:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_startPackagesVerify_Result>()[0]);
      if (!write(streamer, instance.startPackagesVerifyResult(), prop))
        return false;
      }
      break;
    case 4313302:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_getPackagesVerifyResult_Result>()[0]);
      if (!write(streamer, instance.getPackagesVerifyResultResult(), prop))
        return false;
      }
      break;
    case 178527735:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_checkUpdateCondition_Result>()[0]);
      if (!write(streamer, instance.checkUpdateConditionResult(), prop))
        return false;
      }
      break;
    case 200329384:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_getCheckUpdateConditionResult_Result>()[0]);
      if (!write(streamer, instance.getCheckUpdateConditionResultResult(), prop))
        return false;
      }
      break;
    case 136901098:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_startUpdate_Result>()[0]);
      if (!write(streamer, instance.startUpdateResult(), prop))
        return false;
      }
      break;
    case 29478526:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_resumeUpdate_Result>()[0]);
      if (!write(streamer, instance.resumeUpdateResult(), prop))
        return false;
      }
      break;
    case 251480810:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_pauseUpdate_Result>()[0]);
      if (!write(streamer, instance.pauseUpdateResult(), prop))
        return false;
      }
      break;
    case 66921313:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_getUpdateProgress_Result>()[0]);
      if (!write(streamer, instance.getUpdateProgressResult(), prop))
        return false;
      }
      break;
    case 146417871:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_activate_Result>()[0]);
      if (!write(streamer, instance.activateResult(), prop))
        return false;
      }
      break;
    case 170403593:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_rollback_Result>()[0]);
      if (!write(streamer, instance.rollbackResult(), prop))
        return false;
      }
      break;
    case 31250426:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_getRollbackProgress_Result>()[0]);
      if (!write(streamer, instance.getRollbackProgressResult(), prop))
        return false;
      }
      break;
    case 126211184:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_uploadLog_Result>()[0]);
      if (!write(streamer, instance.uploadLogResult(), prop))
        return false;
      }
      break;
    default:
      break;
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_Return& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto d = instance._d();
  if (!read(streamer, d))
    return false;
  switch(d)
  {
    case 202679978:
    {
      auto obj = decl_ref_type(instance.inventoryCollectionResult())();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_inventoryCollection_Result>()[0]);
      if (!read(streamer, obj, prop))
        return false;
      instance.inventoryCollectionResult(obj);
    }
    break;
    case 23721455:
    {
      auto obj = decl_ref_type(instance.stopInventoryCollectionResult())();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_stopInventoryCollection_Result>()[0]);
      if (!read(streamer, obj, prop))
        return false;
      instance.stopInventoryCollectionResult(obj);
    }
    break;
    case 207766764:
    {
      auto obj = decl_ref_type(instance.getInventoryResultResult())();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_getInventoryResult_Result>()[0]);
      if (!read(streamer, obj, prop))
        return false;
      instance.getInventoryResultResult(obj);
    }
    break;
    case 7343798:
    {
      auto obj = decl_ref_type(instance.checkDownloadConditionResult())();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_checkDownloadCondition_Result>()[0]);
      if (!read(streamer, obj, prop))
        return false;
      instance.checkDownloadConditionResult(obj);
    }
    break;
    case 38715577:
    {
      auto obj = decl_ref_type(instance.startDownloadResult())();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_startDownload_Result>()[0]);
      if (!read(streamer, obj, prop))
        return false;
      instance.startDownloadResult(obj);
    }
    break;
    case 33876474:
    {
      auto obj = decl_ref_type(instance.downloadCtrlResult())();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_downloadCtrl_Result>()[0]);
      if (!read(streamer, obj, prop))
        return false;
      instance.downloadCtrlResult(obj);
    }
    break;
    case 173346514:
    {
      auto obj = decl_ref_type(instance.getDownloadProgressResult())();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_getDownloadProgress_Result>()[0]);
      if (!read(streamer, obj, prop))
        return false;
      instance.getDownloadProgressResult(obj);
    }
    break;
    case 113763844:
    {
      auto obj = decl_ref_type(instance.uzipPackagesResult())();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_uzipPackages_Result>()[0]);
      if (!read(streamer, obj, prop))
        return false;
      instance.uzipPackagesResult(obj);
    }
    break;
    case 256795369:
    {
      auto obj = decl_ref_type(instance.getuzipPackagesResultResult())();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_getuzipPackagesResult_Result>()[0]);
      if (!read(streamer, obj, prop))
        return false;
      instance.getuzipPackagesResultResult(obj);
    }
    break;
    case 194734422:
    {
      auto obj = decl_ref_type(instance.startPackagesVerifyResult())();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_startPackagesVerify_Result>()[0]);
      if (!read(streamer, obj, prop))
        return false;
      instance.startPackagesVerifyResult(obj);
    }
    break;
    case 4313302:
    {
      auto obj = decl_ref_type(instance.getPackagesVerifyResultResult())();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_getPackagesVerifyResult_Result>()[0]);
      if (!read(streamer, obj, prop))
        return false;
      instance.getPackagesVerifyResultResult(obj);
    }
    break;
    case 178527735:
    {
      auto obj = decl_ref_type(instance.checkUpdateConditionResult())();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_checkUpdateCondition_Result>()[0]);
      if (!read(streamer, obj, prop))
        return false;
      instance.checkUpdateConditionResult(obj);
    }
    break;
    case 200329384:
    {
      auto obj = decl_ref_type(instance.getCheckUpdateConditionResultResult())();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_getCheckUpdateConditionResult_Result>()[0]);
      if (!read(streamer, obj, prop))
        return false;
      instance.getCheckUpdateConditionResultResult(obj);
    }
    break;
    case 136901098:
    {
      auto obj = decl_ref_type(instance.startUpdateResult())();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_startUpdate_Result>()[0]);
      if (!read(streamer, obj, prop))
        return false;
      instance.startUpdateResult(obj);
    }
    break;
    case 29478526:
    {
      auto obj = decl_ref_type(instance.resumeUpdateResult())();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_resumeUpdate_Result>()[0]);
      if (!read(streamer, obj, prop))
        return false;
      instance.resumeUpdateResult(obj);
    }
    break;
    case 251480810:
    {
      auto obj = decl_ref_type(instance.pauseUpdateResult())();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_pauseUpdate_Result>()[0]);
      if (!read(streamer, obj, prop))
        return false;
      instance.pauseUpdateResult(obj);
    }
    break;
    case 66921313:
    {
      auto obj = decl_ref_type(instance.getUpdateProgressResult())();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_getUpdateProgress_Result>()[0]);
      if (!read(streamer, obj, prop))
        return false;
      instance.getUpdateProgressResult(obj);
    }
    break;
    case 146417871:
    {
      auto obj = decl_ref_type(instance.activateResult())();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_activate_Result>()[0]);
      if (!read(streamer, obj, prop))
        return false;
      instance.activateResult(obj);
    }
    break;
    case 170403593:
    {
      auto obj = decl_ref_type(instance.rollbackResult())();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_rollback_Result>()[0]);
      if (!read(streamer, obj, prop))
        return false;
      instance.rollbackResult(obj);
    }
    break;
    case 31250426:
    {
      auto obj = decl_ref_type(instance.getRollbackProgressResult())();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_getRollbackProgress_Result>()[0]);
      if (!read(streamer, obj, prop))
        return false;
      instance.getRollbackProgressResult(obj);
    }
    break;
    case 126211184:
    {
      auto obj = decl_ref_type(instance.uploadLogResult())();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_uploadLog_Result>()[0]);
      if (!read(streamer, obj, prop))
        return false;
      instance.uploadLogResult(obj);
    }
    break;
    default:
      instance._d(d);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_Return& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  const auto d = instance._d();
  if (!move(streamer, d))
    return false;
  switch(d)
  {
    case 202679978:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_inventoryCollection_Result>()[0]);
      if (!move(streamer, instance.inventoryCollectionResult(), prop))
        return false;
      }
      break;
    case 23721455:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_stopInventoryCollection_Result>()[0]);
      if (!move(streamer, instance.stopInventoryCollectionResult(), prop))
        return false;
      }
      break;
    case 207766764:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_getInventoryResult_Result>()[0]);
      if (!move(streamer, instance.getInventoryResultResult(), prop))
        return false;
      }
      break;
    case 7343798:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_checkDownloadCondition_Result>()[0]);
      if (!move(streamer, instance.checkDownloadConditionResult(), prop))
        return false;
      }
      break;
    case 38715577:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_startDownload_Result>()[0]);
      if (!move(streamer, instance.startDownloadResult(), prop))
        return false;
      }
      break;
    case 33876474:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_downloadCtrl_Result>()[0]);
      if (!move(streamer, instance.downloadCtrlResult(), prop))
        return false;
      }
      break;
    case 173346514:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_getDownloadProgress_Result>()[0]);
      if (!move(streamer, instance.getDownloadProgressResult(), prop))
        return false;
      }
      break;
    case 113763844:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_uzipPackages_Result>()[0]);
      if (!move(streamer, instance.uzipPackagesResult(), prop))
        return false;
      }
      break;
    case 256795369:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_getuzipPackagesResult_Result>()[0]);
      if (!move(streamer, instance.getuzipPackagesResultResult(), prop))
        return false;
      }
      break;
    case 194734422:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_startPackagesVerify_Result>()[0]);
      if (!move(streamer, instance.startPackagesVerifyResult(), prop))
        return false;
      }
      break;
    case 4313302:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_getPackagesVerifyResult_Result>()[0]);
      if (!move(streamer, instance.getPackagesVerifyResultResult(), prop))
        return false;
      }
      break;
    case 178527735:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_checkUpdateCondition_Result>()[0]);
      if (!move(streamer, instance.checkUpdateConditionResult(), prop))
        return false;
      }
      break;
    case 200329384:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_getCheckUpdateConditionResult_Result>()[0]);
      if (!move(streamer, instance.getCheckUpdateConditionResultResult(), prop))
        return false;
      }
      break;
    case 136901098:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_startUpdate_Result>()[0]);
      if (!move(streamer, instance.startUpdateResult(), prop))
        return false;
      }
      break;
    case 29478526:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_resumeUpdate_Result>()[0]);
      if (!move(streamer, instance.resumeUpdateResult(), prop))
        return false;
      }
      break;
    case 251480810:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_pauseUpdate_Result>()[0]);
      if (!move(streamer, instance.pauseUpdateResult(), prop))
        return false;
      }
      break;
    case 66921313:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_getUpdateProgress_Result>()[0]);
      if (!move(streamer, instance.getUpdateProgressResult(), prop))
        return false;
      }
      break;
    case 146417871:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_activate_Result>()[0]);
      if (!move(streamer, instance.activateResult(), prop))
        return false;
      }
      break;
    case 170403593:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_rollback_Result>()[0]);
      if (!move(streamer, instance.rollbackResult(), prop))
        return false;
      }
      break;
    case 31250426:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_getRollbackProgress_Result>()[0]);
      if (!move(streamer, instance.getRollbackProgressResult(), prop))
        return false;
      }
      break;
    case 126211184:
      {
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_uploadLog_Result>()[0]);
      if (!move(streamer, instance.uploadLogResult(), prop))
        return false;
      }
      break;
    default:
      break;
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_Return& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  const auto d = instance._d();
  if (!max(streamer, d))
    return false;
  size_t union_max = streamer.position();
  size_t alignment_max = streamer.alignment();
  {
    size_t pos = streamer.position();
    size_t alignment = streamer.alignment();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_inventoryCollection_Result>()[0]);
      if (!max(streamer, instance.inventoryCollectionResult(), prop))
        return false;
    if (union_max < streamer.position()) {
      union_max = streamer.position();
      alignment_max = streamer.alignment();
    }
    streamer.position(pos);
    streamer.alignment(alignment);
  }
  {
    size_t pos = streamer.position();
    size_t alignment = streamer.alignment();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_stopInventoryCollection_Result>()[0]);
      if (!max(streamer, instance.stopInventoryCollectionResult(), prop))
        return false;
    if (union_max < streamer.position()) {
      union_max = streamer.position();
      alignment_max = streamer.alignment();
    }
    streamer.position(pos);
    streamer.alignment(alignment);
  }
  {
    size_t pos = streamer.position();
    size_t alignment = streamer.alignment();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_getInventoryResult_Result>()[0]);
      if (!max(streamer, instance.getInventoryResultResult(), prop))
        return false;
    if (union_max < streamer.position()) {
      union_max = streamer.position();
      alignment_max = streamer.alignment();
    }
    streamer.position(pos);
    streamer.alignment(alignment);
  }
  {
    size_t pos = streamer.position();
    size_t alignment = streamer.alignment();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_checkDownloadCondition_Result>()[0]);
      if (!max(streamer, instance.checkDownloadConditionResult(), prop))
        return false;
    if (union_max < streamer.position()) {
      union_max = streamer.position();
      alignment_max = streamer.alignment();
    }
    streamer.position(pos);
    streamer.alignment(alignment);
  }
  {
    size_t pos = streamer.position();
    size_t alignment = streamer.alignment();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_startDownload_Result>()[0]);
      if (!max(streamer, instance.startDownloadResult(), prop))
        return false;
    if (union_max < streamer.position()) {
      union_max = streamer.position();
      alignment_max = streamer.alignment();
    }
    streamer.position(pos);
    streamer.alignment(alignment);
  }
  {
    size_t pos = streamer.position();
    size_t alignment = streamer.alignment();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_downloadCtrl_Result>()[0]);
      if (!max(streamer, instance.downloadCtrlResult(), prop))
        return false;
    if (union_max < streamer.position()) {
      union_max = streamer.position();
      alignment_max = streamer.alignment();
    }
    streamer.position(pos);
    streamer.alignment(alignment);
  }
  {
    size_t pos = streamer.position();
    size_t alignment = streamer.alignment();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_getDownloadProgress_Result>()[0]);
      if (!max(streamer, instance.getDownloadProgressResult(), prop))
        return false;
    if (union_max < streamer.position()) {
      union_max = streamer.position();
      alignment_max = streamer.alignment();
    }
    streamer.position(pos);
    streamer.alignment(alignment);
  }
  {
    size_t pos = streamer.position();
    size_t alignment = streamer.alignment();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_uzipPackages_Result>()[0]);
      if (!max(streamer, instance.uzipPackagesResult(), prop))
        return false;
    if (union_max < streamer.position()) {
      union_max = streamer.position();
      alignment_max = streamer.alignment();
    }
    streamer.position(pos);
    streamer.alignment(alignment);
  }
  {
    size_t pos = streamer.position();
    size_t alignment = streamer.alignment();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_getuzipPackagesResult_Result>()[0]);
      if (!max(streamer, instance.getuzipPackagesResultResult(), prop))
        return false;
    if (union_max < streamer.position()) {
      union_max = streamer.position();
      alignment_max = streamer.alignment();
    }
    streamer.position(pos);
    streamer.alignment(alignment);
  }
  {
    size_t pos = streamer.position();
    size_t alignment = streamer.alignment();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_startPackagesVerify_Result>()[0]);
      if (!max(streamer, instance.startPackagesVerifyResult(), prop))
        return false;
    if (union_max < streamer.position()) {
      union_max = streamer.position();
      alignment_max = streamer.alignment();
    }
    streamer.position(pos);
    streamer.alignment(alignment);
  }
  {
    size_t pos = streamer.position();
    size_t alignment = streamer.alignment();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_getPackagesVerifyResult_Result>()[0]);
      if (!max(streamer, instance.getPackagesVerifyResultResult(), prop))
        return false;
    if (union_max < streamer.position()) {
      union_max = streamer.position();
      alignment_max = streamer.alignment();
    }
    streamer.position(pos);
    streamer.alignment(alignment);
  }
  {
    size_t pos = streamer.position();
    size_t alignment = streamer.alignment();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_checkUpdateCondition_Result>()[0]);
      if (!max(streamer, instance.checkUpdateConditionResult(), prop))
        return false;
    if (union_max < streamer.position()) {
      union_max = streamer.position();
      alignment_max = streamer.alignment();
    }
    streamer.position(pos);
    streamer.alignment(alignment);
  }
  {
    size_t pos = streamer.position();
    size_t alignment = streamer.alignment();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_getCheckUpdateConditionResult_Result>()[0]);
      if (!max(streamer, instance.getCheckUpdateConditionResultResult(), prop))
        return false;
    if (union_max < streamer.position()) {
      union_max = streamer.position();
      alignment_max = streamer.alignment();
    }
    streamer.position(pos);
    streamer.alignment(alignment);
  }
  {
    size_t pos = streamer.position();
    size_t alignment = streamer.alignment();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_startUpdate_Result>()[0]);
      if (!max(streamer, instance.startUpdateResult(), prop))
        return false;
    if (union_max < streamer.position()) {
      union_max = streamer.position();
      alignment_max = streamer.alignment();
    }
    streamer.position(pos);
    streamer.alignment(alignment);
  }
  {
    size_t pos = streamer.position();
    size_t alignment = streamer.alignment();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_resumeUpdate_Result>()[0]);
      if (!max(streamer, instance.resumeUpdateResult(), prop))
        return false;
    if (union_max < streamer.position()) {
      union_max = streamer.position();
      alignment_max = streamer.alignment();
    }
    streamer.position(pos);
    streamer.alignment(alignment);
  }
  {
    size_t pos = streamer.position();
    size_t alignment = streamer.alignment();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_pauseUpdate_Result>()[0]);
      if (!max(streamer, instance.pauseUpdateResult(), prop))
        return false;
    if (union_max < streamer.position()) {
      union_max = streamer.position();
      alignment_max = streamer.alignment();
    }
    streamer.position(pos);
    streamer.alignment(alignment);
  }
  {
    size_t pos = streamer.position();
    size_t alignment = streamer.alignment();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_getUpdateProgress_Result>()[0]);
      if (!max(streamer, instance.getUpdateProgressResult(), prop))
        return false;
    if (union_max < streamer.position()) {
      union_max = streamer.position();
      alignment_max = streamer.alignment();
    }
    streamer.position(pos);
    streamer.alignment(alignment);
  }
  {
    size_t pos = streamer.position();
    size_t alignment = streamer.alignment();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_activate_Result>()[0]);
      if (!max(streamer, instance.activateResult(), prop))
        return false;
    if (union_max < streamer.position()) {
      union_max = streamer.position();
      alignment_max = streamer.alignment();
    }
    streamer.position(pos);
    streamer.alignment(alignment);
  }
  {
    size_t pos = streamer.position();
    size_t alignment = streamer.alignment();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_rollback_Result>()[0]);
      if (!max(streamer, instance.rollbackResult(), prop))
        return false;
    if (union_max < streamer.position()) {
      union_max = streamer.position();
      alignment_max = streamer.alignment();
    }
    streamer.position(pos);
    streamer.alignment(alignment);
  }
  {
    size_t pos = streamer.position();
    size_t alignment = streamer.alignment();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_getRollbackProgress_Result>()[0]);
      if (!max(streamer, instance.getRollbackProgressResult(), prop))
        return false;
    if (union_max < streamer.position()) {
      union_max = streamer.position();
      alignment_max = streamer.alignment();
    }
    streamer.position(pos);
    streamer.alignment(alignment);
  }
  {
    size_t pos = streamer.position();
    size_t alignment = streamer.alignment();
      const auto &prop = &(get_type_props<::seres::ota_duc_service::DucServiceInterface_uploadLog_Result>()[0]);
      if (!max(streamer, instance.uploadLogResult(), prop))
        return false;
    if (union_max < streamer.position()) {
      union_max = streamer.position();
      alignment_max = streamer.alignment();
    }
    streamer.position(pos);
    streamer.alignment(alignment);
  }
  streamer.position(union_max);
  streamer.alignment(alignment_max);
  return streamer.finish_struct(*props, member_ids);
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_Reply>();

namespace {
  static const volatile propvec &properties___seres__ota_duc_service__DucServiceInterface_Reply = get_type_props<::seres::ota_duc_service::DucServiceInterface_Reply>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_Reply& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.header(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.data(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::seres::ota_duc_service::DucServiceInterface_Reply& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::DucServiceInterface_Reply>();
  str.set_mode(cdr_stream::stream_mode::write, key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::seres::ota_duc_service::DucServiceInterface_Reply& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.header(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.data(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::seres::ota_duc_service::DucServiceInterface_Reply& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::DucServiceInterface_Reply>();
  str.set_mode(cdr_stream::stream_mode::read, key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_Reply& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.header(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.data(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::seres::ota_duc_service::DucServiceInterface_Reply& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::DucServiceInterface_Reply>();
  str.set_mode(cdr_stream::stream_mode::move, key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::seres::ota_duc_service::DucServiceInterface_Reply& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.header(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.data(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::seres::ota_duc_service::DucServiceInterface_Reply& instance, key_mode key) {
  const auto &props = get_type_props<::seres::ota_duc_service::DucServiceInterface_Reply>();
  str.set_mode(cdr_stream::stream_mode::max, key);
  return max(str, instance, props.data()); 
}

} //namespace cdr
} //namespace core
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

#endif // DDSCXX_OTA_DUCINTERFACE_HPP_46FD7474B46E62D7C8A5F4776E293F0F
