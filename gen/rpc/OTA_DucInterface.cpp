/****************************************************************

  Generated by Eclipse Cyclone DDS IDL to CXX Translator
  File name: /home/<USER>/dds/scom-idl/OTA/OTA_DucInterface.idl
  Source: OTA_DucInterface.cpp
  Cyclone DDS: v0.11.0

*****************************************************************/
#include "OTA_DucInterface.hpp"

namespace seres
{
namespace ota_duc_service
{
std::ostream& operator<<(std::ostream& os, ReturnCode const& rhs)
{
  (void) rhs;
  switch (rhs)
  {
    case ReturnCode::OK:
      os << "ReturnCode::OK"; break;
    case ReturnCode::ERROR:
      os << "ReturnCode::ERROR"; break;
    case ReturnCode::REFUSED:
      os << "ReturnCode::REFUSED"; break;
    default: break;
  }
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_inventoryCollection_In const& rhs)
{
  (void) rhs;
  os << "[";
  os << "inventory_list: " << rhs.inventory_list();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_stopInventoryCollection_In const& rhs)
{
  (void) rhs;
  os << "[";
  os << "_default: " << rhs._default();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_getInventoryResult_In const& rhs)
{
  (void) rhs;
  os << "[";
  os << "_default: " << rhs._default();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_checkDownloadCondition_In const& rhs)
{
  (void) rhs;
  os << "[";
  os << "conditions: " << rhs.conditions();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_startDownload_In const& rhs)
{
  (void) rhs;
  os << "[";
  os << "task_list: " << rhs.task_list();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_downloadCtrl_In const& rhs)
{
  (void) rhs;
  os << "[";
  os << "download_command: " << rhs.download_command();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_getDownloadProgress_In const& rhs)
{
  (void) rhs;
  os << "[";
  os << "_default: " << rhs._default();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_uzipPackages_In const& rhs)
{
  (void) rhs;
  os << "[";
  os << "_default: " << rhs._default();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_getuzipPackagesResult_In const& rhs)
{
  (void) rhs;
  os << "[";
  os << "_default: " << rhs._default();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_startPackagesVerify_In const& rhs)
{
  (void) rhs;
  os << "[";
  os << "_default: " << rhs._default();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_getPackagesVerifyResult_In const& rhs)
{
  (void) rhs;
  os << "[";
  os << "_default: " << rhs._default();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_checkUpdateCondition_In const& rhs)
{
  (void) rhs;
  os << "[";
  os << "_default: " << rhs._default();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_getCheckUpdateConditionResult_In const& rhs)
{
  (void) rhs;
  os << "[";
  os << "_default: " << rhs._default();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_startUpdate_In const& rhs)
{
  (void) rhs;
  os << "[";
  os << "mode: " << rhs.mode();
  os << ", update_list: " << rhs.update_list();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_resumeUpdate_In const& rhs)
{
  (void) rhs;
  os << "[";
  os << "_default: " << rhs._default();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_pauseUpdate_In const& rhs)
{
  (void) rhs;
  os << "[";
  os << "_default: " << rhs._default();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_getUpdateProgress_In const& rhs)
{
  (void) rhs;
  os << "[";
  os << "_default: " << rhs._default();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_activate_In const& rhs)
{
  (void) rhs;
  os << "[";
  os << "_default: " << rhs._default();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_rollback_In const& rhs)
{
  (void) rhs;
  os << "[";
  os << "component_list: " << rhs.component_list();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_getRollbackProgress_In const& rhs)
{
  (void) rhs;
  os << "[";
  os << "_default: " << rhs._default();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_uploadLog_In const& rhs)
{
  (void) rhs;
  os << "[";
  os << "_default: " << rhs._default();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_inventoryCollection_Out const& rhs)
{
  (void) rhs;
  os << "[";
  os << "_default: " << rhs._default();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_inventoryCollection_Result const& rhs)
{
  (void) rhs;
  os << "[";
  os << "inventoryCollectionOut: " << rhs.inventoryCollectionOut();
  os << ", _return: " << rhs._return();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_stopInventoryCollection_Out const& rhs)
{
  (void) rhs;
  os << "[";
  os << "_default: " << rhs._default();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_stopInventoryCollection_Result const& rhs)
{
  (void) rhs;
  os << "[";
  os << "stopInventoryCollectionOut: " << rhs.stopInventoryCollectionOut();
  os << ", _return: " << rhs._return();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_getInventoryResult_Out const& rhs)
{
  (void) rhs;
  os << "[";
  os << "inventory_list: " << rhs.inventory_list();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_getInventoryResult_Result const& rhs)
{
  (void) rhs;
  os << "[";
  os << "getInventoryResultOut: " << rhs.getInventoryResultOut();
  os << ", _return: " << rhs._return();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_checkDownloadCondition_Out const& rhs)
{
  (void) rhs;
  os << "[";
  os << "condition_result: " << rhs.condition_result();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_checkDownloadCondition_Result const& rhs)
{
  (void) rhs;
  os << "[";
  os << "checkDownloadConditionOut: " << rhs.checkDownloadConditionOut();
  os << ", _return: " << rhs._return();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_startDownload_Out const& rhs)
{
  (void) rhs;
  os << "[";
  os << "_default: " << rhs._default();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_startDownload_Result const& rhs)
{
  (void) rhs;
  os << "[";
  os << "startDownloadOut: " << rhs.startDownloadOut();
  os << ", _return: " << rhs._return();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_downloadCtrl_Out const& rhs)
{
  (void) rhs;
  os << "[";
  os << "_default: " << rhs._default();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_downloadCtrl_Result const& rhs)
{
  (void) rhs;
  os << "[";
  os << "downloadCtrlOut: " << rhs.downloadCtrlOut();
  os << ", _return: " << rhs._return();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_getDownloadProgress_Out const& rhs)
{
  (void) rhs;
  os << "[";
  os << "download_progress: " << rhs.download_progress();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_getDownloadProgress_Result const& rhs)
{
  (void) rhs;
  os << "[";
  os << "getDownloadProgressOut: " << rhs.getDownloadProgressOut();
  os << ", _return: " << rhs._return();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_uzipPackages_Out const& rhs)
{
  (void) rhs;
  os << "[";
  os << "_default: " << rhs._default();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_uzipPackages_Result const& rhs)
{
  (void) rhs;
  os << "[";
  os << "uzipPackagesOut: " << rhs.uzipPackagesOut();
  os << ", _return: " << rhs._return();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_getuzipPackagesResult_Out const& rhs)
{
  (void) rhs;
  os << "[";
  os << "uzip_Result: " << rhs.uzip_Result();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_getuzipPackagesResult_Result const& rhs)
{
  (void) rhs;
  os << "[";
  os << "getuzipPackagesResultOut: " << rhs.getuzipPackagesResultOut();
  os << ", _return: " << rhs._return();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_startPackagesVerify_Out const& rhs)
{
  (void) rhs;
  os << "[";
  os << "_default: " << rhs._default();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_startPackagesVerify_Result const& rhs)
{
  (void) rhs;
  os << "[";
  os << "startPackagesVerifyOut: " << rhs.startPackagesVerifyOut();
  os << ", _return: " << rhs._return();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_getPackagesVerifyResult_Out const& rhs)
{
  (void) rhs;
  os << "[";
  os << "verify_Result: " << rhs.verify_Result();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_getPackagesVerifyResult_Result const& rhs)
{
  (void) rhs;
  os << "[";
  os << "getPackagesVerifyResultOut: " << rhs.getPackagesVerifyResultOut();
  os << ", _return: " << rhs._return();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_checkUpdateCondition_Out const& rhs)
{
  (void) rhs;
  os << "[";
  os << "_default: " << rhs._default();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_checkUpdateCondition_Result const& rhs)
{
  (void) rhs;
  os << "[";
  os << "checkUpdateConditionOut: " << rhs.checkUpdateConditionOut();
  os << ", _return: " << rhs._return();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_getCheckUpdateConditionResult_Out const& rhs)
{
  (void) rhs;
  os << "[";
  os << "checkcondition_Result: " << rhs.checkcondition_Result();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_getCheckUpdateConditionResult_Result const& rhs)
{
  (void) rhs;
  os << "[";
  os << "getCheckUpdateConditionResultOut: " << rhs.getCheckUpdateConditionResultOut();
  os << ", _return: " << rhs._return();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_startUpdate_Out const& rhs)
{
  (void) rhs;
  os << "[";
  os << "_default: " << rhs._default();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_startUpdate_Result const& rhs)
{
  (void) rhs;
  os << "[";
  os << "startUpdateOut: " << rhs.startUpdateOut();
  os << ", _return: " << rhs._return();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_resumeUpdate_Out const& rhs)
{
  (void) rhs;
  os << "[";
  os << "_default: " << rhs._default();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_resumeUpdate_Result const& rhs)
{
  (void) rhs;
  os << "[";
  os << "resumeUpdateOut: " << rhs.resumeUpdateOut();
  os << ", _return: " << rhs._return();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_pauseUpdate_Out const& rhs)
{
  (void) rhs;
  os << "[";
  os << "_default: " << rhs._default();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_pauseUpdate_Result const& rhs)
{
  (void) rhs;
  os << "[";
  os << "pauseUpdateOut: " << rhs.pauseUpdateOut();
  os << ", _return: " << rhs._return();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_getUpdateProgress_Out const& rhs)
{
  (void) rhs;
  os << "[";
  os << "update_progress: " << rhs.update_progress();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_getUpdateProgress_Result const& rhs)
{
  (void) rhs;
  os << "[";
  os << "getUpdateProgressOut: " << rhs.getUpdateProgressOut();
  os << ", _return: " << rhs._return();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_activate_Out const& rhs)
{
  (void) rhs;
  os << "[";
  os << "_default: " << rhs._default();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_activate_Result const& rhs)
{
  (void) rhs;
  os << "[";
  os << "activateOut: " << rhs.activateOut();
  os << ", _return: " << rhs._return();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_rollback_Out const& rhs)
{
  (void) rhs;
  os << "[";
  os << "_default: " << rhs._default();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_rollback_Result const& rhs)
{
  (void) rhs;
  os << "[";
  os << "rollbackOut: " << rhs.rollbackOut();
  os << ", _return: " << rhs._return();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_getRollbackProgress_Out const& rhs)
{
  (void) rhs;
  os << "[";
  os << "update_progress: " << rhs.update_progress();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_getRollbackProgress_Result const& rhs)
{
  (void) rhs;
  os << "[";
  os << "getRollbackProgressOut: " << rhs.getRollbackProgressOut();
  os << ", _return: " << rhs._return();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_uploadLog_Out const& rhs)
{
  (void) rhs;
  os << "[";
  os << "_default: " << rhs._default();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_uploadLog_Result const& rhs)
{
  (void) rhs;
  os << "[";
  os << "uploadLogOut: " << rhs.uploadLogOut();
  os << ", _return: " << rhs._return();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_Call const& rhs)
{
  (void) rhs;
  switch (rhs._d()) {
    case 202679978:
      os << "inventoryCollectionIn: " << rhs.inventoryCollectionIn(); break;
    case 23721455:
      os << "stopInventoryCollectionIn: " << rhs.stopInventoryCollectionIn(); break;
    case 207766764:
      os << "getInventoryResultIn: " << rhs.getInventoryResultIn(); break;
    case 7343798:
      os << "checkDownloadConditionIn: " << rhs.checkDownloadConditionIn(); break;
    case 38715577:
      os << "startDownloadIn: " << rhs.startDownloadIn(); break;
    case 33876474:
      os << "downloadCtrlIn: " << rhs.downloadCtrlIn(); break;
    case 173346514:
      os << "getDownloadProgressIn: " << rhs.getDownloadProgressIn(); break;
    case 113763844:
      os << "uzipPackagesIn: " << rhs.uzipPackagesIn(); break;
    case 256795369:
      os << "getuzipPackagesResultIn: " << rhs.getuzipPackagesResultIn(); break;
    case 194734422:
      os << "startPackagesVerifyIn: " << rhs.startPackagesVerifyIn(); break;
    case 4313302:
      os << "getPackagesVerifyResultIn: " << rhs.getPackagesVerifyResultIn(); break;
    case 178527735:
      os << "checkUpdateConditionIn: " << rhs.checkUpdateConditionIn(); break;
    case 200329384:
      os << "getCheckUpdateConditionResultIn: " << rhs.getCheckUpdateConditionResultIn(); break;
    case 136901098:
      os << "startUpdateIn: " << rhs.startUpdateIn(); break;
    case 29478526:
      os << "resumeUpdateIn: " << rhs.resumeUpdateIn(); break;
    case 251480810:
      os << "pauseUpdateIn: " << rhs.pauseUpdateIn(); break;
    case 66921313:
      os << "getUpdateProgressIn: " << rhs.getUpdateProgressIn(); break;
    case 146417871:
      os << "activateIn: " << rhs.activateIn(); break;
    case 170403593:
      os << "rollbackIn: " << rhs.rollbackIn(); break;
    case 31250426:
      os << "getRollbackProgressIn: " << rhs.getRollbackProgressIn(); break;
    case 126211184:
      os << "uploadLogIn: " << rhs.uploadLogIn(); break;
    default:
    {
      // Prevent compiler warnings
    }
  }
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_Request const& rhs)
{
  (void) rhs;
  os << "[";
  os << "header: " << rhs.header();
  os << ", data: " << rhs.data();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_Return const& rhs)
{
  (void) rhs;
  switch (rhs._d()) {
    case 202679978:
      os << "inventoryCollectionResult: " << rhs.inventoryCollectionResult(); break;
    case 23721455:
      os << "stopInventoryCollectionResult: " << rhs.stopInventoryCollectionResult(); break;
    case 207766764:
      os << "getInventoryResultResult: " << rhs.getInventoryResultResult(); break;
    case 7343798:
      os << "checkDownloadConditionResult: " << rhs.checkDownloadConditionResult(); break;
    case 38715577:
      os << "startDownloadResult: " << rhs.startDownloadResult(); break;
    case 33876474:
      os << "downloadCtrlResult: " << rhs.downloadCtrlResult(); break;
    case 173346514:
      os << "getDownloadProgressResult: " << rhs.getDownloadProgressResult(); break;
    case 113763844:
      os << "uzipPackagesResult: " << rhs.uzipPackagesResult(); break;
    case 256795369:
      os << "getuzipPackagesResultResult: " << rhs.getuzipPackagesResultResult(); break;
    case 194734422:
      os << "startPackagesVerifyResult: " << rhs.startPackagesVerifyResult(); break;
    case 4313302:
      os << "getPackagesVerifyResultResult: " << rhs.getPackagesVerifyResultResult(); break;
    case 178527735:
      os << "checkUpdateConditionResult: " << rhs.checkUpdateConditionResult(); break;
    case 200329384:
      os << "getCheckUpdateConditionResultResult: " << rhs.getCheckUpdateConditionResultResult(); break;
    case 136901098:
      os << "startUpdateResult: " << rhs.startUpdateResult(); break;
    case 29478526:
      os << "resumeUpdateResult: " << rhs.resumeUpdateResult(); break;
    case 251480810:
      os << "pauseUpdateResult: " << rhs.pauseUpdateResult(); break;
    case 66921313:
      os << "getUpdateProgressResult: " << rhs.getUpdateProgressResult(); break;
    case 146417871:
      os << "activateResult: " << rhs.activateResult(); break;
    case 170403593:
      os << "rollbackResult: " << rhs.rollbackResult(); break;
    case 31250426:
      os << "getRollbackProgressResult: " << rhs.getRollbackProgressResult(); break;
    case 126211184:
      os << "uploadLogResult: " << rhs.uploadLogResult(); break;
    default:
    {
      // Prevent compiler warnings
    }
  }
  return os;
}

std::ostream& operator<<(std::ostream& os, DucServiceInterface_Reply const& rhs)
{
  (void) rhs;
  os << "[";
  os << "header: " << rhs.header();
  os << ", data: " << rhs.data();
  os << "]";
  return os;
}

} //namespace ota_duc_service

} //namespace seres

namespace org{
namespace eclipse{
namespace cyclonedds{
namespace core{
namespace cdr{

template<>
::seres::ota_duc_service::ReturnCode enum_conversion<::seres::ota_duc_service::ReturnCode>(uint32_t in) {
  switch (in) {
    default:
    case 0:
    return ::seres::ota_duc_service::ReturnCode::OK;
    break;
    case 1:
    return ::seres::ota_duc_service::ReturnCode::ERROR;
    break;
    case 2:
    return ::seres::ota_duc_service::ReturnCode::REFUSED;
    break;
  }
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_inventoryCollection_In>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<::seres::ota_duc_service::SelectedInventoryList>(), extensibility::ext_final, false));  //::inventory_list
  entity_properties_t::append_struct_contents(props, get_type_props<::seres::ota_duc_service::SelectedInventoryList>());  //internal contents of ::inventory_list

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_stopInventoryCollection_In>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<int8_t>(), extensibility::ext_final, false));  //::_default

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_getInventoryResult_In>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<int8_t>(), extensibility::ext_final, false));  //::_default

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_checkDownloadCondition_In>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<::seres::ota_duc_service::DownloadConditionLists>(), extensibility::ext_final, false));  //::conditions
  entity_properties_t::append_struct_contents(props, get_type_props<::seres::ota_duc_service::DownloadConditionLists>());  //internal contents of ::conditions

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_startDownload_In>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<::seres::ota_duc_service::DownloadTaskLists>(), extensibility::ext_final, false));  //::task_list
  entity_properties_t::append_struct_contents(props, get_type_props<::seres::ota_duc_service::DownloadTaskLists>());  //internal contents of ::task_list

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_downloadCtrl_In>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<::seres::ota_duc_service::DownloadCtrl>(), extensibility::ext_final, false));  //::download_command

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_getDownloadProgress_In>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<int8_t>(), extensibility::ext_final, false));  //::_default

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_uzipPackages_In>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<int8_t>(), extensibility::ext_final, false));  //::_default

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_getuzipPackagesResult_In>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<int8_t>(), extensibility::ext_final, false));  //::_default

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_startPackagesVerify_In>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<int8_t>(), extensibility::ext_final, false));  //::_default

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_getPackagesVerifyResult_In>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<int8_t>(), extensibility::ext_final, false));  //::_default

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_checkUpdateCondition_In>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<int8_t>(), extensibility::ext_final, false));  //::_default

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_getCheckUpdateConditionResult_In>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<int8_t>(), extensibility::ext_final, false));  //::_default

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_startUpdate_In>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<::seres::ota_duc_service::UpdateMode>(), extensibility::ext_final, false));  //::mode
  props.push_back(entity_properties_t(1, 1, false, get_bit_bound<::seres::ota_duc_service::UpdateDeviceList>(), extensibility::ext_final, false));  //::update_list
  entity_properties_t::append_struct_contents(props, get_type_props<::seres::ota_duc_service::UpdateDeviceList>());  //internal contents of ::update_list

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_resumeUpdate_In>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<int8_t>(), extensibility::ext_final, false));  //::_default

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_pauseUpdate_In>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<int8_t>(), extensibility::ext_final, false));  //::_default

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_getUpdateProgress_In>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<int8_t>(), extensibility::ext_final, false));  //::_default

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_activate_In>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<int8_t>(), extensibility::ext_final, false));  //::_default

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_rollback_In>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<::seres::ota_duc_service::RollbackComponentList>(), extensibility::ext_final, false));  //::component_list
  entity_properties_t::append_struct_contents(props, get_type_props<::seres::ota_duc_service::RollbackComponentList>());  //internal contents of ::component_list

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_getRollbackProgress_In>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<int8_t>(), extensibility::ext_final, false));  //::_default

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_uploadLog_In>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<int8_t>(), extensibility::ext_final, false));  //::_default

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_inventoryCollection_Out>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<int8_t>(), extensibility::ext_final, false));  //::_default

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_inventoryCollection_Result>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<::seres::ota_duc_service::DucServiceInterface_inventoryCollection_Out>(), extensibility::ext_final, false));  //::inventoryCollectionOut
  entity_properties_t::append_struct_contents(props, get_type_props<::seres::ota_duc_service::DucServiceInterface_inventoryCollection_Out>());  //internal contents of ::inventoryCollectionOut
  props.push_back(entity_properties_t(1, 1, false, get_bit_bound<::seres::ota_duc_service::ReturnCode>(), extensibility::ext_final, false));  //::_return

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_stopInventoryCollection_Out>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<int8_t>(), extensibility::ext_final, false));  //::_default

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_stopInventoryCollection_Result>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<::seres::ota_duc_service::DucServiceInterface_stopInventoryCollection_Out>(), extensibility::ext_final, false));  //::stopInventoryCollectionOut
  entity_properties_t::append_struct_contents(props, get_type_props<::seres::ota_duc_service::DucServiceInterface_stopInventoryCollection_Out>());  //internal contents of ::stopInventoryCollectionOut
  props.push_back(entity_properties_t(1, 1, false, get_bit_bound<::seres::ota_duc_service::ReturnCode>(), extensibility::ext_final, false));  //::_return

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_getInventoryResult_Out>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<::seres::ota_duc_service::InventoryResult>(), extensibility::ext_final, false));  //::inventory_list
  entity_properties_t::append_struct_contents(props, get_type_props<::seres::ota_duc_service::InventoryResult>());  //internal contents of ::inventory_list

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_getInventoryResult_Result>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<::seres::ota_duc_service::DucServiceInterface_getInventoryResult_Out>(), extensibility::ext_final, false));  //::getInventoryResultOut
  entity_properties_t::append_struct_contents(props, get_type_props<::seres::ota_duc_service::DucServiceInterface_getInventoryResult_Out>());  //internal contents of ::getInventoryResultOut
  props.push_back(entity_properties_t(1, 1, false, get_bit_bound<::seres::ota_duc_service::ReturnCode>(), extensibility::ext_final, false));  //::_return

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_checkDownloadCondition_Out>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<::seres::ota_duc_service::DownloadConditionResult>(), extensibility::ext_final, false));  //::condition_result

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_checkDownloadCondition_Result>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<::seres::ota_duc_service::DucServiceInterface_checkDownloadCondition_Out>(), extensibility::ext_final, false));  //::checkDownloadConditionOut
  entity_properties_t::append_struct_contents(props, get_type_props<::seres::ota_duc_service::DucServiceInterface_checkDownloadCondition_Out>());  //internal contents of ::checkDownloadConditionOut
  props.push_back(entity_properties_t(1, 1, false, get_bit_bound<::seres::ota_duc_service::ReturnCode>(), extensibility::ext_final, false));  //::_return

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_startDownload_Out>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<int8_t>(), extensibility::ext_final, false));  //::_default

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_startDownload_Result>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<::seres::ota_duc_service::DucServiceInterface_startDownload_Out>(), extensibility::ext_final, false));  //::startDownloadOut
  entity_properties_t::append_struct_contents(props, get_type_props<::seres::ota_duc_service::DucServiceInterface_startDownload_Out>());  //internal contents of ::startDownloadOut
  props.push_back(entity_properties_t(1, 1, false, get_bit_bound<::seres::ota_duc_service::ReturnCode>(), extensibility::ext_final, false));  //::_return

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_downloadCtrl_Out>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<int8_t>(), extensibility::ext_final, false));  //::_default

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_downloadCtrl_Result>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<::seres::ota_duc_service::DucServiceInterface_downloadCtrl_Out>(), extensibility::ext_final, false));  //::downloadCtrlOut
  entity_properties_t::append_struct_contents(props, get_type_props<::seres::ota_duc_service::DucServiceInterface_downloadCtrl_Out>());  //internal contents of ::downloadCtrlOut
  props.push_back(entity_properties_t(1, 1, false, get_bit_bound<::seres::ota_duc_service::ReturnCode>(), extensibility::ext_final, false));  //::_return

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_getDownloadProgress_Out>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<::seres::ota_duc_service::DownloadProgress>(), extensibility::ext_final, false));  //::download_progress
  entity_properties_t::append_struct_contents(props, get_type_props<::seres::ota_duc_service::DownloadProgress>());  //internal contents of ::download_progress

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_getDownloadProgress_Result>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<::seres::ota_duc_service::DucServiceInterface_getDownloadProgress_Out>(), extensibility::ext_final, false));  //::getDownloadProgressOut
  entity_properties_t::append_struct_contents(props, get_type_props<::seres::ota_duc_service::DucServiceInterface_getDownloadProgress_Out>());  //internal contents of ::getDownloadProgressOut
  props.push_back(entity_properties_t(1, 1, false, get_bit_bound<::seres::ota_duc_service::ReturnCode>(), extensibility::ext_final, false));  //::_return

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_uzipPackages_Out>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<int8_t>(), extensibility::ext_final, false));  //::_default

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_uzipPackages_Result>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<::seres::ota_duc_service::DucServiceInterface_uzipPackages_Out>(), extensibility::ext_final, false));  //::uzipPackagesOut
  entity_properties_t::append_struct_contents(props, get_type_props<::seres::ota_duc_service::DucServiceInterface_uzipPackages_Out>());  //internal contents of ::uzipPackagesOut
  props.push_back(entity_properties_t(1, 1, false, get_bit_bound<::seres::ota_duc_service::ReturnCode>(), extensibility::ext_final, false));  //::_return

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_getuzipPackagesResult_Out>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<::seres::ota_duc_service::UzipPackagesResult>(), extensibility::ext_final, false));  //::uzip_Result
  entity_properties_t::append_struct_contents(props, get_type_props<::seres::ota_duc_service::UzipPackagesResult>());  //internal contents of ::uzip_Result

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_getuzipPackagesResult_Result>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<::seres::ota_duc_service::DucServiceInterface_getuzipPackagesResult_Out>(), extensibility::ext_final, false));  //::getuzipPackagesResultOut
  entity_properties_t::append_struct_contents(props, get_type_props<::seres::ota_duc_service::DucServiceInterface_getuzipPackagesResult_Out>());  //internal contents of ::getuzipPackagesResultOut
  props.push_back(entity_properties_t(1, 1, false, get_bit_bound<::seres::ota_duc_service::ReturnCode>(), extensibility::ext_final, false));  //::_return

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_startPackagesVerify_Out>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<int8_t>(), extensibility::ext_final, false));  //::_default

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_startPackagesVerify_Result>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<::seres::ota_duc_service::DucServiceInterface_startPackagesVerify_Out>(), extensibility::ext_final, false));  //::startPackagesVerifyOut
  entity_properties_t::append_struct_contents(props, get_type_props<::seres::ota_duc_service::DucServiceInterface_startPackagesVerify_Out>());  //internal contents of ::startPackagesVerifyOut
  props.push_back(entity_properties_t(1, 1, false, get_bit_bound<::seres::ota_duc_service::ReturnCode>(), extensibility::ext_final, false));  //::_return

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_getPackagesVerifyResult_Out>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<::seres::ota_duc_service::PackagesVerifyResult>(), extensibility::ext_final, false));  //::verify_Result
  entity_properties_t::append_struct_contents(props, get_type_props<::seres::ota_duc_service::PackagesVerifyResult>());  //internal contents of ::verify_Result

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_getPackagesVerifyResult_Result>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<::seres::ota_duc_service::DucServiceInterface_getPackagesVerifyResult_Out>(), extensibility::ext_final, false));  //::getPackagesVerifyResultOut
  entity_properties_t::append_struct_contents(props, get_type_props<::seres::ota_duc_service::DucServiceInterface_getPackagesVerifyResult_Out>());  //internal contents of ::getPackagesVerifyResultOut
  props.push_back(entity_properties_t(1, 1, false, get_bit_bound<::seres::ota_duc_service::ReturnCode>(), extensibility::ext_final, false));  //::_return

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_checkUpdateCondition_Out>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<int8_t>(), extensibility::ext_final, false));  //::_default

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_checkUpdateCondition_Result>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<::seres::ota_duc_service::DucServiceInterface_checkUpdateCondition_Out>(), extensibility::ext_final, false));  //::checkUpdateConditionOut
  entity_properties_t::append_struct_contents(props, get_type_props<::seres::ota_duc_service::DucServiceInterface_checkUpdateCondition_Out>());  //internal contents of ::checkUpdateConditionOut
  props.push_back(entity_properties_t(1, 1, false, get_bit_bound<::seres::ota_duc_service::ReturnCode>(), extensibility::ext_final, false));  //::_return

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_getCheckUpdateConditionResult_Out>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<::seres::ota_duc_service::CheckUpdateConditionResult>(), extensibility::ext_final, false));  //::checkcondition_Result
  entity_properties_t::append_struct_contents(props, get_type_props<::seres::ota_duc_service::CheckUpdateConditionResult>());  //internal contents of ::checkcondition_Result

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_getCheckUpdateConditionResult_Result>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<::seres::ota_duc_service::DucServiceInterface_getCheckUpdateConditionResult_Out>(), extensibility::ext_final, false));  //::getCheckUpdateConditionResultOut
  entity_properties_t::append_struct_contents(props, get_type_props<::seres::ota_duc_service::DucServiceInterface_getCheckUpdateConditionResult_Out>());  //internal contents of ::getCheckUpdateConditionResultOut
  props.push_back(entity_properties_t(1, 1, false, get_bit_bound<::seres::ota_duc_service::ReturnCode>(), extensibility::ext_final, false));  //::_return

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_startUpdate_Out>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<int8_t>(), extensibility::ext_final, false));  //::_default

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_startUpdate_Result>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<::seres::ota_duc_service::DucServiceInterface_startUpdate_Out>(), extensibility::ext_final, false));  //::startUpdateOut
  entity_properties_t::append_struct_contents(props, get_type_props<::seres::ota_duc_service::DucServiceInterface_startUpdate_Out>());  //internal contents of ::startUpdateOut
  props.push_back(entity_properties_t(1, 1, false, get_bit_bound<::seres::ota_duc_service::ReturnCode>(), extensibility::ext_final, false));  //::_return

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_resumeUpdate_Out>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<int8_t>(), extensibility::ext_final, false));  //::_default

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_resumeUpdate_Result>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<::seres::ota_duc_service::DucServiceInterface_resumeUpdate_Out>(), extensibility::ext_final, false));  //::resumeUpdateOut
  entity_properties_t::append_struct_contents(props, get_type_props<::seres::ota_duc_service::DucServiceInterface_resumeUpdate_Out>());  //internal contents of ::resumeUpdateOut
  props.push_back(entity_properties_t(1, 1, false, get_bit_bound<::seres::ota_duc_service::ReturnCode>(), extensibility::ext_final, false));  //::_return

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_pauseUpdate_Out>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<int8_t>(), extensibility::ext_final, false));  //::_default

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_pauseUpdate_Result>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<::seres::ota_duc_service::DucServiceInterface_pauseUpdate_Out>(), extensibility::ext_final, false));  //::pauseUpdateOut
  entity_properties_t::append_struct_contents(props, get_type_props<::seres::ota_duc_service::DucServiceInterface_pauseUpdate_Out>());  //internal contents of ::pauseUpdateOut
  props.push_back(entity_properties_t(1, 1, false, get_bit_bound<::seres::ota_duc_service::ReturnCode>(), extensibility::ext_final, false));  //::_return

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_getUpdateProgress_Out>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<::seres::ota_duc_service::UpdateProgress>(), extensibility::ext_final, false));  //::update_progress
  entity_properties_t::append_struct_contents(props, get_type_props<::seres::ota_duc_service::UpdateProgress>());  //internal contents of ::update_progress

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_getUpdateProgress_Result>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<::seres::ota_duc_service::DucServiceInterface_getUpdateProgress_Out>(), extensibility::ext_final, false));  //::getUpdateProgressOut
  entity_properties_t::append_struct_contents(props, get_type_props<::seres::ota_duc_service::DucServiceInterface_getUpdateProgress_Out>());  //internal contents of ::getUpdateProgressOut
  props.push_back(entity_properties_t(1, 1, false, get_bit_bound<::seres::ota_duc_service::ReturnCode>(), extensibility::ext_final, false));  //::_return

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_activate_Out>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<int8_t>(), extensibility::ext_final, false));  //::_default

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_activate_Result>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<::seres::ota_duc_service::DucServiceInterface_activate_Out>(), extensibility::ext_final, false));  //::activateOut
  entity_properties_t::append_struct_contents(props, get_type_props<::seres::ota_duc_service::DucServiceInterface_activate_Out>());  //internal contents of ::activateOut
  props.push_back(entity_properties_t(1, 1, false, get_bit_bound<::seres::ota_duc_service::ReturnCode>(), extensibility::ext_final, false));  //::_return

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_rollback_Out>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<int8_t>(), extensibility::ext_final, false));  //::_default

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_rollback_Result>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<::seres::ota_duc_service::DucServiceInterface_rollback_Out>(), extensibility::ext_final, false));  //::rollbackOut
  entity_properties_t::append_struct_contents(props, get_type_props<::seres::ota_duc_service::DucServiceInterface_rollback_Out>());  //internal contents of ::rollbackOut
  props.push_back(entity_properties_t(1, 1, false, get_bit_bound<::seres::ota_duc_service::ReturnCode>(), extensibility::ext_final, false));  //::_return

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_getRollbackProgress_Out>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<::seres::ota_duc_service::UpdateProgress>(), extensibility::ext_final, false));  //::update_progress
  entity_properties_t::append_struct_contents(props, get_type_props<::seres::ota_duc_service::UpdateProgress>());  //internal contents of ::update_progress

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_getRollbackProgress_Result>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<::seres::ota_duc_service::DucServiceInterface_getRollbackProgress_Out>(), extensibility::ext_final, false));  //::getRollbackProgressOut
  entity_properties_t::append_struct_contents(props, get_type_props<::seres::ota_duc_service::DucServiceInterface_getRollbackProgress_Out>());  //internal contents of ::getRollbackProgressOut
  props.push_back(entity_properties_t(1, 1, false, get_bit_bound<::seres::ota_duc_service::ReturnCode>(), extensibility::ext_final, false));  //::_return

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_uploadLog_Out>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<int8_t>(), extensibility::ext_final, false));  //::_default

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_uploadLog_Result>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<::seres::ota_duc_service::DucServiceInterface_uploadLog_Out>(), extensibility::ext_final, false));  //::uploadLogOut
  entity_properties_t::append_struct_contents(props, get_type_props<::seres::ota_duc_service::DucServiceInterface_uploadLog_Out>());  //internal contents of ::uploadLogOut
  props.push_back(entity_properties_t(1, 1, false, get_bit_bound<::seres::ota_duc_service::ReturnCode>(), extensibility::ext_final, false));  //::_return

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_Call>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_Request>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<::dds::rpc::RequestHeader>(), extensibility::ext_final, false));  //::header
  entity_properties_t::append_struct_contents(props, get_type_props<::dds::rpc::RequestHeader>());  //internal contents of ::header
  props.push_back(entity_properties_t(1, 1, false, get_bit_bound<::seres::ota_duc_service::DucServiceInterface_Call>(), extensibility::ext_final, false));  //::data

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_Return>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DucServiceInterface_Reply>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<::dds::rpc::ReplyHeader>(), extensibility::ext_final, false));  //::header
  entity_properties_t::append_struct_contents(props, get_type_props<::dds::rpc::ReplyHeader>());  //internal contents of ::header
  props.push_back(entity_properties_t(1, 1, false, get_bit_bound<::seres::ota_duc_service::DucServiceInterface_Return>(), extensibility::ext_final, false));  //::data

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

} //namespace cdr
} //namespace core
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

