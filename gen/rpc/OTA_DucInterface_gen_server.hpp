/****************************************************************

  Generated by Eclipse Cyclone DDS IDL to CXX Translator
  File name: /home/<USER>/dds/scom-idl/OTA/OTA_DucInterface_gen_server
  Source: OTA_DucInterface_gen_server.hpp
  Cyclone DDS: v0.11.0

*****************************************************************/
#ifndef DDSCXX_OTA_DUCINTERFACE_GEN_SERVER_HPP_46FD7474B46E62D7C8A5F4776E293F0F
#define DDSCXX_OTA_DUCINTERFACE_GEN_SERVER_HPP_46FD7474B46E62D7C8A5F4776E293F0F

#include "OTA_DucInterface.hpp"

#include "ServiceEndpoint.hpp"
namespace seres
{
namespace ota_duc_service
{
class Interface_DucServiceInterface_BASE{
public:
    virtual ReturnCode inventoryCollection(const seres::ota_duc_service::SelectedInventoryList &inventory_list) = 0;
    virtual ReturnCode stopInventoryCollection() = 0;
    virtual ReturnCode getInventoryResult(seres::ota_duc_service::InventoryResult &inventory_list) = 0;
    virtual ReturnCode checkDownloadCondition(const seres::ota_duc_service::DownloadConditionLists &conditions, seres::ota_duc_service::DownloadConditionResult &condition_result) = 0;
    virtual ReturnCode startDownload(const seres::ota_duc_service::DownloadTaskLists &task_list) = 0;
    virtual ReturnCode downloadCtrl(const seres::ota_duc_service::DownloadCtrl &download_command) = 0;
    virtual ReturnCode getDownloadProgress(seres::ota_duc_service::DownloadProgress &download_progress) = 0;
    virtual ReturnCode uzipPackages() = 0;
    virtual ReturnCode getuzipPackagesResult(seres::ota_duc_service::UzipPackagesResult &uzip_Result) = 0;
    virtual ReturnCode startPackagesVerify() = 0;
    virtual ReturnCode getPackagesVerifyResult(seres::ota_duc_service::PackagesVerifyResult &verify_Result) = 0;
    virtual ReturnCode checkUpdateCondition() = 0;
    virtual ReturnCode getCheckUpdateConditionResult(seres::ota_duc_service::CheckUpdateConditionResult &checkcondition_Result) = 0;
    virtual ReturnCode startUpdate(const seres::ota_duc_service::UpdateMode &mode, const seres::ota_duc_service::UpdateDeviceList &update_list) = 0;
    virtual ReturnCode resumeUpdate() = 0;
    virtual ReturnCode pauseUpdate() = 0;
    virtual ReturnCode getUpdateProgress(seres::ota_duc_service::UpdateProgress &update_progress) = 0;
    virtual ReturnCode activate() = 0;
    virtual ReturnCode rollback(const seres::ota_duc_service::RollbackComponentList &component_list) = 0;
    virtual ReturnCode getRollbackProgress(seres::ota_duc_service::UpdateProgress &update_progress) = 0;
    virtual ReturnCode uploadLog() = 0;
};

class DucServiceInterfaceDispatcher final {
public:
    using InterfaceType = Interface_DucServiceInterface_BASE;
    using RequestType = DucServiceInterface_Request;
    using ReplyType = DucServiceInterface_Reply;
    
static void dispatch_request(std::shared_ptr<InterfaceType> service_impl, ReplyType &result, const RequestType &request);

private:
    static void dispatch_inventoryCollection(std::shared_ptr<InterfaceType> service_impl, ReplyType &result, const RequestType &request);
    static void dispatch_stopInventoryCollection(std::shared_ptr<InterfaceType> service_impl, ReplyType &result, const RequestType &request);
    static void dispatch_getInventoryResult(std::shared_ptr<InterfaceType> service_impl, ReplyType &result, const RequestType &request);
    static void dispatch_checkDownloadCondition(std::shared_ptr<InterfaceType> service_impl, ReplyType &result, const RequestType &request);
    static void dispatch_startDownload(std::shared_ptr<InterfaceType> service_impl, ReplyType &result, const RequestType &request);
    static void dispatch_downloadCtrl(std::shared_ptr<InterfaceType> service_impl, ReplyType &result, const RequestType &request);
    static void dispatch_getDownloadProgress(std::shared_ptr<InterfaceType> service_impl, ReplyType &result, const RequestType &request);
    static void dispatch_uzipPackages(std::shared_ptr<InterfaceType> service_impl, ReplyType &result, const RequestType &request);
    static void dispatch_getuzipPackagesResult(std::shared_ptr<InterfaceType> service_impl, ReplyType &result, const RequestType &request);
    static void dispatch_startPackagesVerify(std::shared_ptr<InterfaceType> service_impl, ReplyType &result, const RequestType &request);
    static void dispatch_getPackagesVerifyResult(std::shared_ptr<InterfaceType> service_impl, ReplyType &result, const RequestType &request);
    static void dispatch_checkUpdateCondition(std::shared_ptr<InterfaceType> service_impl, ReplyType &result, const RequestType &request);
    static void dispatch_getCheckUpdateConditionResult(std::shared_ptr<InterfaceType> service_impl, ReplyType &result, const RequestType &request);
    static void dispatch_startUpdate(std::shared_ptr<InterfaceType> service_impl, ReplyType &result, const RequestType &request);
    static void dispatch_resumeUpdate(std::shared_ptr<InterfaceType> service_impl, ReplyType &result, const RequestType &request);
    static void dispatch_pauseUpdate(std::shared_ptr<InterfaceType> service_impl, ReplyType &result, const RequestType &request);
    static void dispatch_getUpdateProgress(std::shared_ptr<InterfaceType> service_impl, ReplyType &result, const RequestType &request);
    static void dispatch_activate(std::shared_ptr<InterfaceType> service_impl, ReplyType &result, const RequestType &request);
    static void dispatch_rollback(std::shared_ptr<InterfaceType> service_impl, ReplyType &result, const RequestType &request);
    static void dispatch_getRollbackProgress(std::shared_ptr<InterfaceType> service_impl, ReplyType &result, const RequestType &request);
    static void dispatch_uploadLog(std::shared_ptr<InterfaceType> service_impl, ReplyType &result, const RequestType &request);
};

using DucServiceInterfaceService = dds::rpc::ServiceEndpoint<DucServiceInterfaceDispatcher>;
} //namespace ota_duc_service

} //namespace seres

#endif // DDSCXX_OTA_DUCINTERFACE_GEN_SERVER_HPP_46FD7474B46E62D7C8A5F4776E293F0F
