/****************************************************************

  Generated by Eclipse Cyclone DDS IDL to CXX Translator
  File name: /home/<USER>/dds/scom-idl/OTA/OTA_DucInterface_gen_client
  Source: OTA_DucInterface_gen_client.cpp
  Cyclone DDS: v0.11.0

*****************************************************************/
#include "OTA_DucInterface_gen_client.hpp"

namespace seres
{
namespace ota_duc_service
{

DucServiceInterfaceClient::DucServiceInterfaceClient(const dds::rpc::ClientParams &param):
     DucServiceInterfaceClientEndpoint(param){}


ReturnCode DucServiceInterfaceClient::inventoryCollection(const seres::ota_duc_service::SelectedInventoryList &inventory_list)
 {
 
     DucServiceInterface_Call inventoryCollectionCall;
     inventoryCollectionCall.inventoryCollectionIn(DucServiceInterface_inventoryCollection_In(inventory_list));
 
     DucServiceInterface_Request request;

     //todo 增加校验头
     uint64_t seqnum = get_sequenceNumber();
     request.data(inventoryCollectionCall);

     send_request(request);
     set_sequenceNumber(++seqnum);
     /*recv reply*/
     DucServiceInterface_Reply reply = receive_reply();

     ReturnCode ret;

     //todo 增加校验头
     switch(reply.data()._d()){
        case DucServiceInterface_inventoryCollection_HASH:
           ret = reply.data().inventoryCollectionResult()._return();
           break;
        default:
           throw dds::core::Error("[DucServiceInterfaceClient]--> call inventoryCollection unkown _d() value");
           break;
     }

     return ret;
}

ReturnCode DucServiceInterfaceClient::stopInventoryCollection()
 {
 
     DucServiceInterface_Call stopInventoryCollectionCall;
     stopInventoryCollectionCall.stopInventoryCollectionIn(DucServiceInterface_stopInventoryCollection_In());
 
     DucServiceInterface_Request request;

     //todo 增加校验头
     uint64_t seqnum = get_sequenceNumber();
     request.data(stopInventoryCollectionCall);

     send_request(request);
     set_sequenceNumber(++seqnum);
     /*recv reply*/
     DucServiceInterface_Reply reply = receive_reply();

     ReturnCode ret;

     //todo 增加校验头
     switch(reply.data()._d()){
        case DucServiceInterface_stopInventoryCollection_HASH:
           ret = reply.data().stopInventoryCollectionResult()._return();
           break;
        default:
           throw dds::core::Error("[DucServiceInterfaceClient]--> call stopInventoryCollection unkown _d() value");
           break;
     }

     return ret;
}

ReturnCode DucServiceInterfaceClient::getInventoryResult(seres::ota_duc_service::InventoryResult &inventory_list)
 {
 
     DucServiceInterface_Call getInventoryResultCall;
     getInventoryResultCall.getInventoryResultIn(DucServiceInterface_getInventoryResult_In());
 
     DucServiceInterface_Request request;

     //todo 增加校验头
     uint64_t seqnum = get_sequenceNumber();
     request.data(getInventoryResultCall);

     send_request(request);
     set_sequenceNumber(++seqnum);
     /*recv reply*/
     DucServiceInterface_Reply reply = receive_reply();

     ReturnCode ret;

     //todo 增加校验头
     switch(reply.data()._d()){
        case DucServiceInterface_getInventoryResult_HASH:
           inventory_list = reply.data().getInventoryResultResult().getInventoryResultOut().inventory_list();
           ret = reply.data().getInventoryResultResult()._return();
           break;
        default:
           throw dds::core::Error("[DucServiceInterfaceClient]--> call getInventoryResult unkown _d() value");
           break;
     }

     return ret;
}

ReturnCode DucServiceInterfaceClient::checkDownloadCondition(const seres::ota_duc_service::DownloadConditionLists &conditions,seres::ota_duc_service::DownloadConditionResult &condition_result)
 {
 
     DucServiceInterface_Call checkDownloadConditionCall;
     checkDownloadConditionCall.checkDownloadConditionIn(DucServiceInterface_checkDownloadCondition_In(conditions));
 
     DucServiceInterface_Request request;

     //todo 增加校验头
     uint64_t seqnum = get_sequenceNumber();
     request.data(checkDownloadConditionCall);

     send_request(request);
     set_sequenceNumber(++seqnum);
     /*recv reply*/
     DucServiceInterface_Reply reply = receive_reply();

     ReturnCode ret;

     //todo 增加校验头
     switch(reply.data()._d()){
        case DucServiceInterface_checkDownloadCondition_HASH:
           condition_result = reply.data().checkDownloadConditionResult().checkDownloadConditionOut().condition_result();
           ret = reply.data().checkDownloadConditionResult()._return();
           break;
        default:
           throw dds::core::Error("[DucServiceInterfaceClient]--> call checkDownloadCondition unkown _d() value");
           break;
     }

     return ret;
}

ReturnCode DucServiceInterfaceClient::startDownload(const seres::ota_duc_service::DownloadTaskLists &task_list)
 {
 
     DucServiceInterface_Call startDownloadCall;
     startDownloadCall.startDownloadIn(DucServiceInterface_startDownload_In(task_list));
 
     DucServiceInterface_Request request;

     //todo 增加校验头
     uint64_t seqnum = get_sequenceNumber();
     request.data(startDownloadCall);

     send_request(request);
     set_sequenceNumber(++seqnum);
     /*recv reply*/
     DucServiceInterface_Reply reply = receive_reply();

     ReturnCode ret;

     //todo 增加校验头
     switch(reply.data()._d()){
        case DucServiceInterface_startDownload_HASH:
           ret = reply.data().startDownloadResult()._return();
           break;
        default:
           throw dds::core::Error("[DucServiceInterfaceClient]--> call startDownload unkown _d() value");
           break;
     }

     return ret;
}

ReturnCode DucServiceInterfaceClient::downloadCtrl(const seres::ota_duc_service::DownloadCtrl &download_command)
 {
 
     DucServiceInterface_Call downloadCtrlCall;
     downloadCtrlCall.downloadCtrlIn(DucServiceInterface_downloadCtrl_In(download_command));
 
     DucServiceInterface_Request request;

     //todo 增加校验头
     uint64_t seqnum = get_sequenceNumber();
     request.data(downloadCtrlCall);

     send_request(request);
     set_sequenceNumber(++seqnum);
     /*recv reply*/
     DucServiceInterface_Reply reply = receive_reply();

     ReturnCode ret;

     //todo 增加校验头
     switch(reply.data()._d()){
        case DucServiceInterface_downloadCtrl_HASH:
           ret = reply.data().downloadCtrlResult()._return();
           break;
        default:
           throw dds::core::Error("[DucServiceInterfaceClient]--> call downloadCtrl unkown _d() value");
           break;
     }

     return ret;
}

ReturnCode DucServiceInterfaceClient::getDownloadProgress(seres::ota_duc_service::DownloadProgress &download_progress)
 {
 
     DucServiceInterface_Call getDownloadProgressCall;
     getDownloadProgressCall.getDownloadProgressIn(DucServiceInterface_getDownloadProgress_In());
 
     DucServiceInterface_Request request;

     //todo 增加校验头
     uint64_t seqnum = get_sequenceNumber();
     request.data(getDownloadProgressCall);

     send_request(request);
     set_sequenceNumber(++seqnum);
     /*recv reply*/
     DucServiceInterface_Reply reply = receive_reply();

     ReturnCode ret;

     //todo 增加校验头
     switch(reply.data()._d()){
        case DucServiceInterface_getDownloadProgress_HASH:
           download_progress = reply.data().getDownloadProgressResult().getDownloadProgressOut().download_progress();
           ret = reply.data().getDownloadProgressResult()._return();
           break;
        default:
           throw dds::core::Error("[DucServiceInterfaceClient]--> call getDownloadProgress unkown _d() value");
           break;
     }

     return ret;
}

ReturnCode DucServiceInterfaceClient::uzipPackages()
 {
 
     DucServiceInterface_Call uzipPackagesCall;
     uzipPackagesCall.uzipPackagesIn(DucServiceInterface_uzipPackages_In());
 
     DucServiceInterface_Request request;

     //todo 增加校验头
     uint64_t seqnum = get_sequenceNumber();
     request.data(uzipPackagesCall);

     send_request(request);
     set_sequenceNumber(++seqnum);
     /*recv reply*/
     DucServiceInterface_Reply reply = receive_reply();

     ReturnCode ret;

     //todo 增加校验头
     switch(reply.data()._d()){
        case DucServiceInterface_uzipPackages_HASH:
           ret = reply.data().uzipPackagesResult()._return();
           break;
        default:
           throw dds::core::Error("[DucServiceInterfaceClient]--> call uzipPackages unkown _d() value");
           break;
     }

     return ret;
}

ReturnCode DucServiceInterfaceClient::getuzipPackagesResult(seres::ota_duc_service::UzipPackagesResult &uzip_Result)
 {
 
     DucServiceInterface_Call getuzipPackagesResultCall;
     getuzipPackagesResultCall.getuzipPackagesResultIn(DucServiceInterface_getuzipPackagesResult_In());
 
     DucServiceInterface_Request request;

     //todo 增加校验头
     uint64_t seqnum = get_sequenceNumber();
     request.data(getuzipPackagesResultCall);

     send_request(request);
     set_sequenceNumber(++seqnum);
     /*recv reply*/
     DucServiceInterface_Reply reply = receive_reply();

     ReturnCode ret;

     //todo 增加校验头
     switch(reply.data()._d()){
        case DucServiceInterface_getuzipPackagesResult_HASH:
           uzip_Result = reply.data().getuzipPackagesResultResult().getuzipPackagesResultOut().uzip_Result();
           ret = reply.data().getuzipPackagesResultResult()._return();
           break;
        default:
           throw dds::core::Error("[DucServiceInterfaceClient]--> call getuzipPackagesResult unkown _d() value");
           break;
     }

     return ret;
}

ReturnCode DucServiceInterfaceClient::startPackagesVerify()
 {
 
     DucServiceInterface_Call startPackagesVerifyCall;
     startPackagesVerifyCall.startPackagesVerifyIn(DucServiceInterface_startPackagesVerify_In());
 
     DucServiceInterface_Request request;

     //todo 增加校验头
     uint64_t seqnum = get_sequenceNumber();
     request.data(startPackagesVerifyCall);

     send_request(request);
     set_sequenceNumber(++seqnum);
     /*recv reply*/
     DucServiceInterface_Reply reply = receive_reply();

     ReturnCode ret;

     //todo 增加校验头
     switch(reply.data()._d()){
        case DucServiceInterface_startPackagesVerify_HASH:
           ret = reply.data().startPackagesVerifyResult()._return();
           break;
        default:
           throw dds::core::Error("[DucServiceInterfaceClient]--> call startPackagesVerify unkown _d() value");
           break;
     }

     return ret;
}

ReturnCode DucServiceInterfaceClient::getPackagesVerifyResult(seres::ota_duc_service::PackagesVerifyResult &verify_Result)
 {
 
     DucServiceInterface_Call getPackagesVerifyResultCall;
     getPackagesVerifyResultCall.getPackagesVerifyResultIn(DucServiceInterface_getPackagesVerifyResult_In());
 
     DucServiceInterface_Request request;

     //todo 增加校验头
     uint64_t seqnum = get_sequenceNumber();
     request.data(getPackagesVerifyResultCall);

     send_request(request);
     set_sequenceNumber(++seqnum);
     /*recv reply*/
     DucServiceInterface_Reply reply = receive_reply();

     ReturnCode ret;

     //todo 增加校验头
     switch(reply.data()._d()){
        case DucServiceInterface_getPackagesVerifyResult_HASH:
           verify_Result = reply.data().getPackagesVerifyResultResult().getPackagesVerifyResultOut().verify_Result();
           ret = reply.data().getPackagesVerifyResultResult()._return();
           break;
        default:
           throw dds::core::Error("[DucServiceInterfaceClient]--> call getPackagesVerifyResult unkown _d() value");
           break;
     }

     return ret;
}

ReturnCode DucServiceInterfaceClient::checkUpdateCondition()
 {
 
     DucServiceInterface_Call checkUpdateConditionCall;
     checkUpdateConditionCall.checkUpdateConditionIn(DucServiceInterface_checkUpdateCondition_In());
 
     DucServiceInterface_Request request;

     //todo 增加校验头
     uint64_t seqnum = get_sequenceNumber();
     request.data(checkUpdateConditionCall);

     send_request(request);
     set_sequenceNumber(++seqnum);
     /*recv reply*/
     DucServiceInterface_Reply reply = receive_reply();

     ReturnCode ret;

     //todo 增加校验头
     switch(reply.data()._d()){
        case DucServiceInterface_checkUpdateCondition_HASH:
           ret = reply.data().checkUpdateConditionResult()._return();
           break;
        default:
           throw dds::core::Error("[DucServiceInterfaceClient]--> call checkUpdateCondition unkown _d() value");
           break;
     }

     return ret;
}

ReturnCode DucServiceInterfaceClient::getCheckUpdateConditionResult(seres::ota_duc_service::CheckUpdateConditionResult &checkcondition_Result)
 {
 
     DucServiceInterface_Call getCheckUpdateConditionResultCall;
     getCheckUpdateConditionResultCall.getCheckUpdateConditionResultIn(DucServiceInterface_getCheckUpdateConditionResult_In());
 
     DucServiceInterface_Request request;

     //todo 增加校验头
     uint64_t seqnum = get_sequenceNumber();
     request.data(getCheckUpdateConditionResultCall);

     send_request(request);
     set_sequenceNumber(++seqnum);
     /*recv reply*/
     DucServiceInterface_Reply reply = receive_reply();

     ReturnCode ret;

     //todo 增加校验头
     switch(reply.data()._d()){
        case DucServiceInterface_getCheckUpdateConditionResult_HASH:
           checkcondition_Result = reply.data().getCheckUpdateConditionResultResult().getCheckUpdateConditionResultOut().checkcondition_Result();
           ret = reply.data().getCheckUpdateConditionResultResult()._return();
           break;
        default:
           throw dds::core::Error("[DucServiceInterfaceClient]--> call getCheckUpdateConditionResult unkown _d() value");
           break;
     }

     return ret;
}

ReturnCode DucServiceInterfaceClient::startUpdate(const seres::ota_duc_service::UpdateMode &mode,const seres::ota_duc_service::UpdateDeviceList &update_list)
 {
 
     DucServiceInterface_Call startUpdateCall;
     startUpdateCall.startUpdateIn(DucServiceInterface_startUpdate_In(mode,update_list));
 
     DucServiceInterface_Request request;

     //todo 增加校验头
     uint64_t seqnum = get_sequenceNumber();
     request.data(startUpdateCall);

     send_request(request);
     set_sequenceNumber(++seqnum);
     /*recv reply*/
     DucServiceInterface_Reply reply = receive_reply();

     ReturnCode ret;

     //todo 增加校验头
     switch(reply.data()._d()){
        case DucServiceInterface_startUpdate_HASH:
           ret = reply.data().startUpdateResult()._return();
           break;
        default:
           throw dds::core::Error("[DucServiceInterfaceClient]--> call startUpdate unkown _d() value");
           break;
     }

     return ret;
}

ReturnCode DucServiceInterfaceClient::resumeUpdate()
 {
 
     DucServiceInterface_Call resumeUpdateCall;
     resumeUpdateCall.resumeUpdateIn(DucServiceInterface_resumeUpdate_In());
 
     DucServiceInterface_Request request;

     //todo 增加校验头
     uint64_t seqnum = get_sequenceNumber();
     request.data(resumeUpdateCall);

     send_request(request);
     set_sequenceNumber(++seqnum);
     /*recv reply*/
     DucServiceInterface_Reply reply = receive_reply();

     ReturnCode ret;

     //todo 增加校验头
     switch(reply.data()._d()){
        case DucServiceInterface_resumeUpdate_HASH:
           ret = reply.data().resumeUpdateResult()._return();
           break;
        default:
           throw dds::core::Error("[DucServiceInterfaceClient]--> call resumeUpdate unkown _d() value");
           break;
     }

     return ret;
}

ReturnCode DucServiceInterfaceClient::pauseUpdate()
 {
 
     DucServiceInterface_Call pauseUpdateCall;
     pauseUpdateCall.pauseUpdateIn(DucServiceInterface_pauseUpdate_In());
 
     DucServiceInterface_Request request;

     //todo 增加校验头
     uint64_t seqnum = get_sequenceNumber();
     request.data(pauseUpdateCall);

     send_request(request);
     set_sequenceNumber(++seqnum);
     /*recv reply*/
     DucServiceInterface_Reply reply = receive_reply();

     ReturnCode ret;

     //todo 增加校验头
     switch(reply.data()._d()){
        case DucServiceInterface_pauseUpdate_HASH:
           ret = reply.data().pauseUpdateResult()._return();
           break;
        default:
           throw dds::core::Error("[DucServiceInterfaceClient]--> call pauseUpdate unkown _d() value");
           break;
     }

     return ret;
}

ReturnCode DucServiceInterfaceClient::getUpdateProgress(seres::ota_duc_service::UpdateProgress &update_progress)
 {
 
     DucServiceInterface_Call getUpdateProgressCall;
     getUpdateProgressCall.getUpdateProgressIn(DucServiceInterface_getUpdateProgress_In());
 
     DucServiceInterface_Request request;

     //todo 增加校验头
     uint64_t seqnum = get_sequenceNumber();
     request.data(getUpdateProgressCall);

     send_request(request);
     set_sequenceNumber(++seqnum);
     /*recv reply*/
     DucServiceInterface_Reply reply = receive_reply();

     ReturnCode ret;

     //todo 增加校验头
     switch(reply.data()._d()){
        case DucServiceInterface_getUpdateProgress_HASH:
           update_progress = reply.data().getUpdateProgressResult().getUpdateProgressOut().update_progress();
           ret = reply.data().getUpdateProgressResult()._return();
           break;
        default:
           throw dds::core::Error("[DucServiceInterfaceClient]--> call getUpdateProgress unkown _d() value");
           break;
     }

     return ret;
}

ReturnCode DucServiceInterfaceClient::activate()
 {
 
     DucServiceInterface_Call activateCall;
     activateCall.activateIn(DucServiceInterface_activate_In());
 
     DucServiceInterface_Request request;

     //todo 增加校验头
     uint64_t seqnum = get_sequenceNumber();
     request.data(activateCall);

     send_request(request);
     set_sequenceNumber(++seqnum);
     /*recv reply*/
     DucServiceInterface_Reply reply = receive_reply();

     ReturnCode ret;

     //todo 增加校验头
     switch(reply.data()._d()){
        case DucServiceInterface_activate_HASH:
           ret = reply.data().activateResult()._return();
           break;
        default:
           throw dds::core::Error("[DucServiceInterfaceClient]--> call activate unkown _d() value");
           break;
     }

     return ret;
}

ReturnCode DucServiceInterfaceClient::rollback(const seres::ota_duc_service::RollbackComponentList &component_list)
 {
 
     DucServiceInterface_Call rollbackCall;
     rollbackCall.rollbackIn(DucServiceInterface_rollback_In(component_list));
 
     DucServiceInterface_Request request;

     //todo 增加校验头
     uint64_t seqnum = get_sequenceNumber();
     request.data(rollbackCall);

     send_request(request);
     set_sequenceNumber(++seqnum);
     /*recv reply*/
     DucServiceInterface_Reply reply = receive_reply();

     ReturnCode ret;

     //todo 增加校验头
     switch(reply.data()._d()){
        case DucServiceInterface_rollback_HASH:
           ret = reply.data().rollbackResult()._return();
           break;
        default:
           throw dds::core::Error("[DucServiceInterfaceClient]--> call rollback unkown _d() value");
           break;
     }

     return ret;
}

ReturnCode DucServiceInterfaceClient::getRollbackProgress(seres::ota_duc_service::UpdateProgress &update_progress)
 {
 
     DucServiceInterface_Call getRollbackProgressCall;
     getRollbackProgressCall.getRollbackProgressIn(DucServiceInterface_getRollbackProgress_In());
 
     DucServiceInterface_Request request;

     //todo 增加校验头
     uint64_t seqnum = get_sequenceNumber();
     request.data(getRollbackProgressCall);

     send_request(request);
     set_sequenceNumber(++seqnum);
     /*recv reply*/
     DucServiceInterface_Reply reply = receive_reply();

     ReturnCode ret;

     //todo 增加校验头
     switch(reply.data()._d()){
        case DucServiceInterface_getRollbackProgress_HASH:
           update_progress = reply.data().getRollbackProgressResult().getRollbackProgressOut().update_progress();
           ret = reply.data().getRollbackProgressResult()._return();
           break;
        default:
           throw dds::core::Error("[DucServiceInterfaceClient]--> call getRollbackProgress unkown _d() value");
           break;
     }

     return ret;
}

ReturnCode DucServiceInterfaceClient::uploadLog()
 {
 
     DucServiceInterface_Call uploadLogCall;
     uploadLogCall.uploadLogIn(DucServiceInterface_uploadLog_In());
 
     DucServiceInterface_Request request;

     //todo 增加校验头
     uint64_t seqnum = get_sequenceNumber();
     request.data(uploadLogCall);

     send_request(request);
     set_sequenceNumber(++seqnum);
     /*recv reply*/
     DucServiceInterface_Reply reply = receive_reply();

     ReturnCode ret;

     //todo 增加校验头
     switch(reply.data()._d()){
        case DucServiceInterface_uploadLog_HASH:
           ret = reply.data().uploadLogResult()._return();
           break;
        default:
           throw dds::core::Error("[DucServiceInterfaceClient]--> call uploadLog unkown _d() value");
           break;
     }

     return ret;
}

} //namespace ota_duc_service

} //namespace seres

