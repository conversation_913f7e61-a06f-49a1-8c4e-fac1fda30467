/****************************************************************

  Generated by Eclipse Cyclone DDS IDL to CXX Translator
  File name: /home/<USER>/dds/scom-idl/OTA/rpcCommon.idl
  Source: rpcCommon.cpp
  Cyclone DDS: v0.11.0

*****************************************************************/
#include "rpcCommon.hpp"

#include <org/eclipse/cyclonedds/util/ostream_operators.hpp>

namespace dds
{
namespace rpc
{
std::ostream& operator<<(std::ostream& os, EntityId_t const& rhs)
{
  (void) rhs;
  os << "[";
  os << "entityKey: " << rhs.entityKey();
  os << ", entityKind: " << rhs.entityKind();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, GUID_t const& rhs)
{
  (void) rhs;
  os << "[";
  os << "guidPrefix: " << rhs.guidPrefix();
  os << ", entityId: " << rhs.entityId();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, SequenceNumber_t const& rhs)
{
  (void) rhs;
  os << "[";
  os << "high: " << rhs.high();
  os << ", low: " << rhs.low();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, SampleIdentity const& rhs)
{
  (void) rhs;
  os << "[";
  os << "writer_guid: " << rhs.writer_guid();
  os << ", sequence_number: " << rhs.sequence_number();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, RequestHeader const& rhs)
{
  (void) rhs;
  os << "[";
  os << "requestId: " << rhs.requestId();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, ReplyHeader const& rhs)
{
  (void) rhs;
  os << "[";
  os << "relatedRequestId: " << rhs.relatedRequestId();
  os << "]";
  return os;
}

} //namespace rpc

} //namespace dds

namespace org{
namespace eclipse{
namespace cyclonedds{
namespace core{
namespace cdr{

template<>
const propvec &get_type_props<::dds::rpc::EntityId_t>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<uint8_t>(), extensibility::ext_final, false));  //::entityKey
  props.push_back(entity_properties_t(1, 1, false, get_bit_bound<uint8_t>(), extensibility::ext_final, false));  //::entityKind

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::dds::rpc::GUID_t>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<uint8_t>(), extensibility::ext_final, false));  //::guidPrefix
  props.push_back(entity_properties_t(1, 1, false, get_bit_bound<::dds::rpc::EntityId_t>(), extensibility::ext_final, false));  //::entityId
  entity_properties_t::append_struct_contents(props, get_type_props<::dds::rpc::EntityId_t>());  //internal contents of ::entityId

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::dds::rpc::SequenceNumber_t>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<int32_t>(), extensibility::ext_final, false));  //::high
  props.push_back(entity_properties_t(1, 1, false, get_bit_bound<uint32_t>(), extensibility::ext_final, false));  //::low

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::dds::rpc::SampleIdentity>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<::dds::rpc::GUID_t>(), extensibility::ext_final, false));  //::writer_guid
  entity_properties_t::append_struct_contents(props, get_type_props<::dds::rpc::GUID_t>());  //internal contents of ::writer_guid
  props.push_back(entity_properties_t(1, 1, false, get_bit_bound<::dds::rpc::SequenceNumber_t>(), extensibility::ext_final, false));  //::sequence_number
  entity_properties_t::append_struct_contents(props, get_type_props<::dds::rpc::SequenceNumber_t>());  //internal contents of ::sequence_number

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::dds::rpc::RequestHeader>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<::dds::rpc::SampleIdentity>(), extensibility::ext_final, false));  //::requestId
  entity_properties_t::append_struct_contents(props, get_type_props<::dds::rpc::SampleIdentity>());  //internal contents of ::requestId

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::dds::rpc::ReplyHeader>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<::dds::rpc::SampleIdentity>(), extensibility::ext_final, false));  //::relatedRequestId
  entity_properties_t::append_struct_contents(props, get_type_props<::dds::rpc::SampleIdentity>());  //internal contents of ::relatedRequestId

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

} //namespace cdr
} //namespace core
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

