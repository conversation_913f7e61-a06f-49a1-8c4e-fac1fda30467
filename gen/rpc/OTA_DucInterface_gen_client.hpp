/****************************************************************

  Generated by Eclipse Cyclone DDS IDL to CXX Translator
  File name: /home/<USER>/dds/scom-idl/OTA/OTA_DucInterface_gen_client
  Source: OTA_DucInterface_gen_client.hpp
  Cyclone DDS: v0.11.0

*****************************************************************/
#ifndef DDSCXX_OTA_DUCINTERFACE_GEN_CLIENT_HPP_46FD7474B46E62D7C8A5F4776E293F0F
#define DDSCXX_OTA_DUCINTERFACE_GEN_CLIENT_HPP_46FD7474B46E62D7C8A5F4776E293F0F

#include "OTA_DucInterface.hpp"

#include "ClientParam.hpp"
#include "ClientEndpoint.hpp"
namespace seres
{
namespace ota_duc_service
{
using DucServiceInterfaceClientEndpoint = dds::rpc::ClientEndpoint<DucServiceInterface_Request, DucServiceInterface_Reply>;

class DucServiceInterfaceClient : public DucServiceInterfaceClientEndpoint
{
public:

    explicit DucServiceInterfaceClient(const dds::rpc::ClientParams &param);

    ReturnCode inventoryCollection(const seres::ota_duc_service::SelectedInventoryList &inventory_list);
    ReturnCode stopInventoryCollection();
    ReturnCode getInventoryResult(seres::ota_duc_service::InventoryResult &inventory_list);
    ReturnCode checkDownloadCondition(const seres::ota_duc_service::DownloadConditionLists &conditions, seres::ota_duc_service::DownloadConditionResult &condition_result);
    ReturnCode startDownload(const seres::ota_duc_service::DownloadTaskLists &task_list);
    ReturnCode downloadCtrl(const seres::ota_duc_service::DownloadCtrl &download_command);
    ReturnCode getDownloadProgress(seres::ota_duc_service::DownloadProgress &download_progress);
    ReturnCode uzipPackages();
    ReturnCode getuzipPackagesResult(seres::ota_duc_service::UzipPackagesResult &uzip_Result);
    ReturnCode startPackagesVerify();
    ReturnCode getPackagesVerifyResult(seres::ota_duc_service::PackagesVerifyResult &verify_Result);
    ReturnCode checkUpdateCondition();
    ReturnCode getCheckUpdateConditionResult(seres::ota_duc_service::CheckUpdateConditionResult &checkcondition_Result);
    ReturnCode startUpdate(const seres::ota_duc_service::UpdateMode &mode, const seres::ota_duc_service::UpdateDeviceList &update_list);
    ReturnCode resumeUpdate();
    ReturnCode pauseUpdate();
    ReturnCode getUpdateProgress(seres::ota_duc_service::UpdateProgress &update_progress);
    ReturnCode activate();
    ReturnCode rollback(const seres::ota_duc_service::RollbackComponentList &component_list);
    ReturnCode getRollbackProgress(seres::ota_duc_service::UpdateProgress &update_progress);
    ReturnCode uploadLog();
};

} //namespace ota_duc_service

} //namespace seres

#endif // DDSCXX_OTA_DUCINTERFACE_GEN_CLIENT_HPP_46FD7474B46E62D7C8A5F4776E293F0F
