/****************************************************************

  Generated by Eclipse Cyclone DDS IDL to CXX Translator
  File name: /home/<USER>/dds/scom-idl/OTA/OTA_DucInterface_gen_server
  Source: OTA_DucInterface_gen_server.cpp
  Cyclone DDS: v0.11.0

*****************************************************************/
#include "OTA_DucInterface_gen_server.hpp"

namespace seres
{
namespace ota_duc_service
{

void DucServiceInterfaceDispatcher::dispatch_request(std::shared_ptr<InterfaceType> service_impl, ReplyType &result, const RequestType &request)
{
    result.header().relatedRequestId(request.header().requestId());
    switch(request.data()._d()){
        case DucServiceInterface_inventoryCollection_HASH:
            dispatch_inventoryCollection(service_impl, result, request);
            break;
        case DucServiceInterface_stopInventoryCollection_HASH:
            dispatch_stopInventoryCollection(service_impl, result, request);
            break;
        case DucServiceInterface_getInventoryResult_HASH:
            dispatch_getInventoryResult(service_impl, result, request);
            break;
        case DucServiceInterface_checkDownloadCondition_HASH:
            dispatch_checkDownloadCondition(service_impl, result, request);
            break;
        case DucServiceInterface_startDownload_HASH:
            dispatch_startDownload(service_impl, result, request);
            break;
        case DucServiceInterface_downloadCtrl_HASH:
            dispatch_downloadCtrl(service_impl, result, request);
            break;
        case DucServiceInterface_getDownloadProgress_HASH:
            dispatch_getDownloadProgress(service_impl, result, request);
            break;
        case DucServiceInterface_uzipPackages_HASH:
            dispatch_uzipPackages(service_impl, result, request);
            break;
        case DucServiceInterface_getuzipPackagesResult_HASH:
            dispatch_getuzipPackagesResult(service_impl, result, request);
            break;
        case DucServiceInterface_startPackagesVerify_HASH:
            dispatch_startPackagesVerify(service_impl, result, request);
            break;
        case DucServiceInterface_getPackagesVerifyResult_HASH:
            dispatch_getPackagesVerifyResult(service_impl, result, request);
            break;
        case DucServiceInterface_checkUpdateCondition_HASH:
            dispatch_checkUpdateCondition(service_impl, result, request);
            break;
        case DucServiceInterface_getCheckUpdateConditionResult_HASH:
            dispatch_getCheckUpdateConditionResult(service_impl, result, request);
            break;
        case DucServiceInterface_startUpdate_HASH:
            dispatch_startUpdate(service_impl, result, request);
            break;
        case DucServiceInterface_resumeUpdate_HASH:
            dispatch_resumeUpdate(service_impl, result, request);
            break;
        case DucServiceInterface_pauseUpdate_HASH:
            dispatch_pauseUpdate(service_impl, result, request);
            break;
        case DucServiceInterface_getUpdateProgress_HASH:
            dispatch_getUpdateProgress(service_impl, result, request);
            break;
        case DucServiceInterface_activate_HASH:
            dispatch_activate(service_impl, result, request);
            break;
        case DucServiceInterface_rollback_HASH:
            dispatch_rollback(service_impl, result, request);
            break;
        case DucServiceInterface_getRollbackProgress_HASH:
            dispatch_getRollbackProgress(service_impl, result, request);
            break;
        case DucServiceInterface_uploadLog_HASH:
            dispatch_uploadLog(service_impl, result, request);
            break;
        default:
            result.data()._d(0);
            break;
    }
}

void DucServiceInterfaceDispatcher::dispatch_inventoryCollection(std::shared_ptr<InterfaceType> service_impl, ReplyType &result, const RequestType &request)
{
    try{
        auto ret = service_impl->inventoryCollection(request.data().inventoryCollectionIn().inventory_list());
        ota_duc_service::DucServiceInterface_inventoryCollection_Out inventoryCollectionOut;

        ota_duc_service::DucServiceInterface_inventoryCollection_Result inventoryCollectionResult(inventoryCollectionOut, ret);
        ota_duc_service::DucServiceInterface_Return DucServiceInterfaceReturn;

        DucServiceInterfaceReturn.inventoryCollectionResult(inventoryCollectionResult);

        result.data(DucServiceInterfaceReturn);
    }catch(const std::exception &ex){
        std::cout << "dispatch_inventoryCollection error" << std::endl;
    }
}

void DucServiceInterfaceDispatcher::dispatch_stopInventoryCollection(std::shared_ptr<InterfaceType> service_impl, ReplyType &result, const RequestType &request)
{
    try{
        auto ret = service_impl->stopInventoryCollection();
        ota_duc_service::DucServiceInterface_stopInventoryCollection_Out stopInventoryCollectionOut;

        ota_duc_service::DucServiceInterface_stopInventoryCollection_Result stopInventoryCollectionResult(stopInventoryCollectionOut, ret);
        ota_duc_service::DucServiceInterface_Return DucServiceInterfaceReturn;

        DucServiceInterfaceReturn.stopInventoryCollectionResult(stopInventoryCollectionResult);

        result.data(DucServiceInterfaceReturn);
    }catch(const std::exception &ex){
        std::cout << "dispatch_stopInventoryCollection error" << std::endl;
    }
}

void DucServiceInterfaceDispatcher::dispatch_getInventoryResult(std::shared_ptr<InterfaceType> service_impl, ReplyType &result, const RequestType &request)
{
    try{
        seres::ota_duc_service::InventoryResult inventory_list;
        auto ret = service_impl->getInventoryResult(inventory_list);
        ota_duc_service::DucServiceInterface_getInventoryResult_Out getInventoryResultOut;
        getInventoryResultOut.inventory_list(inventory_list);

        ota_duc_service::DucServiceInterface_getInventoryResult_Result getInventoryResultResult(getInventoryResultOut, ret);
        ota_duc_service::DucServiceInterface_Return DucServiceInterfaceReturn;

        DucServiceInterfaceReturn.getInventoryResultResult(getInventoryResultResult);

        result.data(DucServiceInterfaceReturn);
    }catch(const std::exception &ex){
        std::cout << "dispatch_getInventoryResult error" << std::endl;
    }
}

void DucServiceInterfaceDispatcher::dispatch_checkDownloadCondition(std::shared_ptr<InterfaceType> service_impl, ReplyType &result, const RequestType &request)
{
    try{
        seres::ota_duc_service::DownloadConditionResult condition_result;
        auto ret = service_impl->checkDownloadCondition(request.data().checkDownloadConditionIn().conditions(), condition_result);
        ota_duc_service::DucServiceInterface_checkDownloadCondition_Out checkDownloadConditionOut;
        checkDownloadConditionOut.condition_result(condition_result);

        ota_duc_service::DucServiceInterface_checkDownloadCondition_Result checkDownloadConditionResult(checkDownloadConditionOut, ret);
        ota_duc_service::DucServiceInterface_Return DucServiceInterfaceReturn;

        DucServiceInterfaceReturn.checkDownloadConditionResult(checkDownloadConditionResult);

        result.data(DucServiceInterfaceReturn);
    }catch(const std::exception &ex){
        std::cout << "dispatch_checkDownloadCondition error" << std::endl;
    }
}

void DucServiceInterfaceDispatcher::dispatch_startDownload(std::shared_ptr<InterfaceType> service_impl, ReplyType &result, const RequestType &request)
{
    try{
        auto ret = service_impl->startDownload(request.data().startDownloadIn().task_list());
        ota_duc_service::DucServiceInterface_startDownload_Out startDownloadOut;

        ota_duc_service::DucServiceInterface_startDownload_Result startDownloadResult(startDownloadOut, ret);
        ota_duc_service::DucServiceInterface_Return DucServiceInterfaceReturn;

        DucServiceInterfaceReturn.startDownloadResult(startDownloadResult);

        result.data(DucServiceInterfaceReturn);
    }catch(const std::exception &ex){
        std::cout << "dispatch_startDownload error" << std::endl;
    }
}

void DucServiceInterfaceDispatcher::dispatch_downloadCtrl(std::shared_ptr<InterfaceType> service_impl, ReplyType &result, const RequestType &request)
{
    try{
        auto ret = service_impl->downloadCtrl(request.data().downloadCtrlIn().download_command());
        ota_duc_service::DucServiceInterface_downloadCtrl_Out downloadCtrlOut;

        ota_duc_service::DucServiceInterface_downloadCtrl_Result downloadCtrlResult(downloadCtrlOut, ret);
        ota_duc_service::DucServiceInterface_Return DucServiceInterfaceReturn;

        DucServiceInterfaceReturn.downloadCtrlResult(downloadCtrlResult);

        result.data(DucServiceInterfaceReturn);
    }catch(const std::exception &ex){
        std::cout << "dispatch_downloadCtrl error" << std::endl;
    }
}

void DucServiceInterfaceDispatcher::dispatch_getDownloadProgress(std::shared_ptr<InterfaceType> service_impl, ReplyType &result, const RequestType &request)
{
    try{
        seres::ota_duc_service::DownloadProgress download_progress;
        auto ret = service_impl->getDownloadProgress(download_progress);
        ota_duc_service::DucServiceInterface_getDownloadProgress_Out getDownloadProgressOut;
        getDownloadProgressOut.download_progress(download_progress);

        ota_duc_service::DucServiceInterface_getDownloadProgress_Result getDownloadProgressResult(getDownloadProgressOut, ret);
        ota_duc_service::DucServiceInterface_Return DucServiceInterfaceReturn;

        DucServiceInterfaceReturn.getDownloadProgressResult(getDownloadProgressResult);

        result.data(DucServiceInterfaceReturn);
    }catch(const std::exception &ex){
        std::cout << "dispatch_getDownloadProgress error" << std::endl;
    }
}

void DucServiceInterfaceDispatcher::dispatch_uzipPackages(std::shared_ptr<InterfaceType> service_impl, ReplyType &result, const RequestType &request)
{
    try{
        auto ret = service_impl->uzipPackages();
        ota_duc_service::DucServiceInterface_uzipPackages_Out uzipPackagesOut;

        ota_duc_service::DucServiceInterface_uzipPackages_Result uzipPackagesResult(uzipPackagesOut, ret);
        ota_duc_service::DucServiceInterface_Return DucServiceInterfaceReturn;

        DucServiceInterfaceReturn.uzipPackagesResult(uzipPackagesResult);

        result.data(DucServiceInterfaceReturn);
    }catch(const std::exception &ex){
        std::cout << "dispatch_uzipPackages error" << std::endl;
    }
}

void DucServiceInterfaceDispatcher::dispatch_getuzipPackagesResult(std::shared_ptr<InterfaceType> service_impl, ReplyType &result, const RequestType &request)
{
    try{
        seres::ota_duc_service::UzipPackagesResult uzip_Result;
        auto ret = service_impl->getuzipPackagesResult(uzip_Result);
        ota_duc_service::DucServiceInterface_getuzipPackagesResult_Out getuzipPackagesResultOut;
        getuzipPackagesResultOut.uzip_Result(uzip_Result);

        ota_duc_service::DucServiceInterface_getuzipPackagesResult_Result getuzipPackagesResultResult(getuzipPackagesResultOut, ret);
        ota_duc_service::DucServiceInterface_Return DucServiceInterfaceReturn;

        DucServiceInterfaceReturn.getuzipPackagesResultResult(getuzipPackagesResultResult);

        result.data(DucServiceInterfaceReturn);
    }catch(const std::exception &ex){
        std::cout << "dispatch_getuzipPackagesResult error" << std::endl;
    }
}

void DucServiceInterfaceDispatcher::dispatch_startPackagesVerify(std::shared_ptr<InterfaceType> service_impl, ReplyType &result, const RequestType &request)
{
    try{
        auto ret = service_impl->startPackagesVerify();
        ota_duc_service::DucServiceInterface_startPackagesVerify_Out startPackagesVerifyOut;

        ota_duc_service::DucServiceInterface_startPackagesVerify_Result startPackagesVerifyResult(startPackagesVerifyOut, ret);
        ota_duc_service::DucServiceInterface_Return DucServiceInterfaceReturn;

        DucServiceInterfaceReturn.startPackagesVerifyResult(startPackagesVerifyResult);

        result.data(DucServiceInterfaceReturn);
    }catch(const std::exception &ex){
        std::cout << "dispatch_startPackagesVerify error" << std::endl;
    }
}

void DucServiceInterfaceDispatcher::dispatch_getPackagesVerifyResult(std::shared_ptr<InterfaceType> service_impl, ReplyType &result, const RequestType &request)
{
    try{
        seres::ota_duc_service::PackagesVerifyResult verify_Result;
        auto ret = service_impl->getPackagesVerifyResult(verify_Result);
        ota_duc_service::DucServiceInterface_getPackagesVerifyResult_Out getPackagesVerifyResultOut;
        getPackagesVerifyResultOut.verify_Result(verify_Result);

        ota_duc_service::DucServiceInterface_getPackagesVerifyResult_Result getPackagesVerifyResultResult(getPackagesVerifyResultOut, ret);
        ota_duc_service::DucServiceInterface_Return DucServiceInterfaceReturn;

        DucServiceInterfaceReturn.getPackagesVerifyResultResult(getPackagesVerifyResultResult);

        result.data(DucServiceInterfaceReturn);
    }catch(const std::exception &ex){
        std::cout << "dispatch_getPackagesVerifyResult error" << std::endl;
    }
}

void DucServiceInterfaceDispatcher::dispatch_checkUpdateCondition(std::shared_ptr<InterfaceType> service_impl, ReplyType &result, const RequestType &request)
{
    try{
        auto ret = service_impl->checkUpdateCondition();
        ota_duc_service::DucServiceInterface_checkUpdateCondition_Out checkUpdateConditionOut;

        ota_duc_service::DucServiceInterface_checkUpdateCondition_Result checkUpdateConditionResult(checkUpdateConditionOut, ret);
        ota_duc_service::DucServiceInterface_Return DucServiceInterfaceReturn;

        DucServiceInterfaceReturn.checkUpdateConditionResult(checkUpdateConditionResult);

        result.data(DucServiceInterfaceReturn);
    }catch(const std::exception &ex){
        std::cout << "dispatch_checkUpdateCondition error" << std::endl;
    }
}

void DucServiceInterfaceDispatcher::dispatch_getCheckUpdateConditionResult(std::shared_ptr<InterfaceType> service_impl, ReplyType &result, const RequestType &request)
{
    try{
        seres::ota_duc_service::CheckUpdateConditionResult checkcondition_Result;
        auto ret = service_impl->getCheckUpdateConditionResult(checkcondition_Result);
        ota_duc_service::DucServiceInterface_getCheckUpdateConditionResult_Out getCheckUpdateConditionResultOut;
        getCheckUpdateConditionResultOut.checkcondition_Result(checkcondition_Result);

        ota_duc_service::DucServiceInterface_getCheckUpdateConditionResult_Result getCheckUpdateConditionResultResult(getCheckUpdateConditionResultOut, ret);
        ota_duc_service::DucServiceInterface_Return DucServiceInterfaceReturn;

        DucServiceInterfaceReturn.getCheckUpdateConditionResultResult(getCheckUpdateConditionResultResult);

        result.data(DucServiceInterfaceReturn);
    }catch(const std::exception &ex){
        std::cout << "dispatch_getCheckUpdateConditionResult error" << std::endl;
    }
}

void DucServiceInterfaceDispatcher::dispatch_startUpdate(std::shared_ptr<InterfaceType> service_impl, ReplyType &result, const RequestType &request)
{
    try{
        auto ret = service_impl->startUpdate(request.data().startUpdateIn().mode(), request.data().startUpdateIn().update_list());
        ota_duc_service::DucServiceInterface_startUpdate_Out startUpdateOut;

        ota_duc_service::DucServiceInterface_startUpdate_Result startUpdateResult(startUpdateOut, ret);
        ota_duc_service::DucServiceInterface_Return DucServiceInterfaceReturn;

        DucServiceInterfaceReturn.startUpdateResult(startUpdateResult);

        result.data(DucServiceInterfaceReturn);
    }catch(const std::exception &ex){
        std::cout << "dispatch_startUpdate error" << std::endl;
    }
}

void DucServiceInterfaceDispatcher::dispatch_resumeUpdate(std::shared_ptr<InterfaceType> service_impl, ReplyType &result, const RequestType &request)
{
    try{
        auto ret = service_impl->resumeUpdate();
        ota_duc_service::DucServiceInterface_resumeUpdate_Out resumeUpdateOut;

        ota_duc_service::DucServiceInterface_resumeUpdate_Result resumeUpdateResult(resumeUpdateOut, ret);
        ota_duc_service::DucServiceInterface_Return DucServiceInterfaceReturn;

        DucServiceInterfaceReturn.resumeUpdateResult(resumeUpdateResult);

        result.data(DucServiceInterfaceReturn);
    }catch(const std::exception &ex){
        std::cout << "dispatch_resumeUpdate error" << std::endl;
    }
}

void DucServiceInterfaceDispatcher::dispatch_pauseUpdate(std::shared_ptr<InterfaceType> service_impl, ReplyType &result, const RequestType &request)
{
    try{
        auto ret = service_impl->pauseUpdate();
        ota_duc_service::DucServiceInterface_pauseUpdate_Out pauseUpdateOut;

        ota_duc_service::DucServiceInterface_pauseUpdate_Result pauseUpdateResult(pauseUpdateOut, ret);
        ota_duc_service::DucServiceInterface_Return DucServiceInterfaceReturn;

        DucServiceInterfaceReturn.pauseUpdateResult(pauseUpdateResult);

        result.data(DucServiceInterfaceReturn);
    }catch(const std::exception &ex){
        std::cout << "dispatch_pauseUpdate error" << std::endl;
    }
}

void DucServiceInterfaceDispatcher::dispatch_getUpdateProgress(std::shared_ptr<InterfaceType> service_impl, ReplyType &result, const RequestType &request)
{
    try{
        seres::ota_duc_service::UpdateProgress update_progress;
        auto ret = service_impl->getUpdateProgress(update_progress);
        ota_duc_service::DucServiceInterface_getUpdateProgress_Out getUpdateProgressOut;
        getUpdateProgressOut.update_progress(update_progress);

        ota_duc_service::DucServiceInterface_getUpdateProgress_Result getUpdateProgressResult(getUpdateProgressOut, ret);
        ota_duc_service::DucServiceInterface_Return DucServiceInterfaceReturn;

        DucServiceInterfaceReturn.getUpdateProgressResult(getUpdateProgressResult);

        result.data(DucServiceInterfaceReturn);
    }catch(const std::exception &ex){
        std::cout << "dispatch_getUpdateProgress error" << std::endl;
    }
}

void DucServiceInterfaceDispatcher::dispatch_activate(std::shared_ptr<InterfaceType> service_impl, ReplyType &result, const RequestType &request)
{
    try{
        auto ret = service_impl->activate();
        ota_duc_service::DucServiceInterface_activate_Out activateOut;

        ota_duc_service::DucServiceInterface_activate_Result activateResult(activateOut, ret);
        ota_duc_service::DucServiceInterface_Return DucServiceInterfaceReturn;

        DucServiceInterfaceReturn.activateResult(activateResult);

        result.data(DucServiceInterfaceReturn);
    }catch(const std::exception &ex){
        std::cout << "dispatch_activate error" << std::endl;
    }
}

void DucServiceInterfaceDispatcher::dispatch_rollback(std::shared_ptr<InterfaceType> service_impl, ReplyType &result, const RequestType &request)
{
    try{
        auto ret = service_impl->rollback(request.data().rollbackIn().component_list());
        ota_duc_service::DucServiceInterface_rollback_Out rollbackOut;

        ota_duc_service::DucServiceInterface_rollback_Result rollbackResult(rollbackOut, ret);
        ota_duc_service::DucServiceInterface_Return DucServiceInterfaceReturn;

        DucServiceInterfaceReturn.rollbackResult(rollbackResult);

        result.data(DucServiceInterfaceReturn);
    }catch(const std::exception &ex){
        std::cout << "dispatch_rollback error" << std::endl;
    }
}

void DucServiceInterfaceDispatcher::dispatch_getRollbackProgress(std::shared_ptr<InterfaceType> service_impl, ReplyType &result, const RequestType &request)
{
    try{
        seres::ota_duc_service::UpdateProgress update_progress;
        auto ret = service_impl->getRollbackProgress(update_progress);
        ota_duc_service::DucServiceInterface_getRollbackProgress_Out getRollbackProgressOut;
        getRollbackProgressOut.update_progress(update_progress);

        ota_duc_service::DucServiceInterface_getRollbackProgress_Result getRollbackProgressResult(getRollbackProgressOut, ret);
        ota_duc_service::DucServiceInterface_Return DucServiceInterfaceReturn;

        DucServiceInterfaceReturn.getRollbackProgressResult(getRollbackProgressResult);

        result.data(DucServiceInterfaceReturn);
    }catch(const std::exception &ex){
        std::cout << "dispatch_getRollbackProgress error" << std::endl;
    }
}

void DucServiceInterfaceDispatcher::dispatch_uploadLog(std::shared_ptr<InterfaceType> service_impl, ReplyType &result, const RequestType &request)
{
    try{
        auto ret = service_impl->uploadLog();
        ota_duc_service::DucServiceInterface_uploadLog_Out uploadLogOut;

        ota_duc_service::DucServiceInterface_uploadLog_Result uploadLogResult(uploadLogOut, ret);
        ota_duc_service::DucServiceInterface_Return DucServiceInterfaceReturn;

        DucServiceInterfaceReturn.uploadLogResult(uploadLogResult);

        result.data(DucServiceInterfaceReturn);
    }catch(const std::exception &ex){
        std::cout << "dispatch_uploadLog error" << std::endl;
    }
}

} //namespace ota_duc_service

} //namespace seres

