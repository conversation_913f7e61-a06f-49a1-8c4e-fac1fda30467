/****************************************************************

  Generated by Eclipse Cyclone DDS IDL to CXX Translator
  File name: /home/<USER>/dds/scom-idl/OTA/rpcCommon.idl
  Source: rpcCommon.hpp
  Cyclone DDS: v0.11.0

*****************************************************************/
#ifndef DDSCXX_RPCCOMMON_HPP_F0D46936B4A4A1F5DD939BC1BAE0AAA2
#define DDSCXX_RPCCOMMON_HPP_F0D46936B4A4A1F5DD939BC1BAE0AAA2

#include <utility>
#include <ostream>
#include <cstdint>
#include <array>

namespace dds
{
namespace rpc
{
typedef std::array<uint8_t, 12> GuidPrefix_t;

class EntityId_t
{
private:
 std::array<uint8_t, 3> entityKey_ = { };
 uint8_t entityKind_ = 0;

public:
  EntityId_t() = default;

  explicit EntityId_t(
    const std::array<uint8_t, 3>& entityKey,
    uint8_t entityKind) :
    entityKey_(entityKey),
    entityKind_(entityKind) { }

  const std::array<uint8_t, 3>& entityKey() const { return this->entityKey_; }
  std::array<uint8_t, 3>& entityKey() { return this->entityKey_; }
  void entityKey(const std::array<uint8_t, 3>& _val_) { this->entityKey_ = _val_; }
  void entityKey(std::array<uint8_t, 3>&& _val_) { this->entityKey_ = std::move(_val_); }
  uint8_t entityKind() const { return this->entityKind_; }
  uint8_t& entityKind() { return this->entityKind_; }
  void entityKind(uint8_t _val_) { this->entityKind_ = _val_; }

  bool operator==(const EntityId_t& _other) const
  {
    (void) _other;
    return entityKey_ == _other.entityKey_ &&
      entityKind_ == _other.entityKind_;
  }

  bool operator!=(const EntityId_t& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, EntityId_t const& rhs);

class GUID_t
{
private:
 std::array<uint8_t, 12> guidPrefix_ = { };
 ::dds::rpc::EntityId_t entityId_;

public:
  GUID_t() = default;

  explicit GUID_t(
    const std::array<uint8_t, 12>& guidPrefix,
    const ::dds::rpc::EntityId_t& entityId) :
    guidPrefix_(guidPrefix),
    entityId_(entityId) { }

  const std::array<uint8_t, 12>& guidPrefix() const { return this->guidPrefix_; }
  std::array<uint8_t, 12>& guidPrefix() { return this->guidPrefix_; }
  void guidPrefix(const std::array<uint8_t, 12>& _val_) { this->guidPrefix_ = _val_; }
  void guidPrefix(std::array<uint8_t, 12>&& _val_) { this->guidPrefix_ = std::move(_val_); }
  const ::dds::rpc::EntityId_t& entityId() const { return this->entityId_; }
  ::dds::rpc::EntityId_t& entityId() { return this->entityId_; }
  void entityId(const ::dds::rpc::EntityId_t& _val_) { this->entityId_ = _val_; }
  void entityId(::dds::rpc::EntityId_t&& _val_) { this->entityId_ = std::move(_val_); }

  bool operator==(const GUID_t& _other) const
  {
    (void) _other;
    return guidPrefix_ == _other.guidPrefix_ &&
      entityId_ == _other.entityId_;
  }

  bool operator!=(const GUID_t& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, GUID_t const& rhs);

class SequenceNumber_t
{
private:
 int32_t high_ = 0;
 uint32_t low_ = 0;

public:
  SequenceNumber_t() = default;

  explicit SequenceNumber_t(
    int32_t high,
    uint32_t low) :
    high_(high),
    low_(low) { }

  int32_t high() const { return this->high_; }
  int32_t& high() { return this->high_; }
  void high(int32_t _val_) { this->high_ = _val_; }
  uint32_t low() const { return this->low_; }
  uint32_t& low() { return this->low_; }
  void low(uint32_t _val_) { this->low_ = _val_; }

  bool operator==(const SequenceNumber_t& _other) const
  {
    (void) _other;
    return high_ == _other.high_ &&
      low_ == _other.low_;
  }

  bool operator!=(const SequenceNumber_t& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, SequenceNumber_t const& rhs);

class SampleIdentity
{
private:
 ::dds::rpc::GUID_t writer_guid_;
 ::dds::rpc::SequenceNumber_t sequence_number_;

public:
  SampleIdentity() = default;

  explicit SampleIdentity(
    const ::dds::rpc::GUID_t& writer_guid,
    const ::dds::rpc::SequenceNumber_t& sequence_number) :
    writer_guid_(writer_guid),
    sequence_number_(sequence_number) { }

  const ::dds::rpc::GUID_t& writer_guid() const { return this->writer_guid_; }
  ::dds::rpc::GUID_t& writer_guid() { return this->writer_guid_; }
  void writer_guid(const ::dds::rpc::GUID_t& _val_) { this->writer_guid_ = _val_; }
  void writer_guid(::dds::rpc::GUID_t&& _val_) { this->writer_guid_ = std::move(_val_); }
  const ::dds::rpc::SequenceNumber_t& sequence_number() const { return this->sequence_number_; }
  ::dds::rpc::SequenceNumber_t& sequence_number() { return this->sequence_number_; }
  void sequence_number(const ::dds::rpc::SequenceNumber_t& _val_) { this->sequence_number_ = _val_; }
  void sequence_number(::dds::rpc::SequenceNumber_t&& _val_) { this->sequence_number_ = std::move(_val_); }

  bool operator==(const SampleIdentity& _other) const
  {
    (void) _other;
    return writer_guid_ == _other.writer_guid_ &&
      sequence_number_ == _other.sequence_number_;
  }

  bool operator!=(const SampleIdentity& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, SampleIdentity const& rhs);

class RequestHeader
{
private:
 ::dds::rpc::SampleIdentity requestId_;

public:
  RequestHeader() = default;

  explicit RequestHeader(
    const ::dds::rpc::SampleIdentity& requestId) :
    requestId_(requestId) { }

  const ::dds::rpc::SampleIdentity& requestId() const { return this->requestId_; }
  ::dds::rpc::SampleIdentity& requestId() { return this->requestId_; }
  void requestId(const ::dds::rpc::SampleIdentity& _val_) { this->requestId_ = _val_; }
  void requestId(::dds::rpc::SampleIdentity&& _val_) { this->requestId_ = std::move(_val_); }

  bool operator==(const RequestHeader& _other) const
  {
    (void) _other;
    return requestId_ == _other.requestId_;
  }

  bool operator!=(const RequestHeader& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, RequestHeader const& rhs);

class ReplyHeader
{
private:
 ::dds::rpc::SampleIdentity relatedRequestId_;

public:
  ReplyHeader() = default;

  explicit ReplyHeader(
    const ::dds::rpc::SampleIdentity& relatedRequestId) :
    relatedRequestId_(relatedRequestId) { }

  const ::dds::rpc::SampleIdentity& relatedRequestId() const { return this->relatedRequestId_; }
  ::dds::rpc::SampleIdentity& relatedRequestId() { return this->relatedRequestId_; }
  void relatedRequestId(const ::dds::rpc::SampleIdentity& _val_) { this->relatedRequestId_ = _val_; }
  void relatedRequestId(::dds::rpc::SampleIdentity&& _val_) { this->relatedRequestId_ = std::move(_val_); }

  bool operator==(const ReplyHeader& _other) const
  {
    (void) _other;
    return relatedRequestId_ == _other.relatedRequestId_;
  }

  bool operator!=(const ReplyHeader& _other) const
  {
    return !(*this == _other);
  }

};

std::ostream& operator<<(std::ostream& os, ReplyHeader const& rhs);

} //namespace rpc

} //namespace dds

#include "dds/topic/TopicTraits.hpp"
#include "org/eclipse/cyclonedds/topic/datatopic.hpp"

namespace org {
namespace eclipse {
namespace cyclonedds {
namespace topic {

template <> constexpr const char* TopicTraits<::dds::rpc::EntityId_t>::getTypeName()
{
  return "dds::rpc::EntityId_t";
}

template <> constexpr bool TopicTraits<::dds::rpc::EntityId_t>::isKeyless()
{
  return true;
}

template <> constexpr const char* TopicTraits<::dds::rpc::GUID_t>::getTypeName()
{
  return "dds::rpc::GUID_t";
}

template <> constexpr bool TopicTraits<::dds::rpc::GUID_t>::isKeyless()
{
  return true;
}

template <> constexpr const char* TopicTraits<::dds::rpc::SequenceNumber_t>::getTypeName()
{
  return "dds::rpc::SequenceNumber_t";
}

template <> constexpr bool TopicTraits<::dds::rpc::SequenceNumber_t>::isKeyless()
{
  return true;
}

template <> constexpr const char* TopicTraits<::dds::rpc::SampleIdentity>::getTypeName()
{
  return "dds::rpc::SampleIdentity";
}

template <> constexpr bool TopicTraits<::dds::rpc::SampleIdentity>::isKeyless()
{
  return true;
}

template <> constexpr const char* TopicTraits<::dds::rpc::RequestHeader>::getTypeName()
{
  return "dds::rpc::RequestHeader";
}

template <> constexpr bool TopicTraits<::dds::rpc::RequestHeader>::isKeyless()
{
  return true;
}

template <> constexpr const char* TopicTraits<::dds::rpc::ReplyHeader>::getTypeName()
{
  return "dds::rpc::ReplyHeader";
}

template <> constexpr bool TopicTraits<::dds::rpc::ReplyHeader>::isKeyless()
{
  return true;
}

} //namespace topic
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

namespace dds {
namespace topic {

template <>
struct topic_type_name<::dds::rpc::EntityId_t>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::dds::rpc::EntityId_t>::getTypeName();
    }
};

template <>
struct topic_type_name<::dds::rpc::GUID_t>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::dds::rpc::GUID_t>::getTypeName();
    }
};

template <>
struct topic_type_name<::dds::rpc::SequenceNumber_t>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::dds::rpc::SequenceNumber_t>::getTypeName();
    }
};

template <>
struct topic_type_name<::dds::rpc::SampleIdentity>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::dds::rpc::SampleIdentity>::getTypeName();
    }
};

template <>
struct topic_type_name<::dds::rpc::RequestHeader>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::dds::rpc::RequestHeader>::getTypeName();
    }
};

template <>
struct topic_type_name<::dds::rpc::ReplyHeader>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::dds::rpc::ReplyHeader>::getTypeName();
    }
};

}
}

REGISTER_TOPIC_TYPE(::dds::rpc::EntityId_t)
REGISTER_TOPIC_TYPE(::dds::rpc::GUID_t)
REGISTER_TOPIC_TYPE(::dds::rpc::SequenceNumber_t)
REGISTER_TOPIC_TYPE(::dds::rpc::SampleIdentity)
REGISTER_TOPIC_TYPE(::dds::rpc::RequestHeader)
REGISTER_TOPIC_TYPE(::dds::rpc::ReplyHeader)

namespace org{
namespace eclipse{
namespace cyclonedds{
namespace core{
namespace cdr{

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write__dds_rpc_GuidPrefix_t(T& streamer, const ::dds::rpc::GuidPrefix_t& instance) {
  (void)instance;
  member_id_set member_ids;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!write(streamer, instance[0], instance.size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
  return true;
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read__dds_rpc_GuidPrefix_t(T& streamer, ::dds::rpc::GuidPrefix_t& instance) {
  (void)instance;
  member_id_set member_ids;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!read(streamer, instance[0], instance.size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
  return true;
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move__dds_rpc_GuidPrefix_t(T& streamer, const ::dds::rpc::GuidPrefix_t& instance) {
  (void)instance;
  member_id_set member_ids;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!move(streamer, instance[0], instance.size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
  return true;
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max__dds_rpc_GuidPrefix_t(T& streamer, const ::dds::rpc::GuidPrefix_t& instance) {
  (void)instance;
  member_id_set member_ids;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!max(streamer, instance[0], instance.size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
  return true;
}

template<>
const propvec &get_type_props<::dds::rpc::EntityId_t>();

namespace {
  static const volatile propvec &properties___dds__rpc__EntityId_t = get_type_props<::dds::rpc::EntityId_t>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::dds::rpc::EntityId_t& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!write(streamer, instance.entityKey()[0], instance.entityKey().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.entityKind()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::dds::rpc::EntityId_t& instance, key_mode key) {
  const auto &props = get_type_props<::dds::rpc::EntityId_t>();
  str.set_mode(cdr_stream::stream_mode::write, key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::dds::rpc::EntityId_t& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!read(streamer, instance.entityKey()[0], instance.entityKey().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.entityKind()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::dds::rpc::EntityId_t& instance, key_mode key) {
  const auto &props = get_type_props<::dds::rpc::EntityId_t>();
  str.set_mode(cdr_stream::stream_mode::read, key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::dds::rpc::EntityId_t& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!move(streamer, instance.entityKey()[0], instance.entityKey().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.entityKind()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::dds::rpc::EntityId_t& instance, key_mode key) {
  const auto &props = get_type_props<::dds::rpc::EntityId_t>();
  str.set_mode(cdr_stream::stream_mode::move, key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::dds::rpc::EntityId_t& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!max(streamer, instance.entityKey()[0], instance.entityKey().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.entityKind()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::dds::rpc::EntityId_t& instance, key_mode key) {
  const auto &props = get_type_props<::dds::rpc::EntityId_t>();
  str.set_mode(cdr_stream::stream_mode::max, key);
  return max(str, instance, props.data()); 
}

template<>
const propvec &get_type_props<::dds::rpc::GUID_t>();

namespace {
  static const volatile propvec &properties___dds__rpc__GUID_t = get_type_props<::dds::rpc::GUID_t>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::dds::rpc::GUID_t& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write__dds_rpc_GuidPrefix_t(streamer, instance.guidPrefix()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.entityId(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::dds::rpc::GUID_t& instance, key_mode key) {
  const auto &props = get_type_props<::dds::rpc::GUID_t>();
  str.set_mode(cdr_stream::stream_mode::write, key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::dds::rpc::GUID_t& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read__dds_rpc_GuidPrefix_t(streamer, instance.guidPrefix()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.entityId(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::dds::rpc::GUID_t& instance, key_mode key) {
  const auto &props = get_type_props<::dds::rpc::GUID_t>();
  str.set_mode(cdr_stream::stream_mode::read, key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::dds::rpc::GUID_t& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move__dds_rpc_GuidPrefix_t(streamer, instance.guidPrefix()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.entityId(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::dds::rpc::GUID_t& instance, key_mode key) {
  const auto &props = get_type_props<::dds::rpc::GUID_t>();
  str.set_mode(cdr_stream::stream_mode::move, key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::dds::rpc::GUID_t& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max__dds_rpc_GuidPrefix_t(streamer, instance.guidPrefix()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.entityId(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::dds::rpc::GUID_t& instance, key_mode key) {
  const auto &props = get_type_props<::dds::rpc::GUID_t>();
  str.set_mode(cdr_stream::stream_mode::max, key);
  return max(str, instance, props.data()); 
}

template<>
const propvec &get_type_props<::dds::rpc::SequenceNumber_t>();

namespace {
  static const volatile propvec &properties___dds__rpc__SequenceNumber_t = get_type_props<::dds::rpc::SequenceNumber_t>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::dds::rpc::SequenceNumber_t& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.high()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.low()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::dds::rpc::SequenceNumber_t& instance, key_mode key) {
  const auto &props = get_type_props<::dds::rpc::SequenceNumber_t>();
  str.set_mode(cdr_stream::stream_mode::write, key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::dds::rpc::SequenceNumber_t& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.high()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.low()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::dds::rpc::SequenceNumber_t& instance, key_mode key) {
  const auto &props = get_type_props<::dds::rpc::SequenceNumber_t>();
  str.set_mode(cdr_stream::stream_mode::read, key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::dds::rpc::SequenceNumber_t& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.high()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.low()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::dds::rpc::SequenceNumber_t& instance, key_mode key) {
  const auto &props = get_type_props<::dds::rpc::SequenceNumber_t>();
  str.set_mode(cdr_stream::stream_mode::move, key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::dds::rpc::SequenceNumber_t& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.high()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.low()))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::dds::rpc::SequenceNumber_t& instance, key_mode key) {
  const auto &props = get_type_props<::dds::rpc::SequenceNumber_t>();
  str.set_mode(cdr_stream::stream_mode::max, key);
  return max(str, instance, props.data()); 
}

template<>
const propvec &get_type_props<::dds::rpc::SampleIdentity>();

namespace {
  static const volatile propvec &properties___dds__rpc__SampleIdentity = get_type_props<::dds::rpc::SampleIdentity>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::dds::rpc::SampleIdentity& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.writer_guid(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.sequence_number(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::dds::rpc::SampleIdentity& instance, key_mode key) {
  const auto &props = get_type_props<::dds::rpc::SampleIdentity>();
  str.set_mode(cdr_stream::stream_mode::write, key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::dds::rpc::SampleIdentity& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.writer_guid(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.sequence_number(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::dds::rpc::SampleIdentity& instance, key_mode key) {
  const auto &props = get_type_props<::dds::rpc::SampleIdentity>();
  str.set_mode(cdr_stream::stream_mode::read, key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::dds::rpc::SampleIdentity& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.writer_guid(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.sequence_number(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::dds::rpc::SampleIdentity& instance, key_mode key) {
  const auto &props = get_type_props<::dds::rpc::SampleIdentity>();
  str.set_mode(cdr_stream::stream_mode::move, key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::dds::rpc::SampleIdentity& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.writer_guid(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.sequence_number(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::dds::rpc::SampleIdentity& instance, key_mode key) {
  const auto &props = get_type_props<::dds::rpc::SampleIdentity>();
  str.set_mode(cdr_stream::stream_mode::max, key);
  return max(str, instance, props.data()); 
}

template<>
const propvec &get_type_props<::dds::rpc::RequestHeader>();

namespace {
  static const volatile propvec &properties___dds__rpc__RequestHeader = get_type_props<::dds::rpc::RequestHeader>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::dds::rpc::RequestHeader& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.requestId(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::dds::rpc::RequestHeader& instance, key_mode key) {
  const auto &props = get_type_props<::dds::rpc::RequestHeader>();
  str.set_mode(cdr_stream::stream_mode::write, key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::dds::rpc::RequestHeader& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.requestId(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::dds::rpc::RequestHeader& instance, key_mode key) {
  const auto &props = get_type_props<::dds::rpc::RequestHeader>();
  str.set_mode(cdr_stream::stream_mode::read, key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::dds::rpc::RequestHeader& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.requestId(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::dds::rpc::RequestHeader& instance, key_mode key) {
  const auto &props = get_type_props<::dds::rpc::RequestHeader>();
  str.set_mode(cdr_stream::stream_mode::move, key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::dds::rpc::RequestHeader& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.requestId(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::dds::rpc::RequestHeader& instance, key_mode key) {
  const auto &props = get_type_props<::dds::rpc::RequestHeader>();
  str.set_mode(cdr_stream::stream_mode::max, key);
  return max(str, instance, props.data()); 
}

template<>
const propvec &get_type_props<::dds::rpc::ReplyHeader>();

namespace {
  static const volatile propvec &properties___dds__rpc__ReplyHeader = get_type_props<::dds::rpc::ReplyHeader>();
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::dds::rpc::ReplyHeader& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.relatedRequestId(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::dds::rpc::ReplyHeader& instance, key_mode key) {
  const auto &props = get_type_props<::dds::rpc::ReplyHeader>();
  str.set_mode(cdr_stream::stream_mode::write, key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::dds::rpc::ReplyHeader& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.relatedRequestId(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::dds::rpc::ReplyHeader& instance, key_mode key) {
  const auto &props = get_type_props<::dds::rpc::ReplyHeader>();
  str.set_mode(cdr_stream::stream_mode::read, key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::dds::rpc::ReplyHeader& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.relatedRequestId(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::dds::rpc::ReplyHeader& instance, key_mode key) {
  const auto &props = get_type_props<::dds::rpc::ReplyHeader>();
  str.set_mode(cdr_stream::stream_mode::move, key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::dds::rpc::ReplyHeader& instance, const entity_properties_t *props) {
  (void)instance;
  member_id_set member_ids;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.relatedRequestId(), prop))
        return false;
      if (!streamer.finish_member(*prop, member_ids))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props, member_ids);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::dds::rpc::ReplyHeader& instance, key_mode key) {
  const auto &props = get_type_props<::dds::rpc::ReplyHeader>();
  str.set_mode(cdr_stream::stream_mode::max, key);
  return max(str, instance, props.data()); 
}

} //namespace cdr
} //namespace core
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

#endif // DDSCXX_RPCCOMMON_HPP_F0D46936B4A4A1F5DD939BC1BAE0AAA2
