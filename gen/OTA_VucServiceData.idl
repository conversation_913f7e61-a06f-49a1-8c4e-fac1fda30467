/*
 * Copyright(c) 2006 to 2020 ZettaScale Technology and others
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License v. 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0, or the Eclipse Distribution License
 * v. 1.0 which is available at
 * http://www.eclipse.org/org/documents/edl-v10.php.
 *
 * SPDX-License-Identifier: EPL-2.0 OR BSD-3-Clause
 */

module seres
{
module ota_vuc_service
{
    const string kVucServiceTopicName = "OtaVucService.topic";
    const uint32 kVucDomainID = 42;

    // 资产信息
    struct ComponentInfo {
        string partNumber;        // 零部件件号
        string softwareVersion;   // 软件版本号
        string supplierCode;      // 供应商代码
        string ecuName;           // ECU名称
        string serialNumber;      // 序号代码
        string hardwareVersion;   // 硬件版本号
        string ecuBatchNumber;    // ECU批次号
        string bootloaderVersion; // bootloader软件版本号
        string backupVersion;     // 备份区软件版本号
    };

    // 资产信息获取结果列表
    struct InventoryInfoList {
        sequence<ComponentInfo> componentLists;     // 资产信息列表
    };

    struct UpgradeTask {
        string upgradePackageInfo;  // TODO
        string upgradeInfo;         // TODO
    };

    enum DownloadFailReason {
        kDiskSpaceNotEnough,    // 磁盘空间不够
        kNetworkAnomaly,        // 网络异常
        kNetworkInterrupt,      // 网络中断
        kIllegalArgs,           // 参数不合法
        kVerifyFailed           // 校验失败
    };

    struct TotalDownloadProgress {
        int32 progress;           // 下载总进度
    };

    // HMI端设置
    enum UseSeamlessUpgrade {
        kNo,
        kYes
    };

    struct SeamlessUpgradeMode {
        UseSeamlessUpgrade use_seamless_upgrade;
    };

    // TODO
    struct NewVersionNotify {
        string history_version;
        string new_version;
    };

    enum UpgradeModeE {
        kImmediately,               // 立即升级
        kAppointment                // 预约升级
    };

    // 版本推送后的升级控制，分为立即升级和预约升级
    struct UpgradeModeCtrl {
        UpgradeModeE upgrade_mode;
        string time;                // 预约时间，TODO
    };

    // 升级倒计时
    struct UpgradeCountdown {
        uint32 time;                // 倒计时时间，unit: min
    };


    enum UpgradeNotifyE {
        kNotUpgrading,              // 用户暂不升级
        kUpgrading                  // 倒计时时间到，开始升级
    };

    // 升级通知
    struct UpgradeNotify {
        UpgradeNotifyE upgrade_notify;
    };

    enum UpgradeFailReason {
        kPowerOnFailure,            // 上电失败
        kOther                      // TODO
    };

    // 升级进度信息
    struct UpgradeProgressItem {
        uint8 progress;             // 进度百分比 0-100 %
        string device_id;            // 设备ID
        UpgradeFailReason fail_reason;    // 错误原因
    };

    // 升级进度列表
    struct UpgradeProgress {
        int32 total_progress;             // 升级总进度
        sequence<UpgradeProgressItem> progress_lists;   // 升级进度列表
    };

    enum VucServiceTopic {
        kReportInventoryInfo,   // 上报资产信息
        kSeamlessUpgradeMode,   // 无感升级模式设置
        kUpgradeTaskNotify,     // 升级任务通知
        kReportDownloadProgress,    // 下载进度上报
        kReportDownloadFailReason,  // 下载错误原因上报
        kUpgradeModeCtrl,           // 升级控制
        kRequestUpgradeCountdown,   // 请求升级倒计时
        kUpgradeNotify,             // 倒计时完成后升级通知
        kReportUpgradeProgress      // 升级进度信息上报
    };

    union VucServiceDataUnion switch (VucServiceTopic) {
        case kReportInventoryInfo:
            InventoryInfoList inventory_info_list;
        case kSeamlessUpgradeMode:
            SeamlessUpgradeMode seamless_upgrade_mode;
        case kUpgradeTaskNotify:
            UpgradeTask upgrade_task;
        case kReportDownloadProgress:
            TotalDownloadProgress downlaod_progress;
        case kReportDownloadFailReason:
            DownloadFailReason download_fail_reason;
        case kUpgradeModeCtrl:
            UpgradeModeCtrl upgrade_mode_ctrl;
        case kRequestUpgradeCountdown:
            UpgradeCountdown upgrade_countdown;
        case kUpgradeNotify:
            UpgradeNotify upgrade_notify;
        case kReportUpgradeProgress:
            UpgradeProgress upgrade_progress;
    };
};
};