#include "rpcCommon.idl"
#include "OTA_DucData.idl"

module seres {
module ota_duc_service {
    // RPC请求返回通用结果
    enum ReturnCode {
        OK,
        ERROR,
        REFUSED
    };

    //Update_agent RPC接口
    interface DucServiceInterface {
        // 资产信息获取
        ReturnCode inventoryCollection(in SelectedInventoryList inventory_list);
        ReturnCode stopInventoryCollection();
        ReturnCode getInventoryResult(out InventoryResult inventory_list);//新增使用topic通信pub/sub的方式获取结果

        // 检查下载条件
        ReturnCode checkDownloadCondition(in DownloadConditionLists conditions, out DownloadConditionResult condition_result);
        // 下载阶段
        ReturnCode startDownload(in DownloadTaskLists task_list);
        ReturnCode downloadCtrl(in DownloadCtrl download_command);
        ReturnCode getDownloadProgress(out DownloadProgress download_progress);//增加DUC主动周期性上报进度，vuc订阅相关话题
        
        //解压
        ReturnCode uzipPackages();
        ReturnCode getuzipPackagesResult(out UzipPackagesResult uzip_Result);

        //升级验签
        ReturnCode startPackagesVerify();
        ReturnCode getPackagesVerifyResult(out PackagesVerifyResult verify_Result);

        // 升级阶段
        ReturnCode checkUpdateCondition();//前置条件检查
        ReturnCode getCheckUpdateConditionResult(out CheckUpdateConditionResult checkcondition_Result);

        ReturnCode startUpdate(in UpdateMode mode,in UpdateDeviceList update_list);
        ReturnCode resumeUpdate();//
        ReturnCode pauseUpdate();//测试接口
        ReturnCode getUpdateProgress(out UpdateProgress update_progress);//增加DUC主动周期性上报进度，vuc订阅相关话题


        // 激活
        ReturnCode activate();
        
        // 回滚阶段
        ReturnCode rollback(in RollbackComponentList component_list);
        ReturnCode getRollbackProgress(out UpdateProgress update_progress);//增加DUC主动周期性上报进度，vuc订阅相关话题

        // 日志上传
        ReturnCode uploadLog();//确认自驾DUC是否能连接外网，如果可以，可以DUC自主上传log
    };

};
};
