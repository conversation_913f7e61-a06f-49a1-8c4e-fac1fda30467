/****************************************************************

  Generated by Eclipse Cyclone DDS IDL to CXX Translator
  File name: /home/<USER>/workspace/fotamaster/gen/OTA_VucServiceData.idl
  Source: OTA_VucServiceData.cpp
  Cyclone DDS: v0.11.0

*****************************************************************/
#include "OTA_VucServiceData.hpp"

#include <org/eclipse/cyclonedds/util/ostream_operators.hpp>

namespace seres
{
namespace ota_vuc_service
{
std::ostream& operator<<(std::ostream& os, ComponentInfo const& rhs)
{
  (void) rhs;
  os << "[";
  os << "partNumber: " << rhs.partNumber();
  os << ", softwareVersion: " << rhs.softwareVersion();
  os << ", supplierCode: " << rhs.supplierCode();
  os << ", ecuName: " << rhs.ecuName();
  os << ", serialNumber: " << rhs.serialNumber();
  os << ", hardwareVersion: " << rhs.hardwareVersion();
  os << ", ecuBatchNumber: " << rhs.ecuBatchNumber();
  os << ", bootloaderVersion: " << rhs.bootloaderVersion();
  os << ", backupVersion: " << rhs.backupVersion();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, InventoryInfoList const& rhs)
{
  (void) rhs;
  os << "[";
  os << "componentLists: " << rhs.componentLists();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, UpgradeTask const& rhs)
{
  (void) rhs;
  os << "[";
  os << "upgradePackageInfo: " << rhs.upgradePackageInfo();
  os << ", upgradeInfo: " << rhs.upgradeInfo();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DownloadFailReason const& rhs)
{
  (void) rhs;
  switch (rhs)
  {
    case DownloadFailReason::kDiskSpaceNotEnough:
      os << "DownloadFailReason::kDiskSpaceNotEnough"; break;
    case DownloadFailReason::kNetworkAnomaly:
      os << "DownloadFailReason::kNetworkAnomaly"; break;
    case DownloadFailReason::kNetworkInterrupt:
      os << "DownloadFailReason::kNetworkInterrupt"; break;
    case DownloadFailReason::kIllegalArgs:
      os << "DownloadFailReason::kIllegalArgs"; break;
    case DownloadFailReason::kVerifyFailed:
      os << "DownloadFailReason::kVerifyFailed"; break;
    default: break;
  }
  return os;
}

std::ostream& operator<<(std::ostream& os, TotalDownloadProgress const& rhs)
{
  (void) rhs;
  os << "[";
  os << "progress: " << rhs.progress();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, UseSeamlessUpgrade const& rhs)
{
  (void) rhs;
  switch (rhs)
  {
    case UseSeamlessUpgrade::kNo:
      os << "UseSeamlessUpgrade::kNo"; break;
    case UseSeamlessUpgrade::kYes:
      os << "UseSeamlessUpgrade::kYes"; break;
    default: break;
  }
  return os;
}

std::ostream& operator<<(std::ostream& os, SeamlessUpgradeMode const& rhs)
{
  (void) rhs;
  os << "[";
  os << "use_seamless_upgrade: " << rhs.use_seamless_upgrade();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, NewVersionNotify const& rhs)
{
  (void) rhs;
  os << "[";
  os << "history_version: " << rhs.history_version();
  os << ", new_version: " << rhs.new_version();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, UpgradeModeE const& rhs)
{
  (void) rhs;
  switch (rhs)
  {
    case UpgradeModeE::kImmediately:
      os << "UpgradeModeE::kImmediately"; break;
    case UpgradeModeE::kAppointment:
      os << "UpgradeModeE::kAppointment"; break;
    default: break;
  }
  return os;
}

std::ostream& operator<<(std::ostream& os, UpgradeModeCtrl const& rhs)
{
  (void) rhs;
  os << "[";
  os << "upgrade_mode: " << rhs.upgrade_mode();
  os << ", time: " << rhs.time();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, UpgradeCountdown const& rhs)
{
  (void) rhs;
  os << "[";
  os << "time: " << rhs.time();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, UpgradeNotifyE const& rhs)
{
  (void) rhs;
  switch (rhs)
  {
    case UpgradeNotifyE::kNotUpgrading:
      os << "UpgradeNotifyE::kNotUpgrading"; break;
    case UpgradeNotifyE::kUpgrading:
      os << "UpgradeNotifyE::kUpgrading"; break;
    default: break;
  }
  return os;
}

std::ostream& operator<<(std::ostream& os, UpgradeNotify const& rhs)
{
  (void) rhs;
  os << "[";
  os << "upgrade_notify: " << rhs.upgrade_notify();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, UpgradeFailReason const& rhs)
{
  (void) rhs;
  switch (rhs)
  {
    case UpgradeFailReason::kPowerOnFailure:
      os << "UpgradeFailReason::kPowerOnFailure"; break;
    case UpgradeFailReason::kOther:
      os << "UpgradeFailReason::kOther"; break;
    default: break;
  }
  return os;
}

std::ostream& operator<<(std::ostream& os, UpgradeProgressItem const& rhs)
{
  (void) rhs;
  os << "[";
  os << "progress: " << rhs.progress();
  os << ", device_id: " << rhs.device_id();
  os << ", fail_reason: " << rhs.fail_reason();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, UpgradeProgress const& rhs)
{
  (void) rhs;
  os << "[";
  os << "total_progress: " << rhs.total_progress();
  os << ", progress_lists: " << rhs.progress_lists();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, VucServiceTopic const& rhs)
{
  (void) rhs;
  switch (rhs)
  {
    case VucServiceTopic::kReportInventoryInfo:
      os << "VucServiceTopic::kReportInventoryInfo"; break;
    case VucServiceTopic::kSeamlessUpgradeMode:
      os << "VucServiceTopic::kSeamlessUpgradeMode"; break;
    case VucServiceTopic::kUpgradeTaskNotify:
      os << "VucServiceTopic::kUpgradeTaskNotify"; break;
    case VucServiceTopic::kReportDownloadProgress:
      os << "VucServiceTopic::kReportDownloadProgress"; break;
    case VucServiceTopic::kReportDownloadFailReason:
      os << "VucServiceTopic::kReportDownloadFailReason"; break;
    case VucServiceTopic::kUpgradeModeCtrl:
      os << "VucServiceTopic::kUpgradeModeCtrl"; break;
    case VucServiceTopic::kRequestUpgradeCountdown:
      os << "VucServiceTopic::kRequestUpgradeCountdown"; break;
    case VucServiceTopic::kUpgradeNotify:
      os << "VucServiceTopic::kUpgradeNotify"; break;
    case VucServiceTopic::kReportUpgradeProgress:
      os << "VucServiceTopic::kReportUpgradeProgress"; break;
    default: break;
  }
  return os;
}

std::ostream& operator<<(std::ostream& os, VucServiceDataUnion const& rhs)
{
  (void) rhs;
  switch (rhs._d()) {
    case ::seres::ota_vuc_service::VucServiceTopic::kReportInventoryInfo:
      os << "inventory_info_list: " << rhs.inventory_info_list(); break;
    case ::seres::ota_vuc_service::VucServiceTopic::kSeamlessUpgradeMode:
      os << "seamless_upgrade_mode: " << rhs.seamless_upgrade_mode(); break;
    case ::seres::ota_vuc_service::VucServiceTopic::kUpgradeTaskNotify:
      os << "upgrade_task: " << rhs.upgrade_task(); break;
    case ::seres::ota_vuc_service::VucServiceTopic::kReportDownloadProgress:
      os << "downlaod_progress: " << rhs.downlaod_progress(); break;
    case ::seres::ota_vuc_service::VucServiceTopic::kReportDownloadFailReason:
      os << "download_fail_reason: " << rhs.download_fail_reason(); break;
    case ::seres::ota_vuc_service::VucServiceTopic::kUpgradeModeCtrl:
      os << "upgrade_mode_ctrl: " << rhs.upgrade_mode_ctrl(); break;
    case ::seres::ota_vuc_service::VucServiceTopic::kRequestUpgradeCountdown:
      os << "upgrade_countdown: " << rhs.upgrade_countdown(); break;
    case ::seres::ota_vuc_service::VucServiceTopic::kUpgradeNotify:
      os << "upgrade_notify: " << rhs.upgrade_notify(); break;
    case ::seres::ota_vuc_service::VucServiceTopic::kReportUpgradeProgress:
      os << "upgrade_progress: " << rhs.upgrade_progress(); break;
  }
  return os;
}

} //namespace ota_vuc_service

} //namespace seres

namespace org{
namespace eclipse{
namespace cyclonedds{
namespace core{
namespace cdr{

template<>
const propvec &get_type_props<::seres::ota_vuc_service::ComponentInfo>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, bit_bound::bb_unset, extensibility::ext_final, false));  //::partNumber
  props.push_back(entity_properties_t(1, 1, false, bit_bound::bb_unset, extensibility::ext_final, false));  //::softwareVersion
  props.push_back(entity_properties_t(1, 2, false, bit_bound::bb_unset, extensibility::ext_final, false));  //::supplierCode
  props.push_back(entity_properties_t(1, 3, false, bit_bound::bb_unset, extensibility::ext_final, false));  //::ecuName
  props.push_back(entity_properties_t(1, 4, false, bit_bound::bb_unset, extensibility::ext_final, false));  //::serialNumber
  props.push_back(entity_properties_t(1, 5, false, bit_bound::bb_unset, extensibility::ext_final, false));  //::hardwareVersion
  props.push_back(entity_properties_t(1, 6, false, bit_bound::bb_unset, extensibility::ext_final, false));  //::ecuBatchNumber
  props.push_back(entity_properties_t(1, 7, false, bit_bound::bb_unset, extensibility::ext_final, false));  //::bootloaderVersion
  props.push_back(entity_properties_t(1, 8, false, bit_bound::bb_unset, extensibility::ext_final, false));  //::backupVersion

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_vuc_service::InventoryInfoList>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, bit_bound::bb_unset, extensibility::ext_final, false));  //::componentLists
  entity_properties_t::append_struct_contents(props, get_type_props<::seres::ota_vuc_service::ComponentInfo>());  //internal contents of ::componentLists

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_vuc_service::UpgradeTask>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, bit_bound::bb_unset, extensibility::ext_final, false));  //::upgradePackageInfo
  props.push_back(entity_properties_t(1, 1, false, bit_bound::bb_unset, extensibility::ext_final, false));  //::upgradeInfo

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
::seres::ota_vuc_service::DownloadFailReason enum_conversion<::seres::ota_vuc_service::DownloadFailReason>(uint32_t in) {
  switch (in) {
    default:
    case 0:
    return ::seres::ota_vuc_service::DownloadFailReason::kDiskSpaceNotEnough;
    break;
    case 1:
    return ::seres::ota_vuc_service::DownloadFailReason::kNetworkAnomaly;
    break;
    case 2:
    return ::seres::ota_vuc_service::DownloadFailReason::kNetworkInterrupt;
    break;
    case 3:
    return ::seres::ota_vuc_service::DownloadFailReason::kIllegalArgs;
    break;
    case 4:
    return ::seres::ota_vuc_service::DownloadFailReason::kVerifyFailed;
    break;
  }
}

template<>
const propvec &get_type_props<::seres::ota_vuc_service::TotalDownloadProgress>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<int32_t>(), extensibility::ext_final, false));  //::progress

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
::seres::ota_vuc_service::UseSeamlessUpgrade enum_conversion<::seres::ota_vuc_service::UseSeamlessUpgrade>(uint32_t in) {
  switch (in) {
    default:
    case 0:
    return ::seres::ota_vuc_service::UseSeamlessUpgrade::kNo;
    break;
    case 1:
    return ::seres::ota_vuc_service::UseSeamlessUpgrade::kYes;
    break;
  }
}

template<>
const propvec &get_type_props<::seres::ota_vuc_service::SeamlessUpgradeMode>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<::seres::ota_vuc_service::UseSeamlessUpgrade>(), extensibility::ext_final, false));  //::use_seamless_upgrade

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_vuc_service::NewVersionNotify>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, bit_bound::bb_unset, extensibility::ext_final, false));  //::history_version
  props.push_back(entity_properties_t(1, 1, false, bit_bound::bb_unset, extensibility::ext_final, false));  //::new_version

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
::seres::ota_vuc_service::UpgradeModeE enum_conversion<::seres::ota_vuc_service::UpgradeModeE>(uint32_t in) {
  switch (in) {
    default:
    case 0:
    return ::seres::ota_vuc_service::UpgradeModeE::kImmediately;
    break;
    case 1:
    return ::seres::ota_vuc_service::UpgradeModeE::kAppointment;
    break;
  }
}

template<>
const propvec &get_type_props<::seres::ota_vuc_service::UpgradeModeCtrl>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<::seres::ota_vuc_service::UpgradeModeE>(), extensibility::ext_final, false));  //::upgrade_mode
  props.push_back(entity_properties_t(1, 1, false, bit_bound::bb_unset, extensibility::ext_final, false));  //::time

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_vuc_service::UpgradeCountdown>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<uint32_t>(), extensibility::ext_final, false));  //::time

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
::seres::ota_vuc_service::UpgradeNotifyE enum_conversion<::seres::ota_vuc_service::UpgradeNotifyE>(uint32_t in) {
  switch (in) {
    default:
    case 0:
    return ::seres::ota_vuc_service::UpgradeNotifyE::kNotUpgrading;
    break;
    case 1:
    return ::seres::ota_vuc_service::UpgradeNotifyE::kUpgrading;
    break;
  }
}

template<>
const propvec &get_type_props<::seres::ota_vuc_service::UpgradeNotify>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<::seres::ota_vuc_service::UpgradeNotifyE>(), extensibility::ext_final, false));  //::upgrade_notify

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
::seres::ota_vuc_service::UpgradeFailReason enum_conversion<::seres::ota_vuc_service::UpgradeFailReason>(uint32_t in) {
  switch (in) {
    default:
    case 0:
    return ::seres::ota_vuc_service::UpgradeFailReason::kPowerOnFailure;
    break;
    case 1:
    return ::seres::ota_vuc_service::UpgradeFailReason::kOther;
    break;
  }
}

template<>
const propvec &get_type_props<::seres::ota_vuc_service::UpgradeProgressItem>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<uint8_t>(), extensibility::ext_final, false));  //::progress
  props.push_back(entity_properties_t(1, 1, false, bit_bound::bb_unset, extensibility::ext_final, false));  //::device_id
  props.push_back(entity_properties_t(1, 2, false, get_bit_bound<::seres::ota_vuc_service::UpgradeFailReason>(), extensibility::ext_final, false));  //::fail_reason

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_vuc_service::UpgradeProgress>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<int32_t>(), extensibility::ext_final, false));  //::total_progress
  props.push_back(entity_properties_t(1, 1, false, bit_bound::bb_unset, extensibility::ext_final, false));  //::progress_lists
  entity_properties_t::append_struct_contents(props, get_type_props<::seres::ota_vuc_service::UpgradeProgressItem>());  //internal contents of ::progress_lists

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
::seres::ota_vuc_service::VucServiceTopic enum_conversion<::seres::ota_vuc_service::VucServiceTopic>(uint32_t in) {
  switch (in) {
    default:
    case 0:
    return ::seres::ota_vuc_service::VucServiceTopic::kReportInventoryInfo;
    break;
    case 1:
    return ::seres::ota_vuc_service::VucServiceTopic::kSeamlessUpgradeMode;
    break;
    case 2:
    return ::seres::ota_vuc_service::VucServiceTopic::kUpgradeTaskNotify;
    break;
    case 3:
    return ::seres::ota_vuc_service::VucServiceTopic::kReportDownloadProgress;
    break;
    case 4:
    return ::seres::ota_vuc_service::VucServiceTopic::kReportDownloadFailReason;
    break;
    case 5:
    return ::seres::ota_vuc_service::VucServiceTopic::kUpgradeModeCtrl;
    break;
    case 6:
    return ::seres::ota_vuc_service::VucServiceTopic::kRequestUpgradeCountdown;
    break;
    case 7:
    return ::seres::ota_vuc_service::VucServiceTopic::kUpgradeNotify;
    break;
    case 8:
    return ::seres::ota_vuc_service::VucServiceTopic::kReportUpgradeProgress;
    break;
  }
}

template<>
const propvec &get_type_props<::seres::ota_vuc_service::VucServiceDataUnion>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

} //namespace cdr
} //namespace core
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

