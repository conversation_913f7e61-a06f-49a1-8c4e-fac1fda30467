/****************************************************************

  Generated by Eclipse Cyclone DDS IDL to CXX Translator
  File name: /home/<USER>/dds/scom-idl/OTA/OTA_DucData.idl
  Source: OTA_DucData.cpp
  Cyclone DDS: v0.11.0

*****************************************************************/
#include "OTA_DucData.hpp"

#include <org/eclipse/cyclonedds/util/ostream_operators.hpp>

namespace seres
{
namespace ota_duc_service
{
std::ostream& operator<<(std::ostream& os, DUCType const& rhs)
{
  (void) rhs;
  switch (rhs)
  {
    case DUCType::CDC:
      os << "DUCType::CDC"; break;
    case DUCType::MDC:
      os << "DUCType::MDC"; break;
    case DUCType::ZCU:
      os << "DUCType::ZCU"; break;
    default: break;
  }
  return os;
}

std::ostream& operator<<(std::ostream& os, SelectedInventoryList const& rhs)
{
  (void) rhs;
  os << "[";
  os << "inventoryLists: " << rhs.inventoryLists();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, InventoryInfo const& rhs)
{
  (void) rhs;
  os << "[";
  os << "partNumber: " << rhs.partNumber();
  os << ", softwareVersion: " << rhs.softwareVersion();
  os << ", supplierCode: " << rhs.supplierCode();
  os << ", ecuName: " << rhs.ecuName();
  os << ", serialNumber: " << rhs.serialNumber();
  os << ", hardwareVersion: " << rhs.hardwareVersion();
  os << ", ecuBatchNumber: " << rhs.ecuBatchNumber();
  os << ", bootloaderVersion: " << rhs.bootloaderVersion();
  os << ", backupVersion: " << rhs.backupVersion();
  os << ", SeamlessModeSupport: " << rhs.SeamlessModeSupport();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, InventoryResult const& rhs)
{
  (void) rhs;
  os << "[";
  os << "InventoryLists: " << rhs.InventoryLists();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DownloadRequirement const& rhs)
{
  (void) rhs;
  os << "[";
  os << "deviceId: " << rhs.deviceId();
  os << ", diskRequirement: " << rhs.diskRequirement();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DownloadConditionLists const& rhs)
{
  (void) rhs;
  os << "[";
  os << "downloadRequirementLists: " << rhs.downloadRequirementLists();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DownloadConditionResult const& rhs)
{
  (void) rhs;
  switch (rhs)
  {
    case DownloadConditionResult::NOEXCEPTION:
      os << "DownloadConditionResult::NOEXCEPTION"; break;
    case DownloadConditionResult::NetworkERROR:
      os << "DownloadConditionResult::NetworkERROR"; break;
    case DownloadConditionResult::DISK_NOT_ENOUGH:
      os << "DownloadConditionResult::DISK_NOT_ENOUGH"; break;
    case DownloadConditionResult::PARAMS_INVALID:
      os << "DownloadConditionResult::PARAMS_INVALID"; break;
    default: break;
  }
  return os;
}

std::ostream& operator<<(std::ostream& os, DownloadTaskInfo const& rhs)
{
  (void) rhs;
  os << "[";
  os << "taskId: " << rhs.taskId();
  os << ", packageVersion: " << rhs.packageVersion();
  os << ", packageName: " << rhs.packageName();
  os << ", packageUrl: " << rhs.packageUrl();
  os << ", packageSize: " << rhs.packageSize();
  os << ", packageMd5: " << rhs.packageMd5();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DownloadTaskLists const& rhs)
{
  (void) rhs;
  os << "[";
  os << "taskLists: " << rhs.taskLists();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DownloadCtrl const& rhs)
{
  (void) rhs;
  switch (rhs)
  {
    case DownloadCtrl::PAUSE:
      os << "DownloadCtrl::PAUSE"; break;
    case DownloadCtrl::RESUME:
      os << "DownloadCtrl::RESUME"; break;
    case DownloadCtrl::CANCEL:
      os << "DownloadCtrl::CANCEL"; break;
    default: break;
  }
  return os;
}

std::ostream& operator<<(std::ostream& os, DownloadStatus const& rhs)
{
  (void) rhs;
  switch (rhs)
  {
    case DownloadStatus::DOWNLOADING:
      os << "DownloadStatus::DOWNLOADING"; break;
    case DownloadStatus::DOWNLOAD_SUCCESS:
      os << "DownloadStatus::DOWNLOAD_SUCCESS"; break;
    case DownloadStatus::DOWNLOAD_FAIL:
      os << "DownloadStatus::DOWNLOAD_FAIL"; break;
    case DownloadStatus::DOWNLOAD_PAUSE:
      os << "DownloadStatus::DOWNLOAD_PAUSE"; break;
    case DownloadStatus::PACKAGE_INCOMPLETE:
      os << "DownloadStatus::PACKAGE_INCOMPLETE"; break;
    default: break;
  }
  return os;
}

std::ostream& operator<<(std::ostream& os, DownloadProgressInfo const& rhs)
{
  (void) rhs;
  os << "[";
  os << "progressPercent: " << rhs.progressPercent();
  os << ", packageName: " << rhs.packageName();
  os << ", downloadedSize: " << rhs.downloadedSize();
  os << ", totalSize: " << rhs.totalSize();
  os << ", status: " << rhs.status();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DownloadProgress const& rhs)
{
  (void) rhs;
  os << "[";
  os << "allFinished: " << rhs.allFinished();
  os << ", progressLists: " << rhs.progressLists();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, UzipPackagesResult const& rhs)
{
  (void) rhs;
  os << "[";
  os << "successed: " << rhs.successed();
  os << ", errorMsg: " << rhs.errorMsg();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, PackagesVerifyResult const& rhs)
{
  (void) rhs;
  os << "[";
  os << "successed: " << rhs.successed();
  os << ", errorMsg: " << rhs.errorMsg();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, UpdateConditionErrorCode const& rhs)
{
  (void) rhs;
  switch (rhs)
  {
    case UpdateConditionErrorCode::NOERROR:
      os << "UpdateConditionErrorCode::NOERROR"; break;
    case UpdateConditionErrorCode::ERROR1:
      os << "UpdateConditionErrorCode::ERROR1"; break;
    case UpdateConditionErrorCode::ERROR2:
      os << "UpdateConditionErrorCode::ERROR2"; break;
    default: break;
  }
  return os;
}

std::ostream& operator<<(std::ostream& os, CheckUpdateConditionResult const& rhs)
{
  (void) rhs;
  os << "[";
  os << "passed: " << rhs.passed();
  os << ", errorCode: " << rhs.errorCode();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, UpdateMode const& rhs)
{
  (void) rhs;
  switch (rhs)
  {
    case UpdateMode::SeamlessMode:
      os << "UpdateMode::SeamlessMode"; break;
    case UpdateMode::FormalMode:
      os << "UpdateMode::FormalMode"; break;
    default: break;
  }
  return os;
}

std::ostream& operator<<(std::ostream& os, UpdateDeviceList const& rhs)
{
  (void) rhs;
  os << "[";
  os << "updateDeviceLists: " << rhs.updateDeviceLists();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, DeviceUpdateStatus const& rhs)
{
  (void) rhs;
  switch (rhs)
  {
    case DeviceUpdateStatus::IDLE:
      os << "DeviceUpdateStatus::IDLE"; break;
    case DeviceUpdateStatus::UPDATING:
      os << "DeviceUpdateStatus::UPDATING"; break;
    case DeviceUpdateStatus::SUCCESS:
      os << "DeviceUpdateStatus::SUCCESS"; break;
    case DeviceUpdateStatus::FAILURE:
      os << "DeviceUpdateStatus::FAILURE"; break;
    default: break;
  }
  return os;
}

std::ostream& operator<<(std::ostream& os, UpdateErrorReason const& rhs)
{
  (void) rhs;
  switch (rhs)
  {
    case UpdateErrorReason::ERROR_REASON_1:
      os << "UpdateErrorReason::ERROR_REASON_1"; break;
    case UpdateErrorReason::ERROR_REASON_2:
      os << "UpdateErrorReason::ERROR_REASON_2"; break;
    case UpdateErrorReason::ERROR_REASON_3:
      os << "UpdateErrorReason::ERROR_REASON_3"; break;
    default: break;
  }
  return os;
}

std::ostream& operator<<(std::ostream& os, DeviceUpdateProgress const& rhs)
{
  (void) rhs;
  os << "[";
  os << "progressPercent: " << rhs.progressPercent();
  os << ", deviceName: " << rhs.deviceName();
  os << ", deviceId: " << rhs.deviceId();
  os << ", status: " << rhs.status();
  os << ", errorReason: " << rhs.errorReason();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, UpdateProgress const& rhs)
{
  (void) rhs;
  os << "[";
  os << "allFinished: " << rhs.allFinished();
  os << ", progressLists: " << rhs.progressLists();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, RollbackComponentList const& rhs)
{
  (void) rhs;
  os << "[";
  os << "rollbackLists: " << rhs.rollbackLists();
  os << "]";
  return os;
}

std::ostream& operator<<(std::ostream& os, OTA_TopicData const& rhs)
{
  (void) rhs;
  switch (rhs)
  {
    case OTA_TopicData::INVENTORY_RESULT:
      os << "OTA_TopicData::INVENTORY_RESULT"; break;
    case OTA_TopicData::DOWNLOAD_PROGRESS:
      os << "OTA_TopicData::DOWNLOAD_PROGRESS"; break;
    case OTA_TopicData::UZIP_PACKAGES_RESULT:
      os << "OTA_TopicData::UZIP_PACKAGES_RESULT"; break;
    case OTA_TopicData::PACKAGES_VERIFY_RESULT:
      os << "OTA_TopicData::PACKAGES_VERIFY_RESULT"; break;
    case OTA_TopicData::CHECK_UPDATE_CONDITION_RESULT:
      os << "OTA_TopicData::CHECK_UPDATE_CONDITION_RESULT"; break;
    case OTA_TopicData::UPDATE_PROGRESS:
      os << "OTA_TopicData::UPDATE_PROGRESS"; break;
    default: break;
  }
  return os;
}

std::ostream& operator<<(std::ostream& os, OTA_DucDataUnion const& rhs)
{
  (void) rhs;
  switch (rhs._d()) {
    case ::seres::ota_duc_service::OTA_TopicData::INVENTORY_RESULT:
      os << "inventoryResult: " << rhs.inventoryResult(); break;
    case ::seres::ota_duc_service::OTA_TopicData::DOWNLOAD_PROGRESS:
      os << "downloadProgress: " << rhs.downloadProgress(); break;
    case ::seres::ota_duc_service::OTA_TopicData::UZIP_PACKAGES_RESULT:
      os << "uzipPackagesResult: " << rhs.uzipPackagesResult(); break;
    case ::seres::ota_duc_service::OTA_TopicData::PACKAGES_VERIFY_RESULT:
      os << "packagesVerifyResult: " << rhs.packagesVerifyResult(); break;
    case ::seres::ota_duc_service::OTA_TopicData::CHECK_UPDATE_CONDITION_RESULT:
      os << "checkUpdateConditionResult: " << rhs.checkUpdateConditionResult(); break;
    case ::seres::ota_duc_service::OTA_TopicData::UPDATE_PROGRESS:
      os << "updateProgress: " << rhs.updateProgress(); break;
  }
  return os;
}

} //namespace ota_duc_service

} //namespace seres

namespace org{
namespace eclipse{
namespace cyclonedds{
namespace core{
namespace cdr{

template<>
::seres::ota_duc_service::DUCType enum_conversion<::seres::ota_duc_service::DUCType>(uint32_t in) {
  switch (in) {
    default:
    case 0:
    return ::seres::ota_duc_service::DUCType::CDC;
    break;
    case 1:
    return ::seres::ota_duc_service::DUCType::MDC;
    break;
    case 2:
    return ::seres::ota_duc_service::DUCType::ZCU;
    break;
  }
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::SelectedInventoryList>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, bit_bound::bb_unset, extensibility::ext_final, false));  //::inventoryLists

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::InventoryInfo>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, bit_bound::bb_unset, extensibility::ext_final, false));  //::partNumber
  props.push_back(entity_properties_t(1, 1, false, bit_bound::bb_unset, extensibility::ext_final, false));  //::softwareVersion
  props.push_back(entity_properties_t(1, 2, false, bit_bound::bb_unset, extensibility::ext_final, false));  //::supplierCode
  props.push_back(entity_properties_t(1, 3, false, bit_bound::bb_unset, extensibility::ext_final, false));  //::ecuName
  props.push_back(entity_properties_t(1, 4, false, bit_bound::bb_unset, extensibility::ext_final, false));  //::serialNumber
  props.push_back(entity_properties_t(1, 5, false, bit_bound::bb_unset, extensibility::ext_final, false));  //::hardwareVersion
  props.push_back(entity_properties_t(1, 6, false, bit_bound::bb_unset, extensibility::ext_final, false));  //::ecuBatchNumber
  props.push_back(entity_properties_t(1, 7, false, bit_bound::bb_unset, extensibility::ext_final, false));  //::bootloaderVersion
  props.push_back(entity_properties_t(1, 8, false, bit_bound::bb_unset, extensibility::ext_final, false));  //::backupVersion
  props.push_back(entity_properties_t(1, 9, false, get_bit_bound<bool>(), extensibility::ext_final, false));  //::SeamlessModeSupport

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::InventoryResult>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, bit_bound::bb_unset, extensibility::ext_final, false));  //::InventoryLists
  entity_properties_t::append_struct_contents(props, get_type_props<::seres::ota_duc_service::InventoryInfo>());  //internal contents of ::InventoryLists

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DownloadRequirement>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, bit_bound::bb_unset, extensibility::ext_final, false));  //::deviceId
  props.push_back(entity_properties_t(1, 1, false, get_bit_bound<uint64_t>(), extensibility::ext_final, false));  //::diskRequirement

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DownloadConditionLists>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, bit_bound::bb_unset, extensibility::ext_final, false));  //::downloadRequirementLists
  entity_properties_t::append_struct_contents(props, get_type_props<::seres::ota_duc_service::DownloadRequirement>());  //internal contents of ::downloadRequirementLists

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
::seres::ota_duc_service::DownloadConditionResult enum_conversion<::seres::ota_duc_service::DownloadConditionResult>(uint32_t in) {
  switch (in) {
    default:
    case 0:
    return ::seres::ota_duc_service::DownloadConditionResult::NOEXCEPTION;
    break;
    case 1:
    return ::seres::ota_duc_service::DownloadConditionResult::NetworkERROR;
    break;
    case 2:
    return ::seres::ota_duc_service::DownloadConditionResult::DISK_NOT_ENOUGH;
    break;
    case 3:
    return ::seres::ota_duc_service::DownloadConditionResult::PARAMS_INVALID;
    break;
  }
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DownloadTaskInfo>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, bit_bound::bb_unset, extensibility::ext_final, false));  //::taskId
  props.push_back(entity_properties_t(1, 1, false, bit_bound::bb_unset, extensibility::ext_final, false));  //::packageVersion
  props.push_back(entity_properties_t(1, 2, false, bit_bound::bb_unset, extensibility::ext_final, false));  //::packageName
  props.push_back(entity_properties_t(1, 3, false, bit_bound::bb_unset, extensibility::ext_final, false));  //::packageUrl
  props.push_back(entity_properties_t(1, 4, false, bit_bound::bb_unset, extensibility::ext_final, false));  //::packageSize
  props.push_back(entity_properties_t(1, 5, false, bit_bound::bb_unset, extensibility::ext_final, false));  //::packageMd5

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DownloadTaskLists>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, bit_bound::bb_unset, extensibility::ext_final, false));  //::taskLists
  entity_properties_t::append_struct_contents(props, get_type_props<::seres::ota_duc_service::DownloadTaskInfo>());  //internal contents of ::taskLists

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
::seres::ota_duc_service::DownloadCtrl enum_conversion<::seres::ota_duc_service::DownloadCtrl>(uint32_t in) {
  switch (in) {
    default:
    case 0:
    return ::seres::ota_duc_service::DownloadCtrl::PAUSE;
    break;
    case 1:
    return ::seres::ota_duc_service::DownloadCtrl::RESUME;
    break;
    case 2:
    return ::seres::ota_duc_service::DownloadCtrl::CANCEL;
    break;
  }
}

template<>
::seres::ota_duc_service::DownloadStatus enum_conversion<::seres::ota_duc_service::DownloadStatus>(uint32_t in) {
  switch (in) {
    default:
    case 0:
    return ::seres::ota_duc_service::DownloadStatus::DOWNLOADING;
    break;
    case 1:
    return ::seres::ota_duc_service::DownloadStatus::DOWNLOAD_SUCCESS;
    break;
    case 2:
    return ::seres::ota_duc_service::DownloadStatus::DOWNLOAD_FAIL;
    break;
    case 3:
    return ::seres::ota_duc_service::DownloadStatus::DOWNLOAD_PAUSE;
    break;
    case 4:
    return ::seres::ota_duc_service::DownloadStatus::PACKAGE_INCOMPLETE;
    break;
  }
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DownloadProgressInfo>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<uint8_t>(), extensibility::ext_final, false));  //::progressPercent
  props.push_back(entity_properties_t(1, 1, false, bit_bound::bb_unset, extensibility::ext_final, false));  //::packageName
  props.push_back(entity_properties_t(1, 2, false, get_bit_bound<uint64_t>(), extensibility::ext_final, false));  //::downloadedSize
  props.push_back(entity_properties_t(1, 3, false, get_bit_bound<uint64_t>(), extensibility::ext_final, false));  //::totalSize
  props.push_back(entity_properties_t(1, 4, false, get_bit_bound<::seres::ota_duc_service::DownloadStatus>(), extensibility::ext_final, false));  //::status

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DownloadProgress>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<bool>(), extensibility::ext_final, false));  //::allFinished
  props.push_back(entity_properties_t(1, 1, false, bit_bound::bb_unset, extensibility::ext_final, false));  //::progressLists
  entity_properties_t::append_struct_contents(props, get_type_props<::seres::ota_duc_service::DownloadProgressInfo>());  //internal contents of ::progressLists

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::UzipPackagesResult>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<bool>(), extensibility::ext_final, false));  //::successed
  props.push_back(entity_properties_t(1, 1, false, bit_bound::bb_unset, extensibility::ext_final, false));  //::errorMsg

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::PackagesVerifyResult>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<bool>(), extensibility::ext_final, false));  //::successed
  props.push_back(entity_properties_t(1, 1, false, bit_bound::bb_unset, extensibility::ext_final, false));  //::errorMsg

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
::seres::ota_duc_service::UpdateConditionErrorCode enum_conversion<::seres::ota_duc_service::UpdateConditionErrorCode>(uint32_t in) {
  switch (in) {
    default:
    case 0:
    return ::seres::ota_duc_service::UpdateConditionErrorCode::NOERROR;
    break;
    case 1:
    return ::seres::ota_duc_service::UpdateConditionErrorCode::ERROR1;
    break;
    case 2:
    return ::seres::ota_duc_service::UpdateConditionErrorCode::ERROR2;
    break;
  }
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::CheckUpdateConditionResult>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<bool>(), extensibility::ext_final, false));  //::passed
  props.push_back(entity_properties_t(1, 1, false, get_bit_bound<::seres::ota_duc_service::UpdateConditionErrorCode>(), extensibility::ext_final, false));  //::errorCode

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
::seres::ota_duc_service::UpdateMode enum_conversion<::seres::ota_duc_service::UpdateMode>(uint32_t in) {
  switch (in) {
    default:
    case 0:
    return ::seres::ota_duc_service::UpdateMode::SeamlessMode;
    break;
    case 1:
    return ::seres::ota_duc_service::UpdateMode::FormalMode;
    break;
  }
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::UpdateDeviceList>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, bit_bound::bb_unset, extensibility::ext_final, false));  //::updateDeviceLists
  entity_properties_t::append_struct_contents(props, get_type_props<::seres::ota_duc_service::InventoryInfo>());  //internal contents of ::updateDeviceLists

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
::seres::ota_duc_service::DeviceUpdateStatus enum_conversion<::seres::ota_duc_service::DeviceUpdateStatus>(uint32_t in) {
  switch (in) {
    default:
    case 0:
    return ::seres::ota_duc_service::DeviceUpdateStatus::IDLE;
    break;
    case 1:
    return ::seres::ota_duc_service::DeviceUpdateStatus::UPDATING;
    break;
    case 2:
    return ::seres::ota_duc_service::DeviceUpdateStatus::SUCCESS;
    break;
    case 3:
    return ::seres::ota_duc_service::DeviceUpdateStatus::FAILURE;
    break;
  }
}

template<>
::seres::ota_duc_service::UpdateErrorReason enum_conversion<::seres::ota_duc_service::UpdateErrorReason>(uint32_t in) {
  switch (in) {
    default:
    case 0:
    return ::seres::ota_duc_service::UpdateErrorReason::ERROR_REASON_1;
    break;
    case 1:
    return ::seres::ota_duc_service::UpdateErrorReason::ERROR_REASON_2;
    break;
    case 2:
    return ::seres::ota_duc_service::UpdateErrorReason::ERROR_REASON_3;
    break;
  }
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::DeviceUpdateProgress>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<uint8_t>(), extensibility::ext_final, false));  //::progressPercent
  props.push_back(entity_properties_t(1, 1, false, bit_bound::bb_unset, extensibility::ext_final, false));  //::deviceName
  props.push_back(entity_properties_t(1, 2, false, bit_bound::bb_unset, extensibility::ext_final, false));  //::deviceId
  props.push_back(entity_properties_t(1, 3, false, get_bit_bound<::seres::ota_duc_service::DeviceUpdateStatus>(), extensibility::ext_final, false));  //::status
  props.push_back(entity_properties_t(1, 4, false, get_bit_bound<::seres::ota_duc_service::UpdateErrorReason>(), extensibility::ext_final, false));  //::errorReason

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::UpdateProgress>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, get_bit_bound<bool>(), extensibility::ext_final, false));  //::allFinished
  props.push_back(entity_properties_t(1, 1, false, bit_bound::bb_unset, extensibility::ext_final, false));  //::progressLists
  entity_properties_t::append_struct_contents(props, get_type_props<::seres::ota_duc_service::DeviceUpdateProgress>());  //internal contents of ::progressLists

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::RollbackComponentList>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root
  props.push_back(entity_properties_t(1, 0, false, bit_bound::bb_unset, extensibility::ext_final, false));  //::rollbackLists
  entity_properties_t::append_struct_contents(props, get_type_props<::seres::ota_duc_service::InventoryInfo>());  //internal contents of ::rollbackLists

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

template<>
::seres::ota_duc_service::OTA_TopicData enum_conversion<::seres::ota_duc_service::OTA_TopicData>(uint32_t in) {
  switch (in) {
    default:
    case 0:
    return ::seres::ota_duc_service::OTA_TopicData::INVENTORY_RESULT;
    break;
    case 1:
    return ::seres::ota_duc_service::OTA_TopicData::DOWNLOAD_PROGRESS;
    break;
    case 2:
    return ::seres::ota_duc_service::OTA_TopicData::UZIP_PACKAGES_RESULT;
    break;
    case 3:
    return ::seres::ota_duc_service::OTA_TopicData::PACKAGES_VERIFY_RESULT;
    break;
    case 4:
    return ::seres::ota_duc_service::OTA_TopicData::CHECK_UPDATE_CONDITION_RESULT;
    break;
    case 5:
    return ::seres::ota_duc_service::OTA_TopicData::UPDATE_PROGRESS;
    break;
  }
}

template<>
const propvec &get_type_props<::seres::ota_duc_service::OTA_DucDataUnion>() {
  static std::mutex mtx;
  static propvec props;
  static std::atomic_bool initialized {false};
  key_endpoint keylist;
  if (initialized.load(std::memory_order_relaxed))
    return props;
  std::lock_guard<std::mutex> lock(mtx);
  if (initialized.load(std::memory_order_relaxed))
    return props;
  props.clear();

  props.push_back(entity_properties_t(0, 0, false, bit_bound::bb_unset, extensibility::ext_final));  //root

  entity_properties_t::finish(props, keylist);
  initialized.store(true, std::memory_order_release);
  return props;
}

} //namespace cdr
} //namespace core
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

